/* ###
 * IP: GHIDRA
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * 
 *      http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include<stdlib.h>
#include<stdio.h>

int main(int argc, char **argv){
    long numerator = strtoul(argv[1],NULL,10);
    long denominator = strtoul(argv[2],NULL,10);
    ldiv_t res = ldiv(numerator,denominator);
    printf("\nquotient: %ld, remainder: %ld\n", res.quot, res.rem);
    return EXIT_SUCCESS;
}





