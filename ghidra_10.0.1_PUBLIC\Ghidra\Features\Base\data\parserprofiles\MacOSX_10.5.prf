libc.h
objc\objc-runtime.h
objc\objc-load.h
objc\objc-auto.h
objc\objc-exception.h
fp.h
aio.h
alloca.h
amber.h
ar.h
asl.h
assert.h
AssertMacros.h
AvailabilityMacros.h
available.h
bitstring.h
bzlib.h
checkint.h
com_err.h
complex.h
copyfile.h
cpio.h
crt_externs.h
ctype.h
curses.h
db.h
des.h
dirent.h
disktab.h
dlfcn.h
dns.h
dns_sd.h
dns_util.h
err.h
errno.h
eti.h
execinfo.h
expat.h
expat_external.h
fastcgi.h
fcgi_config.h
fcgi_stdio.h
fcgiapp.h
fcgimisc.h
fcgios.h
fcntl.h
fenv.h
float.h
fmtmsg.h
fnmatch.h
form.h
fsproperties.h
fstab.h
fts.h
ftw.h
get_compat.h
getopt.h
glob.h
grp.h
gssapi.h
histedit.h
iconv.h
ifaddrs.h
inttypes.h
iodbcext.h
iodbcinst.h
iodbcunix.h
iso646.h
isql.h
isqlext.h
isqltypes.h
krb.h
krb5.h
langinfo.h
launch.h
lber.h
lber_types.h
ldap.h
ldap_cdefs.h
ldap_features.h
ldap_schema.h
ldap_utf8.h
libcharset.h
libgen.h
libproc.h
limits.h
localcharset.h
locale.h
ltdl.h
math.h
membership.h
memory.h
menu.h
monetary.h
monitor.h
mpool.h
nameser.h
nameser8_compat.h
ncurses.h
ncurses_dll.h
ndbm.h
netdb.h
nl_types.h
nlist.h
notify.h
notify_keys.h
NSSystemDirectories.h
ntsid.h
odbcinst.h
panel.h
paths.h
pcap-bpf.h
pcap-namedb.h
pcap.h
poll.h
profile.h
pthread.h
pthread_impl.h
pwd.h
ranlib.h
readpassphrase.h
regex.h
removefile.h
resolv.h
resolv8_compat.h
rune.h
runetype.h
sandbox.h
Saturn.h
sched.h
search.h
semaphore.h
setjmp.h
sgtty.h
signal.h
slapi-plugin.h
spawn.h
sql.h
sqlext.h
sqlite3.h
sqlite3ext.h
sqltypes.h
sqlucode.h
stab.h
standards.h
stdarg.h
stdbool.h
stddef.h
stdint.h
stdio.h
stdlib.h
strhash.h
string.h
stringlist.h
strings.h
struct.h
sysexits.h
syslog.h
tar.h
TargetConditionals.h
tcpd.h
term.h
termcap.h
termios.h
time.h
timeconv.h
ttyent.h
tzfile.h
ucontext.h
ulimit.h
unctrl.h
unistd.h
util.h
utime.h
utmp.h
utmpx.h
varargs.h
vis.h
vproc.h
wchar.h
wctype.h
wordexp.h
xlocale.h
zconf.h
zlib.h

-I<PathToSDK>\MacOSX10.5.sdk\usr\include
-I<PathToSDK>\MacOSX10.5.sdk\usr\X11\include
-I<PathToSDK>\MacOSX10.5.sdk\Developer\Headers\CFMCarbon
-I<PathToSDK>\MacOSX10.5.sdk\usr\lib\gcc\i686-apple-darwin9\4.0.1\include
-D__STRICT_ANSI__
-D__STDC_VERSION__=199900
-DBSD=199103
-D__builtin_va_list="void *"
-D__const=""
-D_INTEGRAL_MAX_BITS=32
-D__m128="long long"
-D__m128i="long long"
-D__m128d="long long"
-D__i386__
-D__GNUC__
-D__APPLE_CPP__
-D__LITTLE_ENDIAN__
-DBYTE_ORDER=LITTLE_ENDIAN
-D__MACH__
-D_MAC_
-DTARGET_API_MAC_OSX
-DTARGET_COCOA
-DHANDLE="unsigned long"
-D_Bool="BOOL"
-D_WCHAR_T
-D_Complex
-Drestrict
-D__restrict
