#This is a .properties file for the script DumpFunctionPatternInfo.java
#it is used to set the data gathered around each function start
Number of first bytes bytes = 16
Number of first instructions instructions = 4
Number of pre bytes bytes = 12
Number of pre instructions instructions = 3
Number of return bytes bytes = 12
Number of return instructions instructions = 3
Directory to save results directory = /local/funcstarts/gcc
#examples of specifying context registers:
#no context registers:
Context register csv csv = null
#TMode and LRset context registers:
#Context register csv csv = TMode,LRset
