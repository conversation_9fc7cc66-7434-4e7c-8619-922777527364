/* ###
 * IP: GHIDRA
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * 
 *      http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
//Writes properties to the tool.
//@category    Examples

import ghidra.app.script.GhidraScript;
import ghidra.framework.options.Options;
import ghidra.framework.plugintool.PluginTool;


public class ToolPropertiesExampleScript extends GhidraScript {

	@Override
	public void run() throws Exception {

		PluginTool tool = state.getTool();

		Options options = tool.getOptions( "name of my script" );

		String fooString = options.getString( "foo", null );

		if ( fooString == null ) {//does not exist in tool options

			fooString = askString( "enter foo", "what value for foo:" );

			if ( fooString != null ) {
				options.setString( "foo", fooString );
			}
		}

		popup( fooString );
	}

}
