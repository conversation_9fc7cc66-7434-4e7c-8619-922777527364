<?xml version="1.0" encoding="UTF-8"?>

<!-- See Relax specification: Ghidra/Framework/SoftwareModeling/data/languages/language_definitions.rxg -->

<language_definitions>
<!-- Uncomment the following to make the language available in Ghidra -->
<!-- 
   <language processor="Skel"
            endian="little"
            size="16"
            variant="default"
            version="1.0"
            slafile="skel.sla"
            processorspec="skel.pspec"
            id="skel:LE:16:default">
    <description>Skeleton Language Module</description>
    <compiler name="default" spec="skel.cspec" id="default"/>
  </language> 
-->
</language_definitions>
