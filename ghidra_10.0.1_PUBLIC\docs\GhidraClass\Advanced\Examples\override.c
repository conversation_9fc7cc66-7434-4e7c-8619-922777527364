/* ###
 * IP: GHIDRA
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * 
 *      http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include<stdio.h>

int a = 1;
long b = 2;
double c = 3.0;
char d[] = "4";


int overrideSignature(int e,int f, int g, int h){
    return printf("a: %d, b: %ld, c: %g, d: %s, e: %d, %d, %d, %d\n", a,b,c,d,e,f,g,h);
}





