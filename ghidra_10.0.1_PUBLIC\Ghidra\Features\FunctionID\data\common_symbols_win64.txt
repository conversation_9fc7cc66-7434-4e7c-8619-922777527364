??$?0D@?$allocator@U_Container_proxy@std@@@std@@QEAA@AEBV?$allocator@D@1@@Z
??$AtlMultiply@_K@ATL@@YAJPEA_K_K1@Z
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
??$_Deallocate@$0BA@$0A@@std@@YAXPEAX_K@Z
??$construct@PEADAEBQEAD@?$_Default_allocator_traits@V?$allocator@D@std@@@std@@SAXAEAV?$allocator@D@1@QEAPEADAEBQEAD@Z
??0?$CHeapPtr@_WVCCRTAllocator@ATL@@@ATL@@QEAA@XZ
??0?$CSimpleStringT@_W$0A@@ATL@@QEAA@PEAUIAtlStringMgr@1@@Z
??0?$CStringT@DV?$StrTraitMFC@DV?$ChTraitsCRT@D@ATL@@@@@ATL@@QEAA@XZ
??0?$CStringT@_WV?$StrTraitMFC@_WV?$ChTraitsCRT@_W@ATL@@@@@ATL@@QEAA@PEB_W@Z
??0?$CStringT@_WV?$StrTraitMFC@_WV?$ChTraitsCRT@_W@ATL@@@@@ATL@@QEAA@XZ
??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@PEBD@Z
??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z
??0AFX_MAINTAIN_STATE2@@QEAA@PEAVAFX_MODULE_STATE@@@Z
??0CObject@@IEAA@XZ
??0CPoint@@QEAA@HH@Z
??0CPreserveLastError@ATL@@QEAA@XZ
??0CRect@@QEAA@HHHH@Z
??0CRect@@QEAA@XZ
??0CSize@@QEAA@HH@Z
??0CTraceFileAndLineInfo@ATL@@QEAA@PEBDH@Z
??0_LocaleUpdate@@QEAA@PEAUlocaleinfo_struct@@@Z
??0_LocaleUpdate@@QEAA@QEAU__crt_locale_pointers@@@Z
??0_Locinfo@std@@QEAA@PEBD@Z
??0_Lockit@std@@QEAA@H@Z
??0_Scoped_lock@_ReentrantPPLLock@details@Concurrency@@QEAA@AEAV123@@Z
??0bad_cast@std@@QEAA@XZ
??0exception@std@@QEAA@AEBQEBD@Z
??0exception@std@@QEAA@AEBV01@@Z
??0invalid_argument@std@@QEAA@PEBD@Z
??1?$CHeapPtr@_WVCCRTAllocator@ATL@@@ATL@@QEAA@XZ
??1?$CStringT@DV?$StrTraitMFC@DV?$ChTraitsCRT@D@ATL@@@@@ATL@@QEAA@XZ
??1?$CStringT@_WV?$StrTraitMFC@_WV?$ChTraitsCRT@_W@ATL@@@@@ATL@@QEAA@XZ
??1?$_String_iterator@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ
??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ
??1AFX_MAINTAIN_STATE2@@QEAA@XZ
??1CGdiObject@@UEAA@XZ
??1CObject@@UEAA@XZ
??1CPreserveLastError@ATL@@QEAA@XZ
??1CWnd@@UEAA@XZ
??1_LocaleUpdate@@QEAA@XZ
??1_Locinfo@std@@QEAA@XZ
??1_Lockit@std@@QEAA@XZ
??1_Scoped_lock@_ReentrantPPLLock@details@Concurrency@@QEAA@XZ
??1exception@std@@UEAA@XZ
??1locale@std@@QEAA@XZ
??2@YAPEAX_K@Z
??2@YAPEAX_KAEAVHeapManager@@H@Z
??2@YAPEAX_KPEAX@Z
??2CObject@@SAPEAX_KPEBDH@Z
??2_Crt_new_delete@std@@SAPEAX_K@Z
??3@YAXPEAX@Z
??3@YAXPEAX_K@Z
??3CObject@@SAXPEAX@Z
??3_Crt_new_delete@std@@SAXPEAX@Z
??4?$CSimpleStringT@D$0A@@ATL@@QEAAAEAV01@AEBV01@@Z
??4?$CSimpleStringT@_W$0A@@ATL@@QEAAAEAV01@AEBV01@@Z
??6CDumpContext@@QEAAAEAV0@PEBD@Z
??B?$CHeapPtrBase@_WVCCRTAllocator@ATL@@@ATL@@QEBAPEA_WXZ
??B?$CSimpleStringT@D$0A@@ATL@@QEBAPEBDXZ
??B?$CSimpleStringT@_W$0A@@ATL@@QEBAPEB_WXZ
??BCRect@@QEAAPEAUtagRECT@@XZ
??BCTraceCategory@ATL@@QEBA_KXZ
??RCTraceFileAndLineInfo@ATL@@QEBAXHIPEBDZZ
??RCTraceFileAndLineInfo@ATL@@QEBAX_KIPEBDZZ
??RCTraceFileAndLineInfo@ATL@@QEBAX_KIPEB_WZZ
??YDName@@QEAAAEAV0@AEBV0@@Z
??_M@YAXPEAX_K1P6AX0@Z@Z
??_U@YAPEAX_K@Z
??_U@YAPEAX_KAEBUnothrow_t@std@@@Z
??_V@YAXPEAX@Z
??_V@YAXPEAX_K@Z
?AfxAssertFailedLine@@YAHPEBDH@Z
?AfxAssertValidObject@@YAXPEBVCObject@@PEBDH@Z
?AfxCrtErrorCheck@@YAHH@Z
?AfxDeactivateActCtx@@YAHK_K@Z
?AfxDynamicDownCast@@YAPEAVCObject@@PEAUCRuntimeClass@@PEAV1@@Z
?AfxFindStringResourceHandle@@YAPEAUHINSTANCE__@@I@Z
?AfxGetModuleState@@YAPEAVAFX_MODULE_STATE@@XZ
?AfxGetStringManager@@YAPEAUIAtlStringMgr@ATL@@XZ
?AfxGetThread@@YAPEAVCWinThread@@XZ
?AfxIsValidAddress@@YAHPEBX_KH@Z
?AfxIsValidString@@YAHPEB_WH@Z
?AfxThrowArchiveException@@YAXHPEBD@Z
?AfxThrowArchiveException@@YAXHPEB_W@Z
?AfxThrowInvalidArgException@@YAXXZ
?AfxThrowMemoryException@@YAXXZ
?AfxThrowOleException@@YAXJ@Z
?Allocate@?$CHeapPtr@_WVCCRTAllocator@ATL@@@ATL@@QEAA_N_K@Z
?Allocate@CCRTAllocator@ATL@@SAPEAX_K@Z
?AtlThrowImpl@ATL@@YAXJ@Z
?Attach@?$CSimpleStringT@D$0A@@ATL@@AEAAXPEAUCStringData@2@@Z
?Attach@?$CSimpleStringT@_W$0A@@ATL@@AEAAXPEAUCStringData@2@@Z
?Attach@CGdiObject@@QEAAHPEAX@Z
?CloneData@?$CSimpleStringT@D$0A@@ATL@@CAPEAUCStringData@2@PEAU32@@Z
?CloneData@?$CSimpleStringT@_W$0A@@ATL@@CAPEAUCStringData@2@PEAU32@@Z
?CopyChars@?$CSimpleStringT@D$0A@@ATL@@SAXPEAD_KPEBDH@Z
?CopyChars@?$CSimpleStringT@_W$0A@@ATL@@SAXPEA_W_KPEB_WH@Z
?Default@CWnd@@IEAA_JXZ
?Delete@CException@@QEAAXXZ
?Empty@?$CSimpleStringT@D$0A@@ATL@@QEAAXXZ
?Empty@?$CSimpleStringT@_W$0A@@ATL@@QEAAXXZ
?FastCurrentContext@SchedulerBase@details@Concurrency@@SAPEAVContextBase@23@XZ
?FillBuffer@CArchive@@QEAAXI@Z
?Flush@CArchive@@QEAAXXZ
?Fork@?$CSimpleStringT@D$0A@@ATL@@AEAAXH@Z
?Fork@?$CSimpleStringT@_W$0A@@ATL@@AEAAXH@Z
?Free@CCRTAllocator@ATL@@SAXPEAX@Z
?FromHandle@CWnd@@SAPEAV1@PEAUHWND__@@@Z
?GetBuffer@?$CSimpleStringT@D$0A@@ATL@@QEAAPEADH@Z
?GetBuffer@?$CSimpleStringT@_W$0A@@ATL@@QEAAPEA_WH@Z
?GetClientRect@CWnd@@QEBAXPEAUtagRECT@@@Z
?GetData@?$CSimpleStringT@D$0A@@ATL@@AEBAPEAUCStringData@2@XZ
?GetData@?$CSimpleStringT@_W$0A@@ATL@@AEBAPEAUCStringData@2@XZ
?GetData@CProcessLocalObject@@QEAAPEAVCNoTrackObject@@P6APEAV2@XZ@Z
?GetData@CThreadLocalObject@@QEAAPEAVCNoTrackObject@@P6APEAV2@XZ@Z
?GetDefaultManager@?$StrTraitMFC@DV?$ChTraitsCRT@D@ATL@@@@SAPEAUIAtlStringMgr@ATL@@XZ
?GetDefaultManager@?$StrTraitMFC@_WV?$ChTraitsCRT@_W@ATL@@@@SAPEAUIAtlStringMgr@ATL@@XZ
?GetGlobalData@@YAPEAUAFX_GLOBAL_DATA@@XZ
?GetHeadPosition@CObList@@QEBAPEAU__POSITION@@XZ
?GetInstance@CMFCVisualManager@@SAPEAV1@XZ
?GetLength@?$CSimpleStringT@D$0A@@ATL@@QEBAHXZ
?GetLength@?$CSimpleStringT@_W$0A@@ATL@@QEBAHXZ
?GetLocaleT@_LocaleUpdate@@QEAAPEAU__crt_locale_pointers@@XZ
?GetNext@CObList@@QEAAAEAPEAVCObject@@AEAPEAU__POSITION@@@Z
?GetParent@CWnd@@QEBAPEAV1@XZ
?GetParentFrame@CWnd@@QEBAPEAVCFrameWnd@@XZ
?GetSafeHwnd@CWnd@@QEBAPEAUHWND__@@XZ
?GetString@?$CSimpleStringT@D$0A@@ATL@@QEBAPEBDXZ
?GetString@?$CSimpleStringT@_W$0A@@ATL@@QEBAPEB_WXZ
?GetStyle@CWnd@@QEBAKXZ
?GetWindowRect@CWnd@@QEBAXPEAUtagRECT@@@Z
?Height@CRect@@QEBAHXZ
?Initialize@AFX_GLOBAL_DATA@@QEAAXXZ
?IsKindOf@CObject@@QEBAHPEBUCRuntimeClass@@@Z
?IsLocked@CStringData@ATL@@QEBA_NXZ
?IsRectEmpty@CRect@@QEBAHXZ
?IsShared@CStringData@ATL@@QEBA_NXZ
?IsStoring@CArchive@@QEBAHXZ
?LoadStringA@?$CStringT@DV?$StrTraitMFC@DV?$ChTraitsCRT@D@ATL@@@@@ATL@@QEAAHPEAUHINSTANCE__@@I@Z
?LoadStringW@?$CStringT@_WV?$StrTraitMFC@_WV?$ChTraitsCRT@_W@ATL@@@@@ATL@@QEAAHPEAUHINSTANCE__@@I@Z
?PrepareWrite2@?$CSimpleStringT@D$0A@@ATL@@AEAAXH@Z
?PrepareWrite2@?$CSimpleStringT@_W$0A@@ATL@@AEAAXH@Z
?Read@CArchive@@QEAAIPEAXI@Z
?Reallocate@?$CSimpleStringT@D$0A@@ATL@@AEAAXH@Z
?Reallocate@?$CSimpleStringT@_W$0A@@ATL@@AEAAXH@Z
?RedrawWindow@CWnd@@QEAAHPEBUtagRECT@@PEAVCRgn@@I@Z
?RegisterCategory@CTrace@ATL@@SAXPEBDI@Z
?RegisterCategory@CTrace@ATL@@SAXPEB_WI@Z
?Release@CStringData@ATL@@QEAAXXZ
?ReleaseBufferSetLength@?$CSimpleStringT@D$0A@@ATL@@QEAAXH@Z
?ReleaseBufferSetLength@?$CSimpleStringT@_W$0A@@ATL@@QEAAXH@Z
?SetLength@?$CSimpleStringT@D$0A@@ATL@@AEAAXH@Z
?SetLength@?$CSimpleStringT@_W$0A@@ATL@@AEAAXH@Z
?SetRectEmpty@CRect@@QEAAXXZ
?SetString@?$CSimpleStringT@D$0A@@ATL@@QEAAXPEBDH@Z
?SetString@?$CSimpleStringT@_W$0A@@ATL@@QEAAXPEB_WH@Z
?ThrowMemoryException@?$CSimpleStringT@D$0A@@ATL@@KAXXZ
?ThrowMemoryException@?$CSimpleStringT@_W$0A@@ATL@@KAXXZ
?TraceV@CTrace@ATL@@CAXPEBDHIIPEB_W@Z
?TraceV@CTrace@ATL@@QEBAXPEBDH_KI0PEAD@Z
?Width@CRect@@QEBAHXZ
?Write@CArchive@@QEAAXPEBXI@Z
?_AfxInitManaged@@YAHXZ
?_AfxRelease@@YAKPEAPEAUIUnknown@@@Z
?_Allocate@std@@YAPEAX_K0_N@Z
?_AtlGetConversionACP@ATL@@YAIXZ
?_AtlGetStringResourceImage@ATL@@YAPEBUATLSTRINGRESOURCEIMAGE@1@PEAUHINSTANCE__@@PEAUHRSRC__@@I@Z
?_ConcRT_CoreAssert@details@Concurrency@@YAXPEBD0H@Z
?_Copy@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@IEAAX_K0@Z
?_Copy@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@V_STL70@@@std@@IEAAX_K0@Z
?_Deallocate@std@@YAXPEAX_K1@Z
?_Eos@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAX_K@Z
?_Facet_Register@std@@YAXPEAV_Facet_base@1@@Z
?_Get_data@?$_String_alloc@U?$_String_base_types@DV?$allocator@D@std@@@std@@@std@@QEAAAEAV?$_String_val@U?$_Simple_types@D@std@@@2@XZ
?_Get_data@?$_String_alloc@U?$_String_base_types@DV?$allocator@D@std@@@std@@@std@@QEBAAEBV?$_String_val@U?$_Simple_types@D@std@@@2@XZ
?_Getal@?$_String_alloc@U?$_String_base_types@DV?$allocator@D@std@@@std@@@std@@QEAAAEAV?$allocator@D@2@XZ
?_Getal@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV?$allocator@D@2@XZ
?_Getgloballocale@locale@std@@CAPEAV_Locimp@12@XZ
?_Ios_base_dtor@ios_base@std@@CAXPEAV12@@Z
?_Mbrtowc@@YAHPEA_WPEBD_KPEAHPEBU_Cvtvec@@@Z
?_Myptr@?$_String_val@U?$_Simple_types@D@std@@@std@@QEAAPEADXZ
?_Myptr@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@IEAAPEADXZ
?_Orphan_all@_Container_base12@std@@QEAAXXZ
?_SpinOnce@?$_SpinWait@$00@details@Concurrency@@QEAA_NXZ
?_Throw_bad_array_new_length@std@@YAXXZ
?_Throw_bad_cast@std@@YAXXZ
?_Tidy@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAX_N_K@Z
?_Xbad_alloc@std@@YAXXZ
?_Xlen@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAXXZ
?_Xlen@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@SAXXZ
?_Xlen@_String_base@std@@SAXXZ
?_Xlen_string@std@@YAXXZ
?_Xlength_error@std@@YAXPEBD@Z
?_Xout_of_range@std@@YAXPEBD@Z
?__global_delete@@YAXPEAX_K@Z
?allocate@?$allocator@D@std@@QEAAPEAD_K@Z
?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z
?assign@?$_Narrow_char_traits@DH@std@@SAXAEADAEBD@Z
?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@AEBV12@_K1@Z
?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@V_STL70@@@std@@QEAAAEAV12@AEBV12@_K1@Z
?assign@?$char_traits@D@std@@SAXAEADAEBD@Z
?clear@ios_base@std@@QEAAXH_N@Z
?copy@?$_Narrow_char_traits@DH@std@@SAPEADQEADQEBD_K@Z
?copy@?$char_traits@D@std@@SAPEADQEADQEBD_K@Z
?data@CStringData@ATL@@QEAAPEAXXZ
?deallocate@?$allocator@D@std@@QEAAXQEAD_K@Z
?g_pfnGetThreadACP@ATL@@3P6AIXZEA
?getloc@ios_base@std@@QEBA?AVlocale@2@XZ
?max_size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KXZ
AtlTraceVA
FastWppTraceMessage
GdiplusShutdown
GetTheFunctionPtr
HRESULT_FROM_WIN32
WmlTrace
_CrtDbgReport
_CrtDbgReportW
_CxxThrowException
_Getctype
_Getcvt
_Init_thread_footer
_Init_thread_header
_Mbrtowc
_RTC_CheckStackVars
_Wcrtomb
__acrt_getptd
__acrt_update_locale_info
__acrt_update_multibyte_info
__guard_check_icall_fptr
__guard_dispatch_icall_fptr
__imp_ActivateActCtx
__imp_CloseHandle
__imp_CoTaskMemFree
__imp_DeleteDC
__imp_EnterCriticalSection
__imp_ExFreePoolWithTag
__imp_FindResourceA
__imp_FindResourceW
__imp_GetClientRect
__imp_GetLastError
__imp_GetParent
__imp_GetProcAddress
__imp_GetWindowRect
__imp_InflateRect
__imp_InitializeCriticalSectionAndSpinCount
__imp_InvalidateRect
__imp_IsRectEmpty
__imp_IsWindow
__imp_LeaveCriticalSection
__imp_LoadResource
__imp_LockResource
__imp_MultiByteToWideChar
__imp_PostQuitMessage
__imp_PtInRect
__imp_RedrawWindow
__imp_SendMessageA
__imp_SendMessageW
__imp_SetLastError
__imp_SetRectEmpty
__imp_SizeofResource
__imp_WideCharToMultiByte
__imp_free
__local_stdio_printf_options
__security_check_cookie
__std_exception_copy
__std_exception_destroy
__stdio_common_vsprintf
__stdio_common_vsprintf_s
__stdio_common_vswprintf_s
__updatetlocinfo
__updatetmbcinfo
__xmlRaiseError
_errno
_free_base
_free_dbg
_getptd
_guard_check_icall
_invalid_parameter
_invalid_parameter_noinfo
_invalid_parameter_noinfo_noreturn
_invoke_watson
_lock
_malloc_dbg
_unlock
_unlock_file
_vswprintf_s_l
atexit
free
log
malloc
memcmp
memcpy
memcpy_s
memmove
memmove_s
memset
sprintf_s
sqrt
strcpy_s
strlen
strnlen
swprintf_s
terminate
wcscpy_s
wcslen
wcsnlen
wmemcpy_s
xmlFree
xmlMalloc
xmlStrEqual
xmlStrdup
