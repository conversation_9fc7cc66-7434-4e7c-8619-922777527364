<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<HTML>
  <HEAD>
    <TITLE>Ghidra Change History</TITLE>
  </HEAD>

<BODY>

<H1 align="center">Ghidra 10.0.1 Change History (July 2021)</H1>
<blockquote><p><u>New Features</u></p>
<ul>
    <li><I>Decompiler</I>. The Decompiler now supports conversion (hex, dec, bin, oct, char) and equate actions directly on constant tokens in the Decompiler window.  To the extent possible, these actions also affect matching scalar operands in the listing. (GP-1053, Issue #21)</li>
</ul>
</blockquote>
<blockquote><p><u>Improvements</u></p>
<ul>
    <li><I>Basic Infrastructure</I>. Ghidra now gracefully fails to launch when its path contains an exclamation point. (GP-1057, Issue #1817)</li>
    <li><I>FileSystems</I>. Can now handle multi-level Ext4 extent nodes when reading a file. (GP-1070)</li>
</ul>
</blockquote>
<blockquote><p><u>Bugs</u></p>
<ul>
    <li><I>Build</I>. No longer building and distributing the Debugger native test binaries. (GP-1080, Issue #3160, #3177)</li>
    <li><I>Debugger</I>. Corrected potential deadlock condition within Debugger which could occur under some circumstances during a breakpoint or while stepping. (GP-1072)</li>
    <li><I>Decompiler</I>. Fixed a bug in the Decompiler causing <code>Overriding symbol with different type size</code> exceptions. (GP-1041)</li>
    <li><I>Exporter</I>. PE and ELF exporters no longer error out when processing non-file-backed relocations. (GP-1091)</li>
    <li><I>FileSystems</I>. Corrected problem mounting Ext4 file systems when the container file is larger than the file system. (GP-1067)</li>
    <li><I>Importer:ELF</I>. Corrected ELF relocation error reporting, including error bookmarks, when relocation handler extension is missing. (GP-1097)</li>
    <li><I>Jython</I>. Added <code>__file__ attribute</code> support in Jython scripts. (GP-1099, Issue #3181)</li>
    <li><I>PDB</I>. Fixed bug that prevented constructor signatures from being created properly. (GP-1086)</li>
    <li><I>PDB</I>. Fixed bug in PDB CLI processing that could kill analysis for binaries imported with older versions of Ghidra. (GP-1104)</li>
    <li><I>Processors</I>. Added ELF Relocation handler for SuperH processors.  Only a few common relocation types have been added. (GP-1090)</li>
    <li><I>Scripting</I>. Fixed a potential NullPointerException that could occur when trying to run a script that doesn't exist. (GP-1074, Issue #2742)</li>
    <li><I>Scripting</I>. Improved graphing of class hierarchy in RecoverClassesFromRTTIScript and the GraphClassesScript to handle duplicate class names, class namespace delimiters, and to make better vertex descriptions. (GP-1095)</li>
    <li><I>Scripting</I>. Fixed a flaw in the RecoverClassesFromRTTIScript that was not using PDB information to create data member names in class data structures. (GP-1101)</li>
</ul>
</blockquote>

<H1 align="center">Ghidra 10.0 Change History (June 2021)</H1>
<blockquote><p><u>New Features</u></p>
<ul>
    <li><I>Debugger</I>. Introduced the Debugger, along with GDB and dbgeng.dll connectors for debugging user-mode applications on Linux and Windows, respectively. The UI includes threads, timeline, modules, memory, registers, watches, etc., for examining and controlling debug targets. See <B>Help -> Contents -> What's New</B> for more details. (GP-986)</li>
    <li><I>Exporter</I>. For programs imported with the PE and ELF loaders, new exporters are available that write back to the original file layout.  Any file-backed bytes that were modified by the user in the program database will be reflected in the written file (except on relocations).  Writing back a modified Memory Map is not supported. (GP-786, Issue #1501, #1505, #19)</li>
    <li><I>Graphing</I>. Added <B>Graph -> Data </B> actions to the Code Browser, allowing visualization of specified pointer relationships in a graph. (GP-194)</li>
    <li><I>Scripting</I>. Added prototype RecoverClassesFromRTTIScript and that uses RTTI information to enhance Ghidra's knowledge of class hierarchy, class member function types (constructors, destructors, deleting destructors, clones) and class member data. The script will label and put member functions into correct class namespace and apply new class structures created either using PDB information, if available, or Decompiler pcode information. (GP-339)</li>
    <li><I>Scripting</I>. Added an example script, LocateMemoryAddressForFileOffset, to demonstrate mapping of a location in the original imported file to the program memory address.  Useful for cases where the original file offset is known; for example, a YARA rule match. (GP-782)</li>
    <li><I>Scripting</I>. Created a script to allow users to search for image base offsets to the current cursor location in 32-bit and 64-bit programs. (GP-863)</li>
</ul>
</blockquote>
<blockquote><p><u>Improvements</u></p>
<ul>
    <li><I>Analysis</I>. Function signatures, including return types and argument data types, are now decoded from CLI Metadata for .NET binaries. (GP-327)</li>
    <li><I>Analysis</I>. Switched #Strings table processing from ASCII to UTF-8 for CIL binaries. (GP-330, Issue #423)</li>
    <li><I>Analysis</I>. Added Constant, Assembly, and AssemblyRef blob processing for CIL binaries. (GP-465)</li>
    <li><I>Analysis</I>. Added the Variadic Function Signature Override analyzer, which identifies functions that take a format string as a parameter and applies the correct signature override at each call site. (GP-516)</li>
    <li><I>Analysis</I>. Added ability to save and easily reuse analysis options in customer-defined configurations. (GP-544, Issue #2182, #312)</li>
    <li><I>Analysis</I>. Ghidra analysis is now aware of more PE/Windows non-returning functions. (GP-733, Issue #2111)</li>
    <li><I>Analysis</I>. ResolveX86orX64LinuxSyscallsScript now properly marks non-returning syscalls. (GP-868, Issue #2761)</li>
    <li><I>API</I>. Revised Structure and Union API, and associated editor, to eliminate the use of the terms Unaligned/Aligned in favor of a packing enablement designation.   Also corrected various change notification issues which may improve archive synchronization and merge behavior. (GP-862, Issue #2681)</li>
    <li><I>API</I>. Renamed <code>Datatype.isDynamicallySized()</code> to <code>DataType.hasLanguageDependantLength()</code> to avoid confusion.  This method is used internally to differentiate between fixed-length types and those whose length is determined by the compiler specification's data organization (e.g., pointers). (GP-932)</li>
    <li><I>Basic Infrastructure</I>. Improved error reporting when trying to launch Ghidra from the git repo without Eclipse having compiled it. (GP-815, Issue #2872)</li>
    <li><I>Build</I>. Command <code><B>gradle -I gradle/support/fetchDependencies.gradle init</B></code> now downloads the Function ID datasets from the ghidra-data GitHub repository so they will be automatically included in development mode and custom builds. (GP-678, Issue #1007)</li>
    <li><I>Build</I>. Performing a <code><B>gradle clean</B></code> no longer deletes downloaded dependencies.  The top-level <B>flatRepo</B> directory has been replaced with the <B>dependencies</B> directory. (GP-811, Issue #1663)</li>
    <li><I>Build</I>. Ghidra now requires Gradle 6.0 or later to build.  Gradle 7.x is now supported. (GP-849, Issue #2949)</li>
    <li><I>Build</I>. Made changes to gradle code to remove warnings. (GP-993, Issue #3039)</li>
    <li><I>Data Types</I>. Added support for hexadecimal byte offset display within composite bitfield view. (GP-910, Issue #2959)</li>
    <li><I>Decompiler</I>. Decompiler analysis now automatically identifies and displays loop variables using standard for-loop syntax.  When a loop variable is discovered, a condition, iteration, and optional initializer statement are displayed at the top of the loop. (GP-565)</li>
    <li><I>Decompiler</I>. Added the <B>Max Instructions per Function</B> Decompiler tool option, specifying the maximum number of instructions the Decompiler will decode in a single function before throwing an exception. Previously, this had been a hard-coded limit. (GP-767, Issue #2557)</li>
    <li><I>Decompiler</I>. The Decompiler now propagates datatypes across signed comparison operations, so constant integer and enum values display correctly. (GP-802, Issue #2565)</li>
    <li><I>Demangler</I>. Updated the GNU Demangler Analyzer options to provide a list of available formats from which to choose. (GP-94, Issue #2214)</li>
    <li><I>Demangler</I>. Updated the GNU Demangler's Namespace-building to improve analysis performance. (GP-706, Issue #2509)</li>
    <li><I>Demangler</I>. Improved Demangler error checking and reporting to give underlying cause of failure. (GP-850)</li>
    <li><I>Documentation</I>. Added basic instructions on how to install, build, and develop Ghidra to README.md. (GP-847)</li>
    <li><I>DWARF</I>. Improved speed and memory usage when importing large DWARF binaries. (GP-419)</li>
    <li><I>DWARF</I>. Added M68000/SVR4 DWARF register mappings. (GP-556, Issue #1610)</li>
    <li><I>DWARF</I>. Improved handling of zero-length structure components during DWARF processing. (GP-851, Issue #2191)</li>
    <li><I>Exporter</I>. Made various improvements and bug fixes and to the IDA Pro exporter. (GP-831, Issue #1897, #2788, #2882, #2891)</li>
    <li><I>FileSystems</I>. Added support for recognizing unencrypted DMG files. (GP-845)</li>
    <li><I>Framework</I>. Added support for program-specific extensions to a compiler specification.  Users can now define their own calling conventions and call-fixups to integrate into decompilation and other analysis (see help for Specification Extensions). (GP-653)</li>
    <li><I>Graphing</I>. Added capability to collapse and expand nodes in the default graph display. (GP-371)</li>
    <li><I>Graphing</I>. Upgraded jungrapht to version 1.1. (GP-377)</li>
    <li><I>Graphing</I>. Refactored graph exporters into a more extensible framework. (GP-440)</li>
    <li><I>Graphing</I>. Graph layout algorithms can now be chosen programmatically. (GP-551)</li>
    <li><I>Graphing</I>. Created additional modified versions of the MinCross layout algorithms, all named to start with Vertical Hierarchical Min-Cross, so that they accept a <B>favoredEdge</B> predicate. When an edge is favored, a pass though the graph layers attempts to align those edges vertically. (GP-625)</li>
    <li><I>Graphing</I>. Added an option to change the background color of the Function Graph window. (GP-760, Issue #1324)</li>
    <li><I>Graphing</I>. Updated Function Graph edge routing when applying the <B>Use Condensed Layout</B> option to reduce edges being clipped by vertices. (GP-768)</li>
    <li><I>Graphing</I>. Added option to disable the lightening of edges in the Function Graph. (GP-769, Issue #1106)</li>
    <li><I>Graphing</I>. Added a distinct visual edge highlight beyond just a different color for graph edge selection. (GP-793, Issue #2953)</li>
    <li><I>Graphing</I>. Added <B>Display as Graph</B> action to the Data Type Manager, allowing visualization of embedded and referenced types of the selected types. (GP-808)</li>
    <li><I>Graphing</I>. Fixed function graph bug that prevented the satellite view from showing the primary view lens.  Fixed a layout bug that allowed some vertices to get clipped when condensing the graph. (GP-940)</li>
    <li><I>Graphing</I>. Added graph API method to set descriptions (tooltips) on vertices and edges. (GP-949)</li>
    <li><I>Graphing</I>. Added Vertex and Edge attributes to GraphML export format. (GP-957, Issue #2958)</li>
    <li><I>GUI</I>. Added new <B>Copy Special</B> actions: <B>Python Byte String</B>, <B>Python List</B>, and <B>C Array</B>. (GP-210, Issue #744)</li>
    <li><I>GUI</I>. Updated the Listing to allow structure members to display Plate Comments. (GP-421, Issue #2091)</li>
    <li><I>GUI</I>. Copy/Pasting and Dragging data types now uses a progress monitor. (GP-422, Issue #2379)</li>
    <li><I>GUI</I>. Added right-click menu <B>Data -> Save Image</B> action to allow user to export embedded graphic resource images. (GP-426)</li>
    <li><I>GUI</I>. Changed Symbol Comment Annotation to use the existing symbol when available.  This allows for the direct navigation of that symbol's address instead of using the search feature of the Go To Service. (GP-675)</li>
    <li><I>GUI</I>. Added the <B>Shift-F10</B> keybinding to allow users to show the popup context menu over the currently focused item.  The  Menu Key can also be used on supporting keyboards. (GP-732, Issue #2790)</li>
    <li><I>GUI</I>. Fixed/Improved the behavior of global menu items and toolbar items with respect to which windows they appear in. These actions can now easily be configured to be either 1) only in menu bar and tool bar of the main window, 2) in the menu bar and tool bar of all windows, or 3) only in the windows that have components that generate the type of context that the action consumes.  Added methods to the ActionBuilder class to support these three options. Also, updated numerous actions to make sure they appear in the appropriate windows. (GP-759)</li>
    <li><I>GUI</I>. Improved overall UI responsiveness when performing analysis with the Symbol Table open. (GP-788)</li>
    <li><I>GUI</I>. Updated the Function Tags table column so that it may be used in most Ghidra tables. (GP-816, Issue #2873)</li>
    <li><I>GUI</I>. Updated the Defined Strings view to reload less frequently during auto-analysis. (GP-835, Issue #2889)</li>
    <li><I>GUI</I>. Updated function hovering in the Decompiler to find the correct function tooltip when multiple functions exist with the same name. (GP-959, Issue #2604)</li>
    <li><I>Importer:ELF</I>. Added markup to ELF import for <code>.note.gnu.build-id</code> and <code>.gnu_debuglink</code> sections. (GP-468)</li>
    <li><I>Importer:ELF</I>. Added ELF import support for SHN_MIPS_TEXT and SHN_MIPS_DATA symbol section index values and provided ability for other processor-specific ELF extensions to resolve ELF symbol memory addresses. (GP-664)</li>
    <li><I>Importer:ELF</I>. Changed various ELF relocations to detect and mark unsupported data relocations which refer to the EXTERNAL block.  Applied EXTERNAL data relocations, which have a non-zero offset from the external symbol, will still be incorrect but will have an error bookmark to flag the condition.  The relocation addend will not be applied in this case to avoid references to a completely irrelevant symbol in the EXTERNAL block. (GP-1029)</li>
    <li><I>Importer:Mach-O</I>. Improved support for Mach-O object files. (GP-700)</li>
    <li><I>Importer:PE</I>. CustomAttrib blobs in CLI/.NET metadata are now decoded. (GP-414)</li>
    <li><I>Importer:PE</I>. Created proper external references for PE Delay Load Imports. (GP-674, Issue #2554, #2623)</li>
    <li><I>Importer:PE</I>. PeLoader can now read and interpret the <code>.pdata</code> section of PE files that include exception handling data. (GP-729)</li>
    <li><I>Importer:PE</I>. Added <B>.exports</B> XML files for the <B>mfc71.dll</B> and <B>mfc71u.dll</B> libraries. Having them allows Ghidra to translate ordinal imports from applications compiled against MFC 7.1 (from Visual Studio .NET 2003) to class and function names with parameters. (GP-1010, Issue #3051)</li>
    <li><I>Listing</I>. Improved Listing view performance, especially noticeable on functions with excessively large stack frames. (GP-268, Issue #109, #2351)</li>
    <li><I>Listing</I>. Added a tool option to hide function auto-comments that appear, trailing a function call in the Listing. (GP-752)</li>
    <li><I>PDB</I>. Improved Ghidra's ability to find and pull PDB files from symbol servers and symbol storage locations. (GP-42)</li>
    <li><I>Processors</I>. Simplified PIC24 return instruction semantics. (GP-647)</li>
    <li><I>Processors</I>. Added support for register alias specification within processor spec (*.pspec).  Added <code>WREG</code> register aliases for PIC24 processor variants. (GP-901, Issue #2956)</li>
    <li><I>Processors</I>. Fixed issue with the <code>PPAGE</code> register not being properly restored after <code>CALL</code> instructions in the HCS12 processor. (GP-920, Issue #1099)</li>
    <li><I>Processors</I>. Fixed HCS12 <code>IDX1</code> addressing with negative immediate values. (GP-937, Issue #3008)</li>
    <li><I>Processors</I>. Fixed V850 multiply-by-immediate calculation that produced an incorrect value when the fifth bit was set. (GP-939, Issue #2970)</li>
    <li><I>References</I>. Improved performance of reference management for special cases when large a number of references from the same address exist (e.g., entry point designation). (GP-696)</li>
    <li><I>Scripting</I>. ExportImageScript now exports all images within a user-selected region to files within a user-selected folder. (GP-231)</li>
    <li><I>Scripting</I>. Improved TableChooserDialog, allowing multiple rows to be processed at once. (GP-676)</li>
    <li><I>Scripting</I>. Updated the TableChooserDialog to allow clients to set the default column sort. (GP-792)</li>
    <li><I>Scripting</I>. Added Python script comment block support. (GP-843, Issue #1484, #2846)</li>
    <li><I>Scripting</I>. Added ApplyClassFunctionSignatureUpdatesScript and ApplyClassFunctionDefinitionUpdatesScript fix-up scripts that can be applied if a user makes changes to a virtual function recovered by the RecoverClassesFromRTTIScript.  Both scripts identify differences between Function Signatures in the Listing and Function Definitions in the Data Type Manager, but the first script fixes all changes to match the signature and the second to match the definition. (GP-973, Issue #3081)</li>
    <li><I>Sleigh</I>. Debug info for Sleigh constructors now includes source file names. (GP-233)</li>
    <li><I>Sleigh</I>. The Sleigh compiler now issues a warning if it generates a temporary varnode which might be large enough to overlap another temporary varnode. (GP-520)</li>
    <li><I>Sleigh</I>. While register names should remain case-sensitive within a Sleigh spec during compilation/parse, register names must not duplicate in a case-insensitive manner since the Program API provides a case-insensitive register lookup by name. The Sleigh Compiler now enforces this. (GP-927)</li>
</ul>
</blockquote>
<blockquote><p><u>Bugs</u></p>
<ul>
    <li><I>Analysis</I>. Fixed how managed code entry points in .NET binaries with CIL entry points are detected and labeled. (GP-319)</li>
    <li><I>Analysis</I>. Can now process implementation-specific data structures for Microsoft CIL compilers. (GP-461)</li>
    <li><I>Analysis</I>. Corrected processing for pointers, function pointers, custom modifiers, ValueTypes, static methods, MethodRefs, MethodDefs, and PInvokes found in .NET mixed binaries. (GP-656)</li>
    <li><I>Analysis</I>. Improved constant analysis speed when processing large binaries with a large amount of code not in defined functions, such as exception handlers. (GP-746, Issue #2509)</li>
    <li><I>Analysis</I>. When OverlayAddressSpace was refactored and Decompiler made aware of it for Ghidra 9.2, the VarnodeContext was not aware of the overlays.  This was fixed and should eliminate the NullPointerException caused when the Symbolic Propagator calls the Varnode constructor. (GP-751, Issue #2785, #2787)</li>
    <li><I>Assembler</I>. Fixed assembler issue with delay-slotted instructions. (GP-587)</li>
    <li><I>Assembler</I>. Fixed assemble <B>Patch Instruction</B> action to work on listings other than the primary static listing. (GP-623)</li>
    <li><I>Assembler</I>. Modified assembler <B>Patch Instruction</B> action to ignore external symbols which produced bad offsets for instructions. (GP-645)</li>
    <li><I>Basic Infrastructure</I>. Fixed an issue with Ghidra and its supporting launch scripts not being able to run correctly on Windows when an ampersand was in the path.  Also fixed an issue with <B>svrAdmin.bat</B> and <B>buildGhidraJar.bat</B> not working if the Ghidra path contained a space. (GP-693, Issue #1726, #1728)</li>
    <li><I>Basic Infrastructure</I>. Corrected <I>"LaunchSupport expected 2 to 4 arguments but got 1" </I> error when starting Ghidra on Windows. (GP-1050, Issue #2176, #3122)</li>
    <li><I>Build</I>. Building of <B>pdb.exe</B> on Windows now works if the path to the Ghidra repository contains a space. (GP-916, Issue #2998)</li>
    <li><I>Build</I>. Corrected GPL DMG module build to properly utilize the jar dependencies included within the repository and distribution. (GP-934)</li>
    <li><I>Build</I>. Corrected an issue with <code><B>gradle prepDev</B></code> when the Ghidra repository is on a different drive than the user's home directory on Windows OS. (GP-970, Issue #3047, #3062)</li>
    <li><I>Build</I>. Fixed a bug that prevented Ghidra from launching in <B>Single Jar Mode</B> when its path contained a space. (GP-1039)</li>
    <li><I>C Parsing</I>. The C-Parser bitfield parsing has been relaxed to allow declared bitfield sizes to exceed the base datatype size.  The effective bitfield size may be clamped based upon the current data organization while preserving the declared size. (GP-558)</li>
    <li><I>Data Types</I>. Fixed a NullPointerException that occurred when trying to edit a function datatype in a datatype archive when there was no open program in the tool. (GP-356, Issue #2407)</li>
    <li><I>Data Types</I>. Corrected the retention of datatype archive search paths, which did not properly remember disabled paths. (GP-639)</li>
    <li><I>Data Types</I>. Fixed potential deadlock encountered when working with the DataTypes tree. (GP-774, Issue #2832)</li>
    <li><I>Decompiler</I>. Fixed endianess issue for joined, two-register returns of <code>longlong</code> values for MIPS 32-bit little endian variants. (GP-513)</li>
    <li><I>Decompiler</I>. The Decompiler no longer emits comments in the middle of conditional expressions. (GP-621, Issue #1670)</li>
    <li><I>Decompiler</I>. Fixed <code>Redefinition of structure...</code> exceptions in the Decompiler caused by a PNG Image and other opaque datatypes. (GP-820, Issue #2734)</li>
    <li><I>Decompiler</I>. Fixed infinite loop in the Decompiler when analyzing return values. (GP-821, Issue #2851)</li>
    <li><I>Decompiler</I>. Fixed bug in the Decompiler's handling of enumerated datatypes causing <code>Shared type id</code> exceptions. (GP-895, Issue #2909)</li>
    <li><I>DWARF</I>. Fixed and consolidated DEX and DWARF implementations of LEB128. (GP-444, Issue #2512)</li>
    <li><I>DWARF</I>. Fixed unnecessary ELF header parsing when DWARF analyzer checks if it needs to run.  Improved DWARF analyzer's run-once logic. (GP-695)</li>
    <li><I>DWARF</I>. Fixed issue with DWARF data type importing that could omit the definition of a structure. (GP-929)</li>
    <li><I>Eclipse Integration</I>. Fixed a GhidraDev bug that prevented Ghidra projects from recognizing extensions installed in the user's <B>~/.ghidra/.ghidra_&lt;version&gt;/Extensions</B> directory. (GP-873)</li>
    <li><I>Extensions</I>. Changed classpath configuration to not contain paths of removed extension libraries. (GP-522, Issue #2637)</li>
    <li><I>FileSystems</I>. Fixed several issues with extracting and importing DYLIB files contained within a DYLD file system. (GP-719, Issue #2934, #682)</li>
    <li><I>FileSystems</I>. Fixed SevenZipFileSystem to correctly fail when opening password-protected archives. (GP-730)</li>
    <li><I>FileSystems</I>. Fixed Ext4 file system to correctly handle sparse files. (GP-871)</li>
    <li><I>Graphing</I>. Fixed IllegalArgumentException when showing a graph popup window after the source component was hidden. (GP-756, Issue #1643)</li>
    <li><I>Graphing</I>. Fixed bug that caused all address in a function graph node to be colored when only the entry point address had a color applied. (GP-757, Issue #1080)</li>
    <li><I>Graphing</I>. Fixed bug in graph dominance algorithm that could cause the <B>Select -> Scoped Flow</B> actions to go into an infinite loop. (GP-776, Issue #2836)</li>
    <li><I>GUI</I>. Fixed UI lock-up issue related to the Function Tags table. (GP-266, Issue #2366)</li>
    <li><I>GUI</I>. Fixed missing spaces in Front End multi-line log messages. (GP-463, Issue #2534)</li>
    <li><I>GUI</I>. Fixed the following modal dialog issues: z-order changing when showing a modal dialog over a detached window; focusing the incorrect window after showing a modal dialog; script progress dialog not getting placed behind input dialog; script dialogs appearing over different windows. (GP-628, Issue #2398, #2480)</li>
    <li><I>GUI</I>. Fixed NullPointerException encountered when creating a new category in the Data Types tree while the tree is filtered. (GP-745, Issue #2799)</li>
    <li><I>GUI</I>. Fixed <B>Right Alt</B> key that did not work for Ghidra actions on some Windows systems. (GP-747, Issue #2008)</li>
    <li><I>GUI</I>. Fixed Function Graph bug that caused some vertex text to get clipped when using wide address format width. (GP-755, Issue #1008)</li>
    <li><I>GUI</I>. Fixed bug in the Listing scroll bar that caused some screen reader software to deadlock. (GP-772, Issue #2820)</li>
    <li><I>GUI</I>. Fixed bug that caused the UI to freeze when clicking in the Program Tree UI.  The bug manifested depending upon the contents of the system clipboard. (GP-775)</li>
    <li><I>GUI</I>. Updated tooltip code to limit data types name length and updated formatting to place pertinent information at the top of the tooltip. (GP-836, Issue #2029)</li>
    <li><I>GUI</I>. Fixed exception triggered when the Bookmarks table failed to remove a deleted symbol. (GP-989, Issue #3066)</li>
    <li><I>GUI</I>. Fixed exception encountered when double-clicking a structure in an archive in the <code><B>closed for edit</B></code> state. (GP-998)</li>
    <li><I>GUI</I>. Fixed Function Graph stack trace encountered when changing the graph's background color option after showing and then closing the graph. (GP-1013, Issue #3058)</li>
    <li><I>Importer:ELF</I>. Added support for additional PIC30 ELF relocations (4, 5, 6) and improved register symbol resolution and markup. (GP-710, Issue #2792)</li>
    <li><I>Importer:ELF</I>. Changed processing of ELF absolute symbols (section ID 0xfff1) to treat them as constants by defining equates instead of memory symbols. (GP-902)</li>
    <li><I>Importer:ELF</I>. Corrected EXTERNAL symbol alignment for PIC24, PIC30, PIC33 during ELF import.  The improperly aligned symbol addresses would cause incorrect external symbol references to appear on instructions (e.g., <code>RCALL</code>). (GP-906)</li>
    <li><I>Importer:PE</I>. Fixed error when importing a PE file with an uninitialized <code>.textbss</code> section. (GP-397, Issue #2496)</li>
    <li><I>Importer:PE</I>. Fixed a bug processing RUNTIME_INFO structures that caused a failure to load PE files under certain conditions when the list is empty. (GP-924, Issue #2995)</li>
    <li><I>Importer:PE</I>. Fixed an issue in the PeLoader that prevented PE files with 0 data directories from being imported. (GP-997, Issue #2858)</li>
    <li><I>Installation</I>. Renamed database <code>db.Record</code> class to <code>db.DBRecord</code> to avoid naming conflict with <code>java.lang.Record</code> class and potential import issues. (GP-193)</li>
    <li><I>Jython</I>. Fixed pasting multi-line strings into the Python interpreter panel. (GP-487, Issue #2456)</li>
    <li><I>Listing</I>. A default thunk function now reflects the <B>namespace</B> of the thunked function similar to the way it reflects its <B>name</B>.  This change also allows thunk functions of a <code>this_call</code> to have the correct <code>this</code> pointer parameter.  Symbol table queries based upon name and/or namespace will always exclude default thunk functions. (GP-17)</li>
    <li><I>Listing</I>. Fixed #US table processing to correctly interpret the string as UTF-16LE for CIL binaries. (GP-318)</li>
    <li><I>Listing</I>. Fixed a sporadic listing operand hover stacktrace bug. (GP-987)</li>
    <li><I>PDB</I>. Escaped more character strings in MSDIA pdb.exe XML output. (GP-578, Issue #1690)</li>
    <li><I>Processors</I>. Fixed various issues pertaining to x86 instruction prefixes. (GP-220, Issue #2286, #2297)</li>
    <li><I>Processors</I>. Refactored PPC interrupt returns to include return pcode statement. (GP-703)</li>
    <li><I>Processors</I>. Fixed issue with ARM <code>VMRS</code> instruction parsing in thumb. (GP-735, Issue #2750)</li>
    <li><I>Processors</I>. Corrected issue with M68000 floating point dynamic k-factor instruction semantics. (GP-736, Issue #2754)</li>
    <li><I>Processors</I>. Fixed instruction semantics for x86 <code>MOVUPS</code> instruction. (GP-744, Issue #2789)</li>
    <li><I>Processors</I>. Simplified SuperH <code>div1</code> instruction.  Corrected several SuperH instructions to set flags properly around the delay slot. (GP-753, Issue #2863, #2864)</li>
    <li><I>Processors</I>. Corrected issue with ARM co-processor registers and the <code>MCR</code> instruction. (GP-761, Issue #2451)</li>
    <li><I>Processors</I>. Fixed issued with x86 <code>INSx.rep</code> and <code>OUTSx.rep</code> pcode ordering. (GP-766, Issue #2829)</li>
    <li><I>Processors</I>. Corrected addresses for PIC24 <code>TBLPAG</code> and <code>PSVPAG</code> registers. (GP-798, Issue #2844, #2855)</li>
    <li><I>Processors</I>. Corrected decoding of some <code>MODR/M</code> opcode bytes in x86. (GP-800, Issue #2504)</li>
    <li><I>Processors</I>. Updated 8085 processor definition to disassemble <code>XRA HL</code> instruction. (GP-818, Issue #2447)</li>
    <li><I>Processors</I>. Corrected missing optional <code>rex.w</code> prefix for x86 conditional jump instructions. (GP-837, Issue #1163)</li>
    <li><I>Processors</I>. Added <code>CALLW</code>, <code>ASRF</code>, <code>LSLF</code>, and <code>LSRF</code> instructions to PIC16 language. (GP-841, Issue #1362)</li>
    <li><I>Processors</I>. Fixed ARM Thumb instructions which update the status flags to now correctly append an <code><B>s</B></code> to the instruction mnemonic. (GP-881)</li>
    <li><I>Processors</I>. Made corrections to <code>wr</code> instruction for SPARC which in some cases did not write to the appropriate ASR register. (GP-928)</li>
    <li><I>Processors</I>. Corrected issue with x86-64 <code>CALL</code> and <code>RET</code> instructions with <code>0x67</code> prefix pushing/popping the wrong address size from the stack. (GP-954, Issue #2976)</li>
    <li><I>Processors</I>. Fixed issue with delay slots modifying some instructions in SuperH processor. (GP-969, Issue #2863)</li>
    <li><I>Processors</I>. Corrected pcode for x86-64 <code>RDMSR</code> instruction. (GP-982, Issue #3046)</li>
    <li><I>Processors</I>. Corrected size of 20-bit signed immediate value in PPC VLE <code>e_li</code> instruction. (GP-1060)</li>
    <li><I>Scripting</I>. Fixed scripting bug where showing a TableChooserDialog while having <code><B>AnalysisMode.DISABLED</B></code> in use caused the dialog to be closed. (GP-1018, Issue #3103)</li>
    <li><I>Sleigh</I>. Fixed multiple errors in x64 vector operation semantics. (GP-799)</li>
</ul>
</blockquote>

<H1 align="center">Ghidra 9.2.4 Change History (April 2021)</H1>
<blockquote><p><u>Improvements</u></p>
<ul>
    <li><I>Basic Infrastructure</I>. Improved support running under JDK 16.  Note that Ghidra still only officially supports JDK 11 LTS. (GP-824, Issue #2879, #2888)</li>
</ul>
</blockquote>
<blockquote><p><u>Bugs</u></p>
<ul>
    <li><I>API</I>. Corrected error condition which could occur if overlay memory block duplicates another memory space name or overlay block name in a case-insensitive manner.  The names are intended to be case-sensitive. (GP-839, Issue #2898)</li>
    <li><I>Demangler</I>. Improved handling of mangled names on thunk functions which were previously left unmangled and could prevent name of underlying thunked function from appearing. (GP-809)</li>
</ul>
</blockquote>

<H1 align="center">Ghidra 9.2.3 Change History (March 2021)</H1>
<blockquote><p><u>Improvements</u></p>
<ul>
    <li><I>Analysis</I>. Added check for vftable entries in <code>.NEP</code> section and relaxed the requirement that the code must have a return. (GP-649)</li>
    <li><I>Analysis</I>. Corrected flaw in RTTI analyzer determination of size of vftables. (GP-688)</li>
    <li><I>Basic Infrastructure</I>. Updated TLS protocol preference to use the most preferred/recent version available to both sides of an SSL connection (e.g., TLSv1.3) instead of forcing use of TLSv1.2. (GP-622)</li>
    <li><I>Build</I>. Corrected build issues which had prevented users from building Ghidra on an Apple M1 (OS X, AARCH64 architecture). (GP-600, Issue #2653)</li>
    <li><I>Demangler</I>. Increased  Gnu Demangler parsing performance by changing some regular expressions. (GP-705)</li>
    <li><I>Eclipse Integration</I>. Updated SleighEditor to support new endian tag on <B>define token</B> definitions. (GP-721)</li>
    <li><I>GUI</I>. Updated the Choose Data Type dialog to apply data types in the same manner as dragging types from the Data Types window.  This provides users more control when choosing how to overwrite existing types. (GP-521)</li>
    <li><I>Importer:ELF</I>. Added support for ELF relocation <code>R_X86_64_IRELATIVE</code>. (GP-651, Issue #1189)</li>
    <li><I>Importer:ELF</I>. Sped up loading of ELF files with large symbol tables. (GP-697)</li>
</ul>
</blockquote>
<blockquote><p><u>Bugs</u></p>
<ul>
    <li><I>Analysis</I>. The RTTI analyzer now runs prior to Reference analysis so that references into vftables are not turned into code or data before the vftables are created. (GP-517)</li>
    <li><I>API</I>. <code>Function.getCalledFunctions(TaskMonitor)</code> and <code>Function.getCallingFunctions(TaskMonitor)</code> now support passing <code>null</code> for the task monitor parameter, which previously would have thrown an exception. (GP-589, Issue #2643)</li>
    <li><I>Data Types</I>. Corrected segmented 32-bit pointer datatype address generation for 16:16 x86 far pointers. (GP-534, Issue #2548)</li>
    <li><I>Decompiler</I>. Fixed Decompiler issue where, when a function name extends beyond the line limit, an end-of-line comment could wrap around to additional lines without including additional <code>//</code> comment indicators. (GP-473)</li>
    <li><I>Decompiler</I>. Corrected an exception that could occur when attempting to edit function signature from the Decompiler. (GP-597, Issue #2601)</li>
    <li><I>Demangler</I>. Changed return type applied to constructors by Demangler from <code>void</code> to <code>Undefined</code>, allowing the Decompiler to determine the type. (GP-790)</li>
    <li><I>DWARF</I>. Improved handling of empty DWARF compile units. (GP-743)</li>
    <li><I>DWARF</I>. Improved handling of DWARF function signatures when parameter info contains unsupported location opcodes or failed to resolve datatypes. (GP-794)</li>
    <li><I>Eclipse Integration</I>. When installing the SleighEditor into Eclipse, the plugin will now show up under the Ghidra category.  Previously the <B>Group Items by Category</B> option had to be turned off before the SleighEditor would appear as a visible entry. (GP-564)</li>
    <li><I>Eclipse Integration</I>. Fixed an issue with Eclipse PyDev breakpoints not catching. (GP-668, Issue #2713)</li>
    <li><I>Eclipse Integration</I>. Fixed an Eclipse GhidraDev exception that occurred when creating a new Ghidra scripting project if a <B>~/ghidra_scripts</B> directory did not exist. (GP-669)</li>
    <li><I>Emulator</I>. Replaced Java floating point emulation to fix multiple rounding issues. (GP-357, Issue #2414)</li>
    <li><I>Graphing</I>. Fixed issue with graph filters not updating satellite view when changing edge filters. (GP-557)</li>
    <li><I>Graphing</I>. Fixed Function Graph keybindings that did not work when docked in the main Code Browser window. (GP-586, Issue #2641)</li>
    <li><I>GUI</I>. Fixed NullPointerException due to using <B>Go To</B> action when there was no open program in the Listing. (GP-66)</li>
    <li><I>GUI</I>. Fixed bug in Reference Code Viewer options that caused an exception. (GP-620, Issue #2672)</li>
    <li><I>Importer</I>. Fixed exception caused when importing previously exported XML data where the bookmark override option was turned off. (GP-667)</li>
    <li><I>Importer:ELF</I>. Fixed a NullPointerException caused by importing an ELF with an uninitialized <code>.got</code> section. (GP-360, Issue #2416)</li>
    <li><I>Importer:ELF</I>. Added Support for ELF <code>R_ARM_MOVW_ABS_NC</code> and <code>R_ARM_MOVT_ABS ELF</code> Relocations for ARM. (GP-555, Issue #2510)</li>
    <li><I>Importer:ELF</I>. Corrected ELF processing of <code>.init_array</code> and <code>.fini_array</code> which was incorrectly overadjusting entries for an image base change. (GP-699)</li>
    <li><I>Importer:Mach-O</I>. Corrected Mach-O fat-binary library import issue and resolved error related to unnamed Mach-O segment. (GP-652, Issue #2702)</li>
    <li><I>Importer:Mach-O</I>. Fixed an issue with DYLD Load Command data structures being created in the wrong locations. (GP-689, Issue #2624)</li>
    <li><I>Importer:Mach-O</I>. Fixed an exception that occurred when importing Mach-O files that define zero <code>LC_BUILD_VERSION</code> tool entries. (GP-702, Issue #2192)</li>
    <li><I>PDB</I>. Fixed createPdbXmlFiles.bat to permit spaces in the path name of Ghidra installation folder and the batch argument name. (GP-575, Issue #2167)</li>
    <li><I>PDB</I>. Fixed PDB Universal analyzer to set the run-once flag when finished. (GP-724)</li>
    <li><I>PDB</I>. Changed return type applied to constructors by PDB Universal from <code>void</code> to <code>Undefined</code>, allowing the Decompiler to determine the type. (GP-791)</li>
    <li><I>Processors</I>. Added missing <code>RFE</code> instruction in MIPS up to version R3000. (GP-33, Issue #1766)</li>
    <li><I>Processors</I>. ARM instruction <code>VMUL</code> now decodes correctly. (GP-627, Issue #2677)</li>
    <li><I>Processors</I>. Added missing <code>CFINV</code> instruction to AARCH64 processor specification and added definitions for locals in neon instructions. (GP-655, Issue #2710)</li>
    <li><I>Scripting</I>. Fixed analyzeHeadless <code><B>-scriptPath</B></code> option that didn't work for Python and other non-Java scripts located in non-default directories. (GP-528, Issue #2561)</li>
    <li><I>Scripting</I>. Fixed concurrency issue with management of scripting bundle paths. (GP-576)</li>
    <li><I>Scripting</I>. Corrected handling for Ghidra Script files which are symlinks that were broken in Ghidra 9.2. (GP-650, Issue #2698)</li>
    <li><I>Scripting</I>. Fixed the analyzeHeadless <code><B>-scriptPath</B></code> option to correctly parse <code><B>$GHIDRA_HOME</B></code> and <code><B>$USER_HOME</B></code>. (GP-781)</li>
</ul>
</blockquote>

<H1 align="center">Ghidra 9.2.2 Change History (December 2020)</H1>
<blockquote><p><u>Bugs</u></p>
<ul>
    <li><I>Graphing</I>. Fixed issue with Graph filters not working and satellite view sometimes not matching graph. (GP-526)</li>
    <li><I>Importer:Mach-O</I>. Mach-O DYLD cache incorrect offset use has been fixed. (GP-550, Issue #2560)</li>
    <li><I>Listing</I>. Fixed issue where <B>Edit Label</B> action (L key) did not work on primary function symbols. (GP-537)</li>
    <li><I>Multi-User</I>. Corrected Ghidra Server build issue for version 9.2.1 which had an improperly generated <B>classpath.frag</B> file.  Issue caused server to fail startup with a ClassNotFoundException. (GP-542)</li>
    <li><I>Processors</I>. The V850 <code>JMP</code> instruction has been corrected not to use the PC in the address calculation. (GP-548, Issue #2570)</li>
    <li><I>Processors</I>. Removed erroneous VST4 variant, most likely from a copy/paste error.  This fixes the ARM Thumb BL instruction disassembly with a negative offset. (GP-549, Issue #2559)</li>
</ul>
</blockquote>

<H1 align="center">Ghidra 9.2.1 Change History (December 2020)</H1>
<blockquote><p><u>Improvements</u></p>
<ul>
    <li><I>Analysis</I>. Updated RTTI analyzer to find <code>type_info</code> vftable when it cannot be found with its mangled name. This will enable many more Windows programs to have their RTTI structures created that were unable to be parsed in previous Ghidra versions. (GP-141)</li>
    <li><I>API</I>. Relaxed memory block naming restrictions and restored ability to have spaces in memory block names.  However, if a memory block is flagged as an overlay, the associated overlay space name may be modified to ensure validity and uniqueness.  The DuplicateNameException has been removed from all memory block API methods since this was entirely an overlay space concern.  Memory block GUI has also been changed eliminate the duplicate block name restriction. (GP-420, Issue #2465)</li>
    <li><I>Build</I>. Eliminated the need for installation of <B>bison</B> and <B>flex</B> when performing source-based <B>gradle</B> build of Ghidra or the Decompiler module.  The generated files are now included with source files and maintained in source control.  A separate <code><B>gradle Decompiler:generateParsers</B></code> task, which still requires <B>bison</B> and <B>flex</B>, must be used, explicitly, when changes are made to lex/yacc source files. (GP-467)</li>
    <li><I>Graphing</I>. Fixed issue with exporting graphs to DOT format due to invalid vertex IDs. (GP-280)</li>
    <li><I>Graphing</I>. Improved graphing where it did not navigate when clicking on external function nodes. Now it will navigate to the <B>fake</B> function location in the program, which is the location of the pointer to the external function. (GP-493)</li>
    <li><I>Listing:Symbols</I>. Removed restriction for naming labels that resemble default label names. (GT-3185, Issue #1057)</li>
    <li><I>PDB</I>. Crafted PDB type ID records <code>0x1608</code> and <code>0x1609</code> with presumed <B>class</B> and <B>struct</B> types and follow-on application of these types. Also fixed up some fall-back data type logic and improved some warning messages to reflect the <B>cause</B> of the conditions. (GP-474, Issue #2523)</li>
    <li><I>Scripting</I>. Removed unnecessary 1-second delay when launching a script. (GP-443)</li>
</ul>
</blockquote>
<blockquote><p><u>Bugs</u></p>
<ul>
    <li><I>Analysis</I>. Fixed the processing of CIL metadata that express arrays of non-primitive types. (GP-331)</li>
    <li><I>API</I>. WrappedMemBuffer methods <code>getInt</code>, <code>getShort</code>, <code>getLong</code>, and <code>getBigInteger</code> have been fixed when allocated at a non-zero offset, wrapping another MemBuffer such as DumbMemBufferImpl. (GP-486)</li>
    <li><I>Decompiler</I>. Fixed issue with the Auto Create/Fill Structure command that caused it to silently miss some pointer accesses. (GP-344)</li>
    <li><I>Decompiler</I>. Jump table recovery now takes into account encoded bits, like ARM/THUMB mode transition, that may be present in address tables. (GP-387, Issue #2420)</li>
    <li><I>Decompiler</I>. Fixed a bug in the Decompiler <B>renaming</B> action when applied to function references. (GP-477, Issue #2415)</li>
    <li><I>Decompiler</I>. Corrected 8-byte return value storage specification in compiler-spec affecting <code>longlong</code> and <code>double</code> return values.  Endianess ordering of <code>r0</code>/<code>r1</code> was incorrect. (GP-512, Issue #2547)</li>
    <li><I>Graphing</I>. Fixed the Function Graph's <B>drag-to-select-nodes</B> feature. (GP-430)</li>
    <li><I>Graphing</I>. Fixed issue where the graph in the satellite view is sometimes truncated. (GP-469)</li>
    <li><I>Graphing</I>. Fixed a stack trace issue caused by reusing a graph display window to show a graph that is larger than is allowed. (GP-492)</li>
    <li><I>Graphing</I>. Fixed issue where graph satellite view did not reflect main graph when graph vertices are hidden using hide actions or filters. (GP-514)</li>
    <li><I>GUI</I>. Fixed stack overflow in TableChooserDialogs. (GP-460, Issue #2536)</li>
    <li><I>PDB</I>. Corrected PDB parser selection bug affecting PDB load/download on Windows. (GP-390)</li>
    <li><I>Processors</I>. Fixed handling of certain ARM/THUMB switch calculation functions. (GP-389)</li>
</ul>
</blockquote>

<H1 align="center">Ghidra 9.2 Change History (November 2020)</H1>
<blockquote><p><u>New Features</u></p>
<ul>
    <li><I>Graphing</I>. A new graph service and implementation was created. The graph service provides basic graphing capabilities. It was also used to generate several different types of graphs including code block graphs, call graphs, and AST graphs. In addition, an export graph service was created that supports various formats. (GP-211)</li>
    <li><I>PDB</I>. Added a new, prototype, platform-independent PDB analyzer that processes and applies data types and symbols to a program from a raw (non-XML-converted) PDB file, allowing users to more easily take advantage of PDB information. (GT-3112)</li>
    <li><I>Processors</I>. Added M8C SLEIGH processor specification. (GT-3052)</li>
    <li><I>Processors</I>. Added support for the RISC-V processor. (GT-3389, Issue #932)</li>
    <li><I>Processors</I>. Added support for the Motorola 6809 processor. (GT-3390, Issue #1201)</li>
    <li><I>Processors</I>. Added CP1600-series processor support. (GT-3426, Issue #1383)</li>
    <li><I>Processors</I>. Added V850 processor module. (GT-3523, Issue #1430)</li>
</ul>
</blockquote>
<blockquote><p><u>Improvements</u></p>
<ul>
    <li><I>Analysis</I>. Increased the speed of the Embedded Media Analyzer, which was especially poor for large programs, by doing better checking and reducing the number of passes over the program. (GT-3258)</li>
    <li><I>Analysis</I>. Improved the performance of the RTTI analyzer. (GT-3341, Issue #10)</li>
    <li><I>Analysis</I>. The handling of Exception records found in GCC-compiled binaries has been sped up dramatically.  In addition, incorrect code disassembly has been corrected. (GT-3374)</li>
    <li><I>Analysis</I>. Updated Auto-analysis to preserve work when encountering recoverable exceptions. (GT-3599)</li>
    <li><I>Analysis</I>. Improved efficiency when creating or checking for functions and namespaces which overlap. (GP-21)</li>
    <li><I>Analysis</I>. Added partial support of Clang for Windows. (GP-64)</li>
    <li><I>Analysis</I>. RTTI structure processing speed has been improved with a faster technique for finding the root RTTI type descriptor. (GP-168, Issue #2075)</li>
    <li><I>API</I>. The performance of adding large numbers of data types to the same category has been improved. (GT-3535)</li>
    <li><I>API</I>. Added the BigIntegerNumberInputDialog that allows users to enter integer values larger than <code>Integer.MAX_VALUE</code> (2147483647). (GT-3607)</li>
    <li><I>API</I>. Made JSON more available using GSON. (GP-89, Issue #1982)</li>
    <li><I>Basic Infrastructure</I>. Introduced an extension point <code>priority</code> annotation so users can control extension point ordering. (GT-3350, Issue #1260)</li>
    <li><I>Basic Infrastructure</I>. Changed file names in <B>launch.bat</B> to always run executables from System32. (GT-3614, Issue #1599)</li>
    <li><I>Basic Infrastructure</I>. Unknown platforms now default to 64-bit. (GT-3615, Issue #1499)</li>
    <li><I>Basic Infrastructure</I>. Updated sevenzipjbinding library to version 16.02-2.01. (GP-254)</li>
    <li><I>Build</I>. Ghidra's native Windows binaries can now be built using Visual Studio 2019. (GT-3277, Issue #999)</li>
    <li><I>Build</I>. Extension builds now exclude gradlew artifacts from zip file. (GT-3631, Issue #1763)</li>
    <li><I>Build</I>. Reduced the number of duplicated help files among the build jar files. (GP-57, Issue #2144)</li>
    <li><I>Build</I>. Git commit hash has been added to <B>application.properties</B> file for every build (not just releases). (GP-67)</li>
    <li><I>Contrib</I>. Extensions are now installed to the user's settings directory, not the Ghidra installation directory. (GT-3639, Issue #1960)</li>
    <li><I>Data Types</I>. Added mutability data settings (constant, volatile) for Enum datatype. (GT-3415)</li>
    <li><I>Data Types</I>. Improved Structure Editor's <B>Edit Component</B> action to work on array pointers. (GP-205, Issue #1633)</li>
    <li><I>Decompiler</I>. Added Secondary Highlights to the Decompiler.  This feature allows the user to create a highlight for a token to show all occurrences of that token.  Further, multiple secondary highlights are allowed at the same time, each using a unique color.  See the Decompiler help for more information. (GT-3292, Issue #784)</li>
    <li><I>Decompiler</I>. Added heuristics to the Decompiler to better distinguish whether a constant pointer refers to something in the CODE or DATA address space, for Harvard architectures. (GT-3468)</li>
    <li><I>Decompiler</I>. Improved Decompiler analysis of local variables with small data types, eliminating unnecessary casts and mask operations. (GT-3525)</li>
    <li><I>Decompiler</I>. Documentation for the Decompiler, accessible from within the Code Browser, has been rewritten and extended. (GP-166)</li>
    <li><I>Decompiler</I>. The Decompiler can now display the namespace path (or part of it) of symbols it renders.  With the default display configuration, the minimal number of path elements necessary are printed to fully resolve the symbol within the current scope. (GP-236)</li>
    <li><I>Decompiler</I>. The Decompiler now respects the <B>Charset</B> and <B>Translate</B> settings for string literals it displays. (GP-237)</li>
    <li><I>Decompiler</I>. The Decompiler's analysis of array accesses is much improved. It can detect more and varied access patterns produced by optimized code, even if the base offset is not contained in the array.  Multi-dimensional arrays are detected as well. (GP-238, Issue #461, #1348)</li>
    <li><I>Decompiler</I>. Extended the Decompiler's support for analyzing class methods.  The class data type is propagated through the <B>this</B> pointer even in cases where the full prototype of the method is not known.  The methods <code>isThisPointer()</code> and <code>isHiddenReturn()</code> are now populated in HighSymbol objects and are accessible in Ghidra scripts. (GP-239, Issue #2151)</li>
    <li><I>Decompiler</I>. The Decompiler will now infer a string pointer from a constant that addresses the interior of a string, not just the beginning. (GP-240, Issue #1502)</li>
    <li><I>Decompiler</I>. The Decompiler now always prints the full precision of floating-point values, using the minimal number of characters in either fixed point or scientific notation. (GP-241, Issue #778)</li>
    <li><I>Decompiler</I>. The Decompiler's <B>Auto Create Structure</B> command now incorporates into new structures data-type information from function prototypes.  The <B>Auto Fill in Structure</B> variant of the command will override <B>undefined</B> and other more general data-types with discovered data-types if they are more specific. (GP-242)</li>
    <li><I>Demangler</I>. Modified Microsoft Demangler (MDMang) to handle symbols represented by MD5 hash codes when their normal mangled length exceeds 4096. (GT-3409, Issue #1344)</li>
    <li><I>Demangler</I>. Upgraded the GNU Demangler to version 2.33.1.  Added support for the now-deprecated GNU Demangler version 2.24 to be used as a fallback option for demangling. (GT-3481, Issue #1195, #1308, #1451, #1454)</li>
    <li><I>Demangler</I>. The Demangler now more carefully applies information if generic changes have been made.  Previously if the function signature had changed in any way from default, the demangler would not attempt to apply any information including the function name. (GP-12)</li>
    <li><I>Demangler</I>. Changed MDMang so cast operator names are complete within the qualified function name, effecting what is available from internal API. (GP-13)</li>
    <li><I>Demangler</I>. Added additional MDMang Extended Types such as <code>char8_t</code>, <code>char16_t</code>, and <code>char32_t</code>. (GP-14)</li>
    <li><I>Documentation</I>. Removed Eclipse BuildShip instructions from the DevGuide. (GT-3634, Issue #1735)</li>
    <li><I>FID</I>. Regenerated FunctionID databases. Added support for Visual Studio versions 2017 and 2019. (GP-170)</li>
    <li><I>Function Diff</I>. Users may now add functions ad-hoc to existing function comparison panels. (GT-2229)</li>
    <li><I>Function Graph</I>. Added <B>Navigation History</B> Tool option for Function Graph to signal it to produce fewer navigation history entries. (GT-3233, Issue #1115)</li>
    <li><I>GUI</I>. Users can now view the Function Tag window to see all functions associated with a tag, without having to inspect the Listing. (GT-3054)</li>
    <li><I>GUI</I>. Updated the <B>Copy Special</B> action to work on the current address when there is no selection. (GT-3155, Issue #1000)</li>
    <li><I>GUI</I>. Significantly improved the performance of filtering trees in the Ghidra GUI. (GT-3225)</li>
    <li><I>GUI</I>. Added many optimizations to increase the speed of table sorting and filtering. (GT-3226, Issue #500)</li>
    <li><I>GUI</I>. Improved performance of bit view component recently introduced to Structure Editor. (GT-3244, Issue #1141)</li>
    <li><I>GUI</I>. Updated usage of timestamps in the UI to be consistent. (GT-3286)</li>
    <li><I>GUI</I>. Added tool actions for navigating to the next/previous functions in the navigation history. (GT-3291, Issue #475)</li>
    <li><I>GUI</I>. Filtering now works on all tables in the Function Tag window. (GT-3329)</li>
    <li><I>GUI</I>. Updated the Ghidra File Chooser so that users can type text into the list and table views in order to quickly jump to a desired file. (GT-3396)</li>
    <li><I>GUI</I>. Improved the performance of the Defined Strings table. (GT-3414, Issue #1259)</li>
    <li><I>GUI</I>. Updated Ghidra to allow users to set a key binding to perform an equivalent operation to double-clicking the <code>XREF</code> field in the Listing.  See the <B>Show Xrefs</B> action in the <B>Tool Options... Key Bindings</B> section. (GT-3446)</li>
    <li><I>GUI</I>. Improved mouse wheel scrolling in Listing and Byte Viewers. (GT-3473)</li>
    <li><I>GUI</I>. Ghidra's action context mechanism was changed so that actions that modify the program are not accidentally invoked in the wrong context, thus possibly modifying the program in ways the user did not want or without the user knowing that it happened.  This also fixed an issue where the navigation history drop-down menu did not represent the locations that would be used if the next/previous buttons were pressed. (GT-3485)</li>
    <li><I>GUI</I>. Updated Ghidra tables to defer updating while analysis is running. (GT-3604)</li>
    <li><I>GUI</I>. Updated Font Size options to allow the user to set any font size. (GT-3606, Issue #160, #1541)</li>
    <li><I>GUI</I>. Added ability to overlay text on an icon. (GP-41)</li>
    <li><I>GUI</I>. Updated Ghidra options to allow users to clear default key binding values. (GP-61, Issue #1681)</li>
    <li><I>GUI</I>. ToggleDirectionAction button now shows in snapshot windows. (GP-93)</li>
    <li><I>GUI</I>. Added a new action to the Symbol Tree to allow users to convert a Namespace to a Class. (GP-225, Issue #2301)</li>
    <li><I>Importer</I>. Updated the XML Loader to parse symbol names for namespaces. (GT-3293)</li>
    <li><I>Importer:ELF</I>. Added support for processing Android packed ELF Relocation Tables. (GT-3320, Issue #1192)</li>
    <li><I>Importer:ELF</I>. Added ELF import opinion for ARM BE8. (GT-3642, Issue #1187)</li>
    <li><I>Importer:ELF</I>. Added support for ELF RELR relocations, such as those produced for Android. (GP-348)</li>
    <li><I>Importer:Mach-O</I>. DYLD Loader can now load x86_64 DYLD from macOS. (GT-3611, Issue #1566)</li>
    <li><I>Importer:PE</I>. Improved parsing of Microsoft ordinal map files produced with <code>DUMPBIN /EXPORTS</code> (see <B>Ghidra/Features/Base/data/symbols/README.txt</B>). (GT-3235)</li>
    <li><I>Jython</I>. Upgraded Jython to version 2.7.2. (GP-109)</li>
    <li><I>Listing</I>. In the PCode field of the Listing, accesses of varnodes in the <code>unique</code> space are now always shown with the size of the access.  Fixed bug which would cause the PCode emulator to reject valid pcode in rare instances. (GP-196)</li>
    <li><I>Listing:Data</I>. Improved handling and display of character sequences embedded in operands or integer values. (GT-3347, Issue #1241)</li>
    <li><I>Multi-User:Ghidra Server</I>. Added ability to specify initial Ghidra Server user password (<code><B>-a0</B></code> mode only) for the svrAdmin <B>add</B> and <B>reset</B> commands. (GT-3640, Issue #321)</li>
    <li><I>Processors</I>. Updated AVR8 ATmega256 processor model to reflect correct memory layout specification. (GT-933)</li>
    <li><I>Processors</I>. Implemented semantics for <code>vstmia/db</code> <code>vldmia/db</code>, added missing instructions, and fixed shift value for several instructions for the ARM/Thumb NEON instruction set. (GT-2567)</li>
    <li><I>Processors</I>. Added the XMEGA variant of the AVR8 processor with general purpose registers moved to a non-memory-mapped register space. (GT-2909)</li>
    <li><I>Processors</I>. Added support for x86 <code>SALC</code> instruction. (GT-3367, Issue #1303)</li>
    <li><I>Processors</I>. Implemented pcode for 6502 <code>BRK</code> instruction. (GT-3375, Issue #1049)</li>
    <li><I>Processors</I>. Implemented x86 <code>PTEST</code> instruction. (GT-3380, Issue #1295)</li>
    <li><I>Processors</I>. Added missing instructions to ARM language module. (GT-3394)</li>
    <li><I>Processors</I>. Added support for <code>RDRAND</code> and <code>RDSEED</code> instructions to x86-32. (GT-3413)</li>
    <li><I>Processors</I>. Improved x86 breakpoint disassembly. (GT-3421, Issue #872)</li>
    <li><I>Processors</I>. Added manual index file for the M6809 processor. (GT-3449, Issue #1414)</li>
    <li><I>Processors</I>. Corrected issues related to retained instruction context during a language upgrade.  In some rare cases this retained context could interfere with the instruction re-disassembly.  This context-clearing mechanism is controlled by a new pspec property: <code>resetContextOnUpgrade</code>. (GT-3531)</li>
    <li><I>Processors</I>. Updated PIC24/PIC30 index file to match latest manual.  Added support for dsPIC33C. (GT-3562)</li>
    <li><I>Processors</I>. Added missing call-fixup to handle call side-effects for 32 bit gcc programs for <code>get_pc_thunk.ax/si</code>. (GP-10)</li>
    <li><I>Processors</I>. Added <code>ExitProcess</code> to PEFunctionsThatDoNotReturn. (GP-35)</li>
    <li><I>Processors</I>. <B>External Disassembly</B> field in the Listing now shows Thumb disassembly when appropriate TMode context has been established on a memory location. (GP-49)</li>
    <li><I>Processors</I>. Changed RISC-V jump instructions to the more appropriate <code>goto</code> instead of <code>call</code>. (GP-54, Issue #2120)</li>
    <li><I>Processors</I>. Updated AARCH64 to v8.5, including new MTE instructions. (GP-124)</li>
    <li><I>Processors</I>. Added support for floating point params and return for SH4 processor calling conventions. (GP-183, Issue #2218)</li>
    <li><I>Processors</I>. Added semantic support for many AARCH64 neon instructions. Addresses for register lanes are now precalculated, reducing the amount of p-code generated. (GP-343)</li>
    <li><I>Processors</I>. Updated RISCV processor to include reorganization, new instructions, and fixes to several instructions. (GP-358, Issue #2333)</li>
    <li><I>Program API</I>. Improved multi-threaded ProgramDB access performance. (GT-3262)</li>
    <li><I>Scripting</I>. Improved <B>ImportSymbolScript.py</B> to import functions in addition to generic labels. (GT-3249, Issue #946)</li>
    <li><I>Scripting</I>. Python scripts can now call protected methods from the GhidraScript API. (GT-3334, Issue #1250)</li>
    <li><I>Scripting</I>. Updated scripting feature with better change detection, external jar dependencies, and modularity. (GP-4)</li>
    <li><I>Scripting</I>. Updated the GhidraDev plugin (v2.1.1) to support Python Debugging when PyDev is installed via the Eclipse <B>dropins</B> directory. (GP-186, Issue #1922)</li>
    <li><I>Sleigh</I>. Error messages produced by the SLEIGH compiler have been reformatted to be more consistent in layout as well as more descriptive and more consistent in providing line number information. (GT-3174)</li>
</ul>
</blockquote>
<blockquote><p><u>Bugs</u></p>
<ul>
    <li><I>Analysis</I>. Function start patterns found at 0x0, function signatures applied from the Data Type Manager at 0x0, and DWARF debug symbols applied at 0x0 will no longer cause stack traces.  In addition, DWARF symbols with zero length address range no longer stack trace. (GT-2817, Issue #386, #1560)</li>
    <li><I>Analysis</I>. Constant propagation will treat an OR with zero (0) as a simple copy. (GT-3548, Issue #1531)</li>
    <li><I>Analysis</I>. Corrected <B>Create Structure from Selection</B>, which failed to use proper data organization during the construction process.  This could result in improperly sized components such as pointers and primitive types. (GT-3587)</li>    
    <li><I>Analysis</I>. Fixed an issue where stored context is initializing the set of registers constantly. (GP-25)</li>
    <li><I>Analysis</I>. Fixed an RTTI Analyzer regression when analyzing RTTI0 structures with no RTTI4 references to them. (GP-62, Issue #2153)</li>
    <li><I>Analysis</I>. Fixed an issue where the RTTI analyzer was not filling out RTTI3 structures in some cases. (GP-111)</li>
    <li><I>API</I>. Fixed NullPointerException when attempting to delete all bookmarks from a script. (GT-3405)</li>
    <li><I>API</I>. Updated the Class Searcher so that Extension Points found in the <B>Ghidra/patch</B> directory get loaded. (GT-3547, Issue #1515)</li>
    <li><I>Build</I>. Updated dependency fetch script to use HTTPS when downloading CDT. (GP-69, Issue #2173)</li>
    <li><I>Build</I>. Fixed resource leak in Ghidra jar builder. (GP-342)</li>
    <li><I>Byte Viewer</I>. Fixed Byte Viewer to correctly load the middle-mouse highlight color options change. (GT-3471, Issue #1464, #1465)</li>
    <li><I>Data Types</I>. Fixed decoding of static strings that have a character set with a smaller character size than the platform's character size. (GT-3333, Issue #1255)</li>
    <li><I>Data Types</I>. Correctly handle Java character sets that do not support the encoding operation. (GT-3407, Issue #1358)</li>
    <li><I>Data Types</I>. Fixed bug that caused Data Type Manager Editor key bindings to get deleted. (GT-3411, Issue #1355)</li>
    <li><I>Data Types</I>. Updated the DataTypeParser to handle data type names containing templates. (GT-3493, Issue #1417)</li>
    <li><I>Data Types</I>. Corrected pointer data type <code>isEquivalent()</code> method to properly check the equivalence of the base data type.  The old implementation could cause a pointer to be replaced by a conflicting pointer with the same name whose base datatype is not equivalent.  This change has a negative performance impact associated with it and can cause additional conflict datatypes due to the rigid datatype relationships. (GT-3557)</li>
    <li><I>Data Types</I>. Improved composite conflict resolution performance and corrected composite merge issues when composite bitfields and/or flexible arrays are present. (GT-3571)</li>
    <li><I>Data Types</I>. Fixed bug in SymbolPathParser naive parse method that caused a less-than-adequate fall-back parse when angle bracket immediately followed the namespace delimiter. (GT-3620)</li>
    <li><I>Data Types</I>. Corrected size of <code><B>long</B></code> for AARCH64 per LP64 standard. (GP-175)</li>
    <li><I>Decompiler</I>. Fixed bug causing the Decompiler to miss symbol references when they are stored to the heap. (GT-3267)</li>
    <li><I>Decompiler</I>. Fixed bug in the Decompiler that caused <code>Deleting op with descendants</code> exception. (GT-3506)</li>
    <li><I>Decompiler</I>. Decompiler now correctly compensates for integer promotion on shift, division, and remainder operations. (GT-3572)</li>
    <li><I>Decompiler</I>. Fixed handling of 64-bit implementations of alloca_probe in the Decompiler. (GT-3576)</li>
    <li><I>Decompiler</I>. Default Decompiler options now minimize the risk of losing code when renaming or retyping variables. (GT-3577)</li>
    <li><I>Decompiler</I>. The Decompiler no longer inherits a variable name from a subfunction if that variable incorporates additional data-flow unrelated to the subfunction. (GT-3580)</li>
    <li><I>Decompiler</I>. Fixed the Decompiler <B>Override Signature</B> action to be enabled on the entire C-code statement. (GT-3636, Issue #1589)</li>
    <li><I>Decompiler</I>. Fixed frequent ClassCast and IllegalArgument exceptions when performing <B>Auto Create Structure</B> or <B>Auto Create Class</B> actions in the Decompiler. (GP-119)</li>
    <li><I>Decompiler</I>. Fixed a bug in the Decompiler that caused different variables to be assigned the same name in rare instances. (GP-243, Issue #1995)</li>
    <li><I>Decompiler</I>. Fixed a bug in the Decompiler that caused <code>PTRSUB off of non-pointer type</code> exceptions. (GP-244, Issue #1826)</li>
    <li><I>Decompiler</I>. Fixed a bug in the Decompiler that caused load operations from volatile memory to be removed as dead code. (GP-245, Issue #393, #1832)</li>
    <li><I>Decompiler</I>. Fixed a bug causing the Decompiler to miss a stack alias if its offset was, itself, stored on the stack. (GP-246)</li>
    <li><I>Decompiler</I>. Fixed a bug causing the Decompiler to lose Equate references to constants passed to functions that were called indirectly. (GP-247)</li>
    <li><I>Decompiler</I>. Addressed various situations where the Decompiler unexpectedly removes active instructions as dead code after renaming or retyping a stack location. If the location was really an array element or structure field, renaming forced the Decompiler to treat the location as a distinct variable. Subsequently, the Decompiler thought that indirect references based before the location could not alias any following stack locations, which could then by considered dead. As of the 9.2 release, the Decompiler's renaming action no longer switches an annotation to <code>forcing</code> if it wasn't already. A retyping action, although it is <code>forcing</code>, won't trigger alias blocking for atomic data-types (this is configurable). (GP-248, Issue #524, #873)</li>
    <li><I>Decompiler</I>. Fixed decompiler memory issues reported by a community security researcher. (GP-267)</li>
    <li><I>Decompiler</I>. Fix for Decompiler error: <code>Pcode: XML comms: Missing symref attribute in &lt;high&gt; tag</code>. (GP-352, Issue #2360)</li>
    <li><I>Decompiler</I>. Fixed bug preventing the Decompiler from seeing Equates attached to <B>compare</B> instructions. (GP-369, Issue #2386)</li>
    <li><I>Demangler</I>. Fixed the GnuDemangler to parse the full namespace for <code>operator</code> symbols. (GT-3474, Issue #1441, #1448)</li>
    <li><I>Demangler</I>. Fixed numerous GNU Demangler parsing issues.  Most notable is the added support for C++ Lambda functions. (GT-3545, Issue #1457, #1569)</li>
    <li><I>Demangler</I>. Updated the GNU Demangler to correctly parse and apply C++ strings using the <code>unnamed type</code> syntax. (GT-3645)</li>
    <li><I>Demangler</I>. Fixed duplicate namespace entry returned from <code>getNamespaceString()</code> on DemangledVariable. (GT-3646, Issue #1729)</li>
    <li><I>Demangler</I>. Fixed a GnuDemangler ClassCastException when parsing a <code>typeinfo</code> string containing <code>operator</code> text. (GP-160, Issue #1870, #2267)</li>
    <li><I>Demangler</I>. Added <B>stdlib.h</B> include to the GNU Demangler to fix a build issue on some systems. (GP-187, Issue #2294)</li>
    <li><I>DWARF</I>. Corrected DWARF relocation handling where the address image base adjustment was factored in twice. (GT-3330)</li>
    <li><I>File Formats</I>. Fixed a potential divide-by-zero exception in the EXT4 file system. (GT-3400, Issue #1342)</li>
    <li><I>File Formats</I>. Fixed date and time parsing of dates in cdrom <code>iso9660</code> image files. (GT-3451, Issue #1403)</li>
    <li><I>Graphing</I>. Fixed a ClassCastException sometimes encountered when performing <B>Select -> Scoped Flow -> Forward Scoped Flow</B>. (GP-180)</li>
    <li><I>GUI</I>. Fixed inconsistent behavior with the interactive python interpreter's key bindings. (GT-3282)</li>
    <li><I>GUI</I>. Fixed Structure Editor bug that prevented the <B>F2 Edit</B> action from editing the correct table cell after using the arrow keys. (GT-3308, Issue #703)</li>
    <li><I>GUI</I>. Updated the Structure Editor so the <B>Delete</B> action is put into a background task to prevent the UI from locking. (GT-3352)</li>
    <li><I>GUI</I>. Fixed IndexOutOfBoundsException when invoking column filter on Key Bindings table. (GT-3445)</li>
    <li><I>GUI</I>. Fixed the analysis log dialog to not consume all available screen space. (GT-3610)</li>
    <li><I>GUI</I>. Fixed issue where <B>Location</B> column, when used in the column filters, resulted in extraneous dialogs popping up. (GT-3623)</li>
    <li><I>GUI</I>. Fixed Data Type Preview <B>copy</B> action so that newlines are preserved; updated table export to CSV to escape quotes and commas. (GT-3624)</li>
    <li><I>GUI</I>. Fixed tables in Ghidra to copy the text that is rendered.  Some tables mistakenly copied the wrong value, such as the Functions Table's Function Signature Column. (GT-3629, Issue #1628)</li>
    <li><I>GUI</I>. Structure editor name now updates in title bar and tab when structure is renamed. (GP-19)</li>
    <li><I>GUI</I>. Fixed an issue where drag-and-drop import locks the Windows File Explorer source window until the import dialog is closed by the user. (GP-27)</li>
    <li><I>GUI</I>. Fixed an issue in GTreeModel where fireNodeChanged had no effect. This could result in stale node information and truncation of the text associated with a node in a GTree. (GP-30)</li>
    <li><I>GUI</I>. Fixed an issue where the file chooser directory list truncated filenames with ellipses on HiDPI Windows. (GP-31)</li>
    <li><I>GUI</I>. Fixed an uncaught exception when double-clicking on <code>UndefinedFunction_</code> in Decompiler window. (GP-40)</li>
    <li><I>GUI</I>. Updated error handling to only show one dialog when a flurry of errors is encountered. (GP-65, Issue #2185)</li>
    <li><I>GUI</I>. Fixed an issue where Docking Windows are restored incorrectly if a snapshot is present. (GP-92)</li>
    <li><I>GUI</I>. Fixed a File Chooser bug causing a NullPointerException for some users. (GP-171, Issue #1706)</li>
    <li><I>GUI</I>. Fixed an issue that caused the script progress bar to appear intermittently. (GP-179, Issue #1819)</li>
    <li><I>GUI</I>. Fixed a bug that caused Call Tree nodes to go missing when showing more than one function with the same name. (GP-213, Issue #1682)</li>
    <li><I>GUI:Project Window</I>. Fixed Front End <B>copy</B> action to allow for the copy of program names so that users can paste those names into external applications. (GT-3403, Issue #1257)</li>
    <li><I>Headless</I>. Headless Ghidra now properly honors the <code><B>-processor</B></code> flag, even if the specified processor is not a valid <B>opinion</B>. (GT-3376, Issue #1311)</li>
    <li><I>Importer</I>. Corrected an NeLoader flags parsing error. (GT-3381, Issue #1312)</li>
    <li><I>Importer</I>. Fixed the <B>File -> Add to Program...</B> action to not show a memory conflict error when the user is creating an overlay. (GT-3491, Issue #1376)</li>
    <li><I>Importer</I>. Updated the XML Importer to apply repeatable comments. (GT-3492, Issue #1423)</li>
    <li><I>Importer</I>. Fixed issue in Batch Import where only one item of a selection was removed when attempting to remove a selection of items. (GP-138)</li>
    <li><I>Importer</I>. Corrected various issues with processing crushed PNG images. (GP-146, Issue #1854, #1874, #1875, #2252)</li>
    <li><I>Importer</I>. Fixed RuntimeException occurrence when trying to load NE programs with unknown resources. (GP-182, Issue #1596, #1713, #2012)</li>
    <li><I>Importer</I>. Fixed batch import to handle IllegalArgumentExceptions thrown by loaders. (GP-227, Issue #2328)</li>
    <li><I>Importer:ELF</I>. Corrected ELF relocation processing for ARM BE8 (mixed-endian). (GT-3527, Issue #1494)</li>
    <li><I>Importer:ELF</I>. Corrected ELF relocation processing for <code>R_ARM_PC24</code> (Type: 1) that was causing improper flow in ARM disassembly. (GT-3654)</li>
    <li><I>Importer:ELF</I>. Corrected ELF import processing of <code>DT_JMPREL</code> relocations and markup of associated PLT entries. (GP-252, Issue #2334)</li>
    <li><I>Importer:PE</I>. Fixed an IndexOutOfBoundsException in the PeLoader that occurred when the size of a section extends past the end of the file. (GT-3433, Issue #1371)</li>
    <li><I>Listing:Comments</I>. Fixed bug in Comment field that prevented navigation when clicking on an address or symbol where tabs were present in the comment. (GT-3440)</li>
    <li><I>Memory</I>. Fixed bug where sometimes random bytes are inserted instead of <code>0x00</code> when expanding a memory block. (GT-3465)</li>
    <li><I>Processors</I>. Corrected the offset in SuperH instructions generated by sign-extending a 20-bit immediate value composed of two sub-fields. (GT-3251, Issue #1161)</li>
    <li><I>Processors</I>. Fixed AVR8 addition/subtraction flag macros. (GT-3276)</li>
    <li><I>Processors</I>. Corrected <code>XGATE</code> <code>ROR</code> instruction semantics. (GT-3278)</li>
    <li><I>Processors</I>. Corrected semantics for SuperH <code>movi20</code> and <code>movi20s</code> instructions. (GT-3337, Issue #1264)</li>
    <li><I>Processors</I>. Corrected SuperH floating point instruction token definition. (GT-3340, Issue #1265)</li>
    <li><I>Processors</I>. Corrected SuperH <code>movu.b</code> and <code>movu.w</code> instruction semantics. (GT-3345, Issue #1271)</li>
    <li><I>Processors</I>. Corrected AVR8 <code>lpm</code> and <code>elpm</code> instruction semantics. (GT-3346, Issue #631)</li>
    <li><I>Processors</I>. Corrected pcode for the 6805 <code>BSET</code> instruction. (GT-3366, Issue #1307)</li>
    <li><I>Processors</I>. Corrected ARM constructors for instructions <code>vnmla</code>, <code>vnmls</code>, and <code>vnmul</code>. (GT-3368, Issue #1277)</li>
    <li><I>Processors</I>. Corrected bit-pattern for ARM <code>vcvt</code> instruction. (GT-3369, Issue #1278)</li>
    <li><I>Processors</I>. Corrected TriCore <code>abs</code> instructions. (GT-3379, Issue #1286)</li>
    <li><I>Processors</I>. Corrected x86 <code>BT</code> instruction semantics. (GT-3423, Issue #1370)</li>
    <li><I>Processors</I>. Fixed issue where CRC16C <code>LOAD</code>/<code>STOR</code> with <code>abs20</code> were not mapped correctly. (GT-3529, Issue #1518)</li>
    <li><I>Processors</I>. Fixed M68000 <code>MOVE USP,x</code> and <code>MOVE x,USP</code> opcodes. (GT-3594, Issue #1593)</li>
    <li><I>Processors</I>. Fixed the ARM/Thumb <code>TEQ</code> instruction pcode to be an XOR. (GP-23, Issue #1802)</li>
    <li><I>Processors</I>. Emulation was broken by a regression in version 9.1.2.  Emulation and Sleigh Pcodetests now work correctly. (GP-24, Issue #1579)</li>
    <li><I>Processors</I>. Fixed carry flag issue for 6502 <code>CMP</code>, <code>CPX</code>, and <code>CPY</code> instructions. (GP-34)</li>
    <li><I>Processors</I>. Corrected the SuperH high-order bit calculation for the <code>rotr</code> instruction. (GP-47)</li>
    <li><I>Processors</I>. Corrected ELF ARM relocation processing for type 3 (<code>R_ARM_REL32</code>) and added support for type 42 (<code>R_ARM_PREL31</code>). (GP-164, Issue #2261, #2276)</li>
    <li><I>Scripting</I>. Moved Jython cache directory out of <B>tmp</B>. (GP-36)</li>
    <li><I>Scripting</I>. Fixed a NoClassDefFoundError when compiling GhidraScript under JDK14. (GP-59, Issue #2152)</li>
    <li><I>Scripting</I>. Fixed issues with null result when searching for the script directory. (GP-103, Issue #2187)</li>
    <li><I>Scripting</I>. Fixed scripting issue where, if there were non-ASCII characters in the user path, Jython would not work. (GP-204, Issue #1890)</li>
    <li><I>Sleigh</I>. Corrected IndexOutOfBoundsException in SLEIGH when doing simple assignment in disassembly actions block. (GT-3382, Issue #745)</li>
    <li><I>Symbol Tree</I>. Fixed the Symbol Tree so that clicking an already-selected symbol node will still trigger a Listing navigation. (GT-3436, Issue #453)</li>
    <li><I>Symbol Tree</I>. Fixed the Symbol Tree to not continuously rebuild while performing Auto-analysis. (GT-3542)</li>
    <li><I>Version Tracking</I>. Fixed Version Tracking <B>Create Manual Match</B> action. (GT-3305, Issue #2215)</li>
    <li><I>Version Tracking</I>. Fixed a NullPointerException encountered when changing the Version Tracking options for the <B>Listing Code Comparison</B> when no data was loaded. (GT-3437, Issue #1143)</li>
    <li><I>Version Tracking</I>. Fixed Version Tracking exception triggered in the <B>Exact Functions Instructions Match</B> correlator encountered when the two functions being compared differed in their number of instructions. (GT-3438, Issue #1352)</li>
</ul>
</blockquote>

<H1 align="center">Ghidra 9.1.2 Change History (February 2020)</H1>
<blockquote><p><u>Bugs</u></p>
<ul>
    <li><I>Data Types</I>. Improved PDB composite reconstruction to attempt <code>pack(1)</code> alignment if default alignment fails. (GT-3401)</li>
    <li><I>Data Types</I>. Added missing support for multi-user merge of unions and structures containing bitfields or a trailing flexible array member. (GT-3479)</li>
    <li><I>Data Types</I>. Corrected structure editor save button enablement issue when editing bitfields within a non-packed structure. (GT-3519, Issue #1297)</li>
    <li><I>Disassembly</I>. Corrected potential infinite loop with disassembler caused by branch to self with invalid delay slot instruction. (GT-3511, Issue #1486)</li>
    <li><I>GUI</I>. Corrected processor manual display for Microsoft Windows users, which was not displaying processor manual and was, instead, rendering a blank page in web browser. (GT-3444)</li>
    <li><I>GUI:Bitfield Editor</I>. Added field comment support to composite bitfield editor. (GT-3410)</li>
    <li><I>Importer:Mach-O</I>. A Mach-O loader regression, in Ghidra 9.1.1, when laying down symbols at the correct location, has been fixed. (GT-3487, Issue #1446)</li>
    <li><I>Multi-User:Ghidra Server</I>. Corrected Ghidra Server remote interface errors that occur when running with Java 11.0.6 (and later) release, which would throw RemoteException <code>Method is not Remote</code> errors. (GT-3521, Issue #1440)</li>
    <li><I>PDB</I>. Corrected PDB XML generation for zero-length classes and structures and resolved various datatype dependency issues encountered during PDB Analysis. Changed line numbers from hex to decimal. (GT-3462, Issue #1410)</li>
    <li><I>Processors</I>. Corrected mnemonic for ARM thumb <code>RSB.w</code> instruction. (GT-3420, Issue #1365)</li>
    <li><I>Processors</I>. Corrected issue in M68000 with some move instructions not creating correct array assignments. (GT-3429, Issue #1394)</li>
    <li><I>Processors</I>. Updated x86 processor manual index file with latest Intel and AMD manuals. (GT-3489, Issue #1078)</li>
</ul>
</blockquote>

<H1 align="center">Ghidra 9.1.1 Change History (December 2019)</H1>
<blockquote><p><u>Improvements</u></p>
<ul>
    <li><I>Importer:Mach-O</I>. Improved import/load time of DYLD shared cache files. (GT-3261)</li>
    <li><I>Program API</I>. Cached the addresses that correspond to executable memory to improve analysis performance. (GT-3260)</li>
</ul>
</blockquote>

<blockquote><p><u>Bugs</u></p>
<ul>
    <li><I>Analysis</I>. Fixed a symbol name error that occurred in the Objective-C analyzer. (GT-3321, Issue #1200)</li>
    <li><I>Analysis</I>. Constant references are now computed correctly within functions in overlay spaces. (GT-3373)</li>
    <li><I>Build</I>. Corrected build of <B>DMG.jar</B> which was improperly built within Ghidra 9.1 release. (GT-3364)</li>
    <li><I>Decompiler</I>. Fixed bug causing <code>Pcode: XML comms: Badly formed address</code> errors when decompiling HCS12 <code>XGATE</code> code. (GT-3297)</li>
    <li><I>Decompiler</I>. Fixed <code>Array DataType must be Fixed length</code> exceptions related to function pointer data types. (GT-3309)</li>
    <li><I>Decompiler</I>. Fixed bug causing decompiler to drop statements, assigning string constants to global variables. (GT-3315)</li>
    <li><I>Decompiler</I>. Fixed issue with enum name strings causing <code>Low-level Error: XML error: syntax error</code> in the decompiler. (GT-3387, Issue #1329)</li>
    <li><I>GUI</I>. Fixed a potential ConcurrentModificationException in the interactive python interpreter. (GT-3280)</li>
    <li><I>Importer:PE</I>. Fixed an exception in the PeLoader that occurred when the size of the memory block for the headers is larger than the file size. (GT-3344, Issue #1266)</li>
    <li><I>Listing</I>. Fixed missing scroll bar in listing. (GT-3290)</li>
    <li><I>Listing</I>. Fixed issue that was causing a stack trace to be generated when contiguous addresses were cleared for a range greater than <code>Integer.MAX</code>. (GT-3357)</li>
    <li><I>Listing:References</I>. Corrected <B>Create Default Reference</B> action bug which did not handle composite/array data components properly. (GT-3371)</li>
    <li><I>Processors</I>. Corrected Sparc floating point instruction pcode implementation. (GT-3202)</li>
    <li><I>Processors</I>. Corrected the semantics of the PowerPC <code>e_cmpi</code> instruction. (GT-3228, Issue #1127)</li>
    <li><I>Processors</I>. Corrected bit generation for PowerPC instructions <code>se_bclri</code>, <code>se_bgeni</code>, <code>se_bseti</code>, and <code>se_btsti</code>. (GT-3232, Issue #967)</li>
    <li><I>Processors</I>. Corrected register definitions for x86 <code>RDRAND</code> instruction. (GT-3253, Issue #1169)</li>
    <li><I>Processors</I>. Corrected signed immediate calculation for some powerPC VLE offsets being incorrect. (GT-3254, Issue #1160)</li>
    <li><I>Processors</I>. Resolved issue with x86 escape opcodes preventing certain instruction patterns from decoding. (GT-3256)</li>
    <li><I>Processors</I>. Corrected bug in <code>XGATE</code> <code>LDH</code> instruction shifting out high bits. (GT-3268)</li>
    <li><I>Processors</I>. Corrected processing of <code>R_MIPS_REL32</code>, <code>R_X86_64_RELATIVE</code>, and <code>R_X86_64_RELATIVE64</code> ELF relocations affecting relocatable binaries which have non-zero section/segment load addresses. (GT-3349)</li>
</ul>
</blockquote>

<H1 align="center">Ghidra 9.1 Change History (October 2019)</H1>
<blockquote><p><u>New Features</u></p>
<ul>
    <li><I>Data Types</I>. Added bit-field support to Structure and Union editor.  An additional Bit-field Editor was also added for explicit bit-field placement within non-packed structures. (GT-559)</li>
    <li><I>Eclipse Integration</I>. Added new GhidraSleighEditor Eclipse plugin in the installation directory under Extensions/Eclipse. (GT-113)</li>
    <li><I>GUI</I>. Added method for turning off table sorting by control-clicking the only sorted table column. (GT-2763, Issue #87)</li>
    <li><I>GUI</I>. Hovering on an address will now show where the byte at that address came from in the imported file. (GT-3016, Issue #154)</li>
    <li><I>Importer:Mach-O</I>. Added new importer/loader for DYLD-shared cache files. (GT-2343)</li>
    <li><I>Memory</I>. Added new API to preserve imported program's original bytes and how they map to memory blocks. (GT-2845)</li>
    <li><I>Processors</I>. Implemented Intel MCS-96 processor module. (GT-2350)</li>
    <li><I>Processors</I>. Added SH1/2/2a sleigh processor specification. (GT-3029, Issue #715)</li>
    <li><I>Processors</I>. Added Tricore processor specification. (GT-3041, Issue #567)</li>
    <li><I>Processors</I>. Added HCS12X processor specification. (GT-3049)</li>
    <li><I>Processors</I>. Added HCS05 and HCS08 sleigh processor specifications. (GT-3050)</li>
    <li><I>Processors</I>. Added SH4 sleigh processor specification. (GT-3051, Issue #37)</li>
    <li><I>Processors</I>. Added MCS-48 processor specification. (GT-3058, Issue #638)</li>
    <li><I>Program API</I>. Added Bit-field support for structures and unions.
<B>Warning:</B> Version upgrade will be forced on all modified programs and data type archives that are open for update. (GT-557)</li>
    <li><I>Sleigh</I>. Added two new extension modules (SleighDevTools and GnuDisassembler) in support of processor module development.  Added support for pcode junit tests which utilize emulation of cross-compiled C  test code to verify sleigh pcode (i.e., instruction semantics).  The SleighDevTools extension provides the pcode test C source and associated build scripts, as well as external disassembler support for aiding in the validation of disassembled instruction syntax. (GT-3067)</li>
</ul>
</blockquote>

<blockquote><p><u>Improvements</u></p>
<ul>
    <li><I>Analysis</I>. Added example script, ResolveX86orX64LinuxSyscallsScript.java, for decompiling Linux system calls in x86 and x64.  Added syscall-related exercises to <B>Advanced</B> class. (GT-3113)</li>
    <li><I>Basic Infrastructure</I>. Made bash scripts more portable, allowing Ghidra to be launched on additional platforms. (GT-2742, Issue #347)</li>
    <li><I>Build</I>. Created a new Gradle task that automates some installation procedures defined in <B>DevGuide.md</B>. (GT-2897)</li>
    <li><I>Build</I>. The build now allows newer versions of Gradle to be used. (GT-3017, Issue #737)</li>
    <li><I>Data Types</I>. All DataType archives have been regenerated to support the new bit-field functionality. (GT-2878)</li>
    <li><I>Data Types</I>. CategoryPath now accepts forward slashes in its components. (GT-2961)</li>
    <li><I>Data Types</I>. Fixed Structure Editor bug that caused the <B>Data Type</B> field of a row to be edited after a successful name field edit. (GT-3109, Issue #703)</li>
    <li><I>Decompiler</I>. Most forms of unnecessary or redundant copy statements are now removed from the decompiler output. (GT-2839)</li>
    <li><I>Decompiler</I>. Added ability to double-click a Decompiler brace syntax token to navigate to the matching brace. (GT-2846)</li>
    <li><I>Decompiler</I>. Updated the Decompiler to navigate to the label of a <code>goto</code> statement when that label is double-clicked. (GT-2847)</li>
    <li><I>Decompiler</I>. Updated the Decompiler's <B>Copy</B> action to copy the symbol under the cursor when there is no selection. (GT-2914, Issue #411)</li>
    <li><I>Decompiler</I>. Fixed broken <B>External Navigation: Navigate to External Program</B> option found in <B>Edit -> Tool Options...</B>. (GT-2932)</li>
    <li><I>Decompiler</I>. The decompiler's logic for handling optimized division has been updated to recognize forms typically found in executables generated with more recent 64-bit compilers. (GT-2968, Issue #668)</li>
    <li><I>Decompiler</I>. Implemented call-fixup for x64 <code>__chkstk</code> function. (GT-3006, Issue #670, #671)</li>
    <li><I>Decompiler</I>. The decompiler simplifies many new sign-bit extraction forms used in optimized division and comparison expressions. (GT-3036)</li>
    <li><I>Decompiler</I>. Ghidra now supports protected mode addressing when analyzing 16-bit x86 programs. This is the default variant when analyzing NE format executables, but it can also be used for MZ (and other) formats. (GT-3090, Issue #98)</li>
    <li><I>Decompiler</I>. Added the <B>Show References to Address</B> and <B>Find References to <I>Symbol</I></B> actions to the Decompiler.  Added <B>Find Uses of <I>Field</I></B> action to the Structure Editor. (GT-3115, Issue #474, #542, #543)</li>
    <li><I>Decompiler</I>. Updated the Decompiler's <B>Edit Data Type</B> action to work on more fields. (GT-3116, Issue #275, #511)</li>
    <li><I>Decompiler</I>. Renaming a single parameter within the decompiler window no longer prevents the data types of parameters from floating. Retyping a single parameter locks the data type for that parameter but no longer prevents the data types of other parameters from floating. (GT-3162)</li>
    <li><I>Documentation</I>. Fixed typos and other errors in GitHub-related documentation. (GT-2748, Issue #345, #361, #370, #375, #398)</li>
    <li><I>Documentation</I>. Added documentation to the <B>DevGuide.md</B> on how to run unit/integration tests. (GT-3046, Issue #815, #832)</li>
    <li><I>DWARF</I>. Corrected DWARF analysis to handle binaries that are imported at non-default locations. (GT-2963, Issue #637)</li>
    <li><I>Emulator</I>. Added improved emulation support at the API level including a simplified API exposed via the EmulatorHelper class.  Sample GhidraScripts, which utilize this API, have been provided. (GT-3066)</li>
    <li><I>Function Graph</I>. Updated the Function Graph to show the current program selection when zoomed out. (GT-2735)</li>
    <li><I>Function Graph</I>. Added an option to the Function Graph to allow more complex edge routing that will go around non-incident vertices.   See the Tool Options for more information and to enable this feature. (GT-3019, Issue #811)</li>
    <li><I>Function Graph</I>. Fixed Function Graph edge layout bugs that caused some edges to get clipped by vertices. (GT-3161)</li>
    <li><I>GUI</I>. Added listener to Script Table Chooser Dialog that will get notified when the dialog closes. (GT-2216)</li>
    <li><I>GUI</I>. Fixed global Tool <B>auto-save</B> option so that it persists between Ghidra sessions. (GT-2818, Issue #231)</li>
    <li><I>GUI</I>. Added the <B>apple.laf.useScreenmenuBar</B> option to hoist the menu bar out of the window on macOS.  The option is off by default but can be activated in <B>support/launch.properties</B>. (GT-2859, Issue #562)</li>
    <li><I>GUI</I>. Updated the <B>Repeat Text Search</B>/<B>Repeat Memory Search</B> menu items to show the search dialog for long searches. (GT-2872, Issue #585)</li>
    <li><I>GUI</I>. Updated Structure Editor to allow user key bindings to work. (GT-2894, Issue #504)</li>
    <li><I>GUI</I>. Python interpreter key bindings for sending reset and interrupt commands are now configurable. (GT-2901, Issue #588)</li>
    <li><I>GUI</I>. Tweaked default graphic settings in <B>support/launch.properties</B> to support a wider range of displays out-of-the-box. (GT-2913, Issue #341)</li>
    <li><I>GUI</I>. Added the ability to assign key bindings to activate individual component providers. (GT-2925, Issue #539)</li>
    <li><I>GUI</I>. Fixed rendering issue in the Search Results table's <B>Preview</B> column. (GT-2942, Issue #550)</li>
    <li><I>GUI</I>. Updated the Function Signature Editor's <B>Data Type Chooser</B> dialog to allow for keyboard navigation. (GT-3110, Issue #636)</li>
    <li><I>GUI</I>. Fixed NullPointerException in the DB Viewer component. (GT-3163, Issue #1023)</li>
    <li><I>Importer</I>. Updated x86 16-bit processor binding for IDA. (GT-3004, Issue #771)</li>
    <li><I>Importer:ELF</I>. Improved ELF loader ability to cope with malformed headers including negative file offsets and missing section names. (GT-2933, Issue #35)</li>
    <li><I>Importer:PE</I>. PeLoader better accounts for section alignment when laying out memory blocks, allowing additional bytes from the file to be loaded into memory. (GT-2827, Issue #327, #418)</li>
    <li><I>Importer:PE</I>. Removed out-of-place call to demangler and laying down of types from PeLoader. This fix enables demangling and other analyzers to be applied correctly and in the proper order. (GT-2849)</li>
    <li><I>Importer:PE</I>. PeLoader now adds TLS callback functions as entry points. (GT-2898, Issue #102)</li>
    <li><I>Listing</I>. Updated Listing to support horizontal scrolling by holding the <B>Shift</B> key when using the mouse wheel. (GT-3105, Issue #451)</li>
    <li><I>Listing:References</I>. Created new <B>overriding</B> reference types, which improve and extend the ability to override calls, jumps, and callothers. (GT-2885)</li>
    <li><I>Multi-User</I>. Added a script to allow repository admins the ability to terminate multiple file checkouts belonging to an individual user on a shared project. (GT-2893)</li>
    <li><I>Multi-User:Ghidra Server</I>. Added additional Ghidra Server authentication modes including: Active Directory via Kerberos and JAAS.  The JAAS framework can facilitate use of LDAP, PAM, and other JAAS-supported extensions which utilize a login name and password. (GT-2658)</li>
    <li><I>Multi-User:Ghidra Server</I>. Changed Ghidra Server repositories storage to ignore file/folder names which start with a period.  This will impose a restriction on naming of Ghidra projects where they can no longer start with a period. (GT-3218)</li>
    <li><I>PDB</I>. Now using HTTPS for Microsoft symbol server URL. (GT-2819, Issue #369)</li>
    <li><I>PDB</I>. PDB processing can now store data types that contain forward slashes under a CategoryPath. (GT-2974, Issue #94, #182)</li>
    <li><I>PDB</I>. PDB Analyzer no longer automatically includes the PDB path specified in the program's PE header when searching for the PDB.  However, the filename in this path is considered during the search.  The analyzer's <B>Unsafe: Include PE PDB Path in PDB Search</B> option allows the user to revert to the original PDB search algorithm. (GT-3076, Issue #277)</li>
    <li><I>Processors</I>. Added new Task Monitor service to better handle user experience when there are delays in building languages. (GT-2376)</li>
    <li><I>Processors</I>. Corrected ARM/Thumb instruction parsing for Thumb <code>bl</code> and <code>add</code> instructions. (GT-2744, Issue #362)</li>
    <li><I>Processors</I>. Added AVR8 manual index file. (GT-2828, Issue #346)</li>
    <li><I>Processors</I>. Improved support for ARM on Windows. (GT-2880)</li>
    <li><I>Processors</I>. M68000 <code>LSL.W</code>, <code>ASL.B</code>, <code>LSL.B</code>, and <code>ASL.W</code> instructions now correctly set the <code>CF</code> flag. (GT-2907, Issue #619)</li>
    <li><I>Processors</I>. Updated x86 manual index files. (GT-2943, Issue #366)</li>
    <li><I>Processors</I>. Improved macro label-related error reporting in slaspec files. (GT-2995, Issue #522)</li>
    <li><I>Processors</I>. Added MIPS special <code>0x1f</code> patterns. (GT-3005, Issue #709)</li>
    <li><I>Processors</I>. Added proper updating of the <code>X</code> condition flag register for the M68000 processor <code>lsl</code> and <code>lsr</code> instructions. (GT-3137, Issue #983)</li>
    <li><I>Processors</I>. Implemented PowerPc VLE Interrupt Handler Efficiency Instructions. (GT-3143, Issue #935)</li>
    <li><I>Processors</I>. Ghidra now correctly models SPARC 64-bit <B>stack bias</B>. (GT-3201)</li>
    <li><I>Processors</I>. Updated AVR32 instruction manual index to latest version. (GT-712)</li>
    <li><I>Program API</I>. Added SHA256 hash to Program metadata and API. (GT-2753, Issue #331)</li>
    <li><I>Scripting</I>. Updated Script Table Chooser Dialog: to fix bug with tracking work items, to add new API methods for item removal and dialog closed notification, and to prevent the same item from being worked on more than once. (GT-2724, Issue #307)</li>
    <li><I>Scripting</I>. Fixed MultiInstructionMemReference Ghidra script to place the reference correctly on instructions with a delay slot. (GT-2906)</li>
    <li><I>Sleigh</I>. The sleigh compiler now reports line numbers for the <code><B>-n</B></code> NOP command line option. (GT-2905, Issue #561)</li>
    <li><I>Sleigh</I>. SLEIGH compiler now warns when building an operand in a constructor may unintentionally overwrite another operand. (GT-3085)</li>
    <li><I>Testing:Junits</I>. <code>test.gradle getLogFileUrl()</code> no longer searches user <B>.dir</B> for <B>log4j</B> properties file. (GT-2834, Issue #499)</li>
    <li><I>Testing:Junits</I>. Added new Gradle task to run integration tests and generate an HTML report. (GT-3060, Issue #870)</li>
    <li><I>Tool</I>. Fixed bug that caused an exported tool to exclude plugin configuration settings. (GT-3193, Issue #1065)</li>
</ul>
</blockquote>

<blockquote><p><u>Bugs</u></p>
<ul>
    <li><I>Analysis</I>. Fixed an exception in the EmbeddedMediaAnalyzer that occurred when media was discovered at the very end of the address space. (GT-2890)</li>
    <li><I>Analysis</I>. Recognition and disassembly of the <code>FMA</code>, <code>F16C</code>, and several missing <code>AVX</code> instructions have been added to the base x86 processor specification.  The pcode for these instructions is pseudo-op and not a full pcode implementation. (GT-3168)</li>
    <li><I>Basic Infrastructure</I>. Updated the apache-commons-lang3 library to version 3.9 which supports Java 11. (GT-2879)</li>
    <li><I>Basic Infrastructure</I>. Prevented Ghidra from launching with 32-bit Java installations. (GT-3146, Issue #882)</li>
    <li><I>Data Types</I>. Corrected string data default label generation when defined within uninitialized memory, which will now render as <code>STRING_<I>address</I></code>. (GT-2715, Issue #272)</li>
    <li><I>Data Types</I>. Improved ASCII string data handling for processors with a char size greater than one (1). (GT-2842)</li>
    <li><I>Data Types</I>. Changed BooleanDataType to extend AbstractIntegerDataType including support as a bit-field. (GT-3170)</li>
    <li><I>DbViewer</I>. Corrected concurrent modification issue within DbViewer resulting in NullPointerException. (GT-3192, Issue #1076)</li>
    <li><I>Decompiler</I>. Fixed aliasing issue where the decompiler would sometimes drop initialization or other code writing to the stack. (GT-2369)</li>
    <li><I>Decompiler</I>. Fixed bug causing the decompiler to incorrectly omit the display of infinite loops when they contained switch statements. (GT-2852, Issue #443)</li>
    <li><I>Decompiler</I>. Integer extension casts are no longer printed in the decompiler if the extension is implied. (GT-2857)</li>
    <li><I>Decompiler</I>. Improved handling of overlay spaces. In particular, the decompiler is now able to handle references into overlays defined on the OTHER space. Added SLEIGH version numbers. (GT-2873)</li>
    <li><I>Decompiler</I>. Updated the Decompiler to place the cursor on the function signature when a function is decompiled. (GT-2882)</li>
    <li><I>Decompiler</I>. Fixed a common source of <code>Data type does not fit</code> errors when using the <B>Retype</B> actions in the decompiler. (GT-2956)</li>
    <li><I>Decompiler</I>. Fixed <code>equals()</code> method in Varnode AST. (GT-2959, Issue #677)</li>
    <li><I>Decompiler</I>. Users can no longer rename undefined functions from the decompiler. (GT-3043, Issue #753)</li>
    <li><I>Decompiler</I>. Fixed a bug that did not allow the prototype for a specific CALL to an external function to be overridden in the decompiler. (GT-3145)</li>
    <li><I>Decompiler</I>. Restricted <B>Auto Fill in Structure</B> command to operate only on pointer variables. (GT-3182)</li>
    <li><I>Decompiler</I>. Fixed bug in the analysis of stack variables for SPARC, which caused extraneous local variables and missed stack parameters in the decompiler. (GT-3200)</li>
    <li><I>Decompiler</I>. Fixed one source of <code>Type propagation algorithm not settling</code> warnings in the decompiler. (GT-3213, Issue #839)</li>
    <li><I>Decompiler:Java</I>. Updated Decompiler's hovers to show preview for data types on variables and return types. (GT-2629)</li>
    <li><I>Decompiler:Java</I>. Fixed error involving decompilation of certain <code>invokedynamic</code> instructions in JVM class files.  Made numerous minor improvements to decompilation of JVM bytecode. (GT-2757, Issue #287)</li>
    <li><I>Demangler</I>. Fixed a NullPointerException in DemangledFunctionPointer. (GT-2948, Issue #609)</li>
    <li><I>DWARF</I>. Empty DWARF compilation unit sections will now be ignored. (GT-2939, Issue #690)</li>
    <li><I>Exporter</I>. Negative memory references in <B>idaxml.py</B> no longer cause errors. (GT-2696, Issue #213, #885)</li>
    <li><I>Exporter</I>. Fixed Intel Hex Exporter to not ignore the <B>Address Space</B> option value. (GT-2749)</li>
    <li><I>Exporter</I>. Fixed cancellation behavior of the C/C++ exporter. (GT-2881, Issue #591)</li>
    <li><I>File Formats</I>. Fixed an out-of-memory error in the CPIO file system. (GT-2912)</li>
    <li><I>File Formats</I>. DmgClientFileSystem no longer falsely matches zlib compressed files. (GT-2926, Issue #583)</li>
    <li><I>File System Browser</I>. Fixed NullPointerException when clicking <B>Get Info</B> on a directory in a zip file in the file system browser when the element was a directory that did not have a corresponding entry in the zip file. Changed the <B>Get Info</B> action to show information about both the highlighted file and any file system mounted from that file. (GT-2758)</li>
    <li><I>File System Browser</I>. Fixed dialog stacking problem in File System Browser when double-clicking a container file to open the filesystem inside it. (GT-2764)</li>
    <li><I>File System Browser</I>. Reduced the disk usage of the DYLD-shared cache file system. (GT-2887)</li>
    <li><I>Function Graph</I>. Fixed exception encountered when a Function Graph's entry node was put into a group node. (GT-3074)</li>
    <li><I>Function Graph</I>. Fixed Function Graph edge routing bug that sometimes caused edge flowing upward to route unexpectedly. (GT-3153, Issue #994)</li>
    <li><I>GUI</I>. Fixed stack trace when deleting large memory block that is in its own address space. (GT-2699)</li>
    <li><I>GUI</I>. Changed Data Type Preview to allow adding string data types. (GT-2832)</li>
    <li><I>GUI</I>. Fixed display of operand scalar values in tooltip popup of Decompiler and Listing windows. (GT-2836, Issue #120)</li>
    <li><I>GUI</I>. Fixed bug in Data Type Preview that caused a rendering error in Structures as primitive types were deleted. (GT-2844)</li>
    <li><I>GUI</I>. Fixed Symbol Tree ClassCastException that happened when clicking a node while the tree was still loading. (GT-2870, Issue #96)</li>
    <li><I>GUI</I>. Fixed bug that prevented the XRef's <B>Ref Type</B> column from sorting correctly. (GT-2892)</li>
    <li><I>GUI</I>. Fixed Listing bug so that the cursor gets restored to the previous location on Ghidra startup. (GT-2927, Issue #505)</li>
    <li><I>GUI</I>. Updated Edit Function Signature dialog to have focus in the signature field when first opened.  Also added undo/redo support. (GT-2947, Issue #635)</li>
    <li><I>GUI</I>. Fixed exception in the References Editor encountered when closing the editor with an active edit in the table. (GT-2951)</li>
    <li><I>GUI</I>. Fixed bug where the Ghidra menu mnemonic was not being set by the ampersand ('&') character in the last field of the menu path. (GT-2954)</li>
    <li><I>GUI</I>. Updated the Component Provider's <B>Close</B> button to allow for key bindings. (GT-2971, Issue #533)</li>
    <li><I>GUI</I>. Fixed tool navigation button enablement when using snapshot windows. (GT-2973)</li>
    <li><I>GUI</I>. Corrected Function Editor issue where parsed signature text resulted in incorrect type sizes which impacted custom storage selection.  Also added support for parsing signatures which reference types from an open datatype archive. (GT-3059)</li>
    <li><I>GUI</I>. Updated resizing in <B>Select Bytes</B> dialog. (GT-3072)</li>
    <li><I>GUI</I>. Fixed bug where listing would jump to random location when opening or closing a large structure or array. (GT-3088)</li>
    <li><I>GUI</I>. Fixed bug that caused some tables (e.g., the Symbol Table) to sort twice during their initial loading of data. (GT-3142)</li>
    <li><I>GUI</I>. Drag-and-Drop bug causing incorrect drop highlighting has been fixed. (GT-3219, Issue #1093)</li>
    <li><I>Help</I>. Fixed NullPointerException when navigating the Help UI. (GT-2830, Issue #493)</li>
    <li><I>Importer</I>. Fixed issues in the MapLoader that prevented <B>.map</B> files from being added to an existing program. (GT-2972, Issue #762)</li>
    <li><I>Importer</I>. For batch import, fixed issue where last character of directory name was truncated on Windows workstations. (GT-3012, Issue #797)</li>
    <li><I>Importer</I>. Fixed a bug in how the NE importer creates External Function symbols for the procedures it imports, allowing the decompiler to properly access any available information. (GT-3140, Issue #770)</li>
    <li><I>Importer</I>. Fixed a bug that prevented some old-style Windows executables from getting loaded by the MzLoader. (GT-3180, Issue #1054)</li>
    <li><I>Importer:ELF</I>. Added ELF relocation handler for <code>R_AARCH64_JUMP26</code>. (GT-2999, Issue #775)</li>
    <li><I>Importer:ELF</I>. Improved ELF MIPS support for GP-relative relocations encountered in PIC compiled binaries.  Also added support for <code>R_MIPS_RPREL32</code> relocation. (GT-3026, Issue #764)</li>
    <li><I>Importer:ELF</I>. ELF x86-64 relocations <code>R_X86_64_GOT32</code>, <code>R_X86_64_PLT32</code>, <code>R_X86_64_SIZE32</code>, <code>R_X86_64_SIZE64</code>, and <code>R_X86_64_GOTPC32</code> have been fixed to relocate correctly. Additional ELF x86-64 relocations, found mostly in unlinked <B>.o</B> files, have been added. (GT-3089, Issue #910)</li>
    <li><I>Importer:PE</I>. Fixed a problem in the PeLoader that would result in section names being incorrectly used as primary symbols.  This could result in function names being wrong. (GT-3195, Issue #761, #1051)</li>
    <li><I>Listing</I>. Fixed potential infinite loop when editing long comments. (GT-2824, Issue #437)</li>
    <li><I>Listing</I>. Fixed potential ClassCastException in Listing comments. (GT-3023)</li>
    <li><I>Listing</I>. Cursor in the listing now stays in the proper column after editing a field. (GT-3045, Issue #702)</li>
    <li><I>Listing</I>. Fixed a problem with register highlighting that could occur on certain register/sub-register combinations. (GT-3071, Issue #810)</li>
    <li><I>Multi-User</I>. Corrected terminate checkout from viewed checkout list which was always terminating first row range based upon number of selected rows and not the actual selected rows. (GT-2903)</li>
    <li><I>Multi-User</I>. Corrected ability for user to cancel checkin/checkout to Ghidra Server. (GT-3208)</li>
    <li><I>Multi-User:Ghidra Server</I>. Added proper Ghidra Server interface binding with new <code><B>-i</B></code> option.  Corrected <code><B>-ip</B></code> option to strictly convey remote access hostname to clients.  The updated server will only accept connections from Ghidra 9.1 and later clients due to the registry port now employing TLS. (GT-2685, Issue #101, #645)</li>
    <li><I>Multi-User:Ghidra Server</I>. Fixed argument-passing bug in svrAdmin script. (GT-3082, Issue #907)</li>
    <li><I>Multi-User:Merge</I>. Corrected merge problem affecting modified Function Definition datatypes which could result in a NullPointerException. (GT-2922)</li>
    <li><I>PDB</I>. Added <code>char16_t</code> and <code>char32_t</code> to PDB <code>BASIC_TYPE_STRINGS</code>. (GT-2952, Issue #685)</li>
    <li><I>PDB</I>. Addressed memory leaks and string handling issues in <B>pdb.exe</B>. (GT-2975, Issue #674, #597, #598, #599, #600)</li>
    <li><I>PDB</I>. Can now recover stack variables from more recent Visual Studio version PDBs. (GT-3014)</li>
    <li><I>PDB</I>. Fixed PDB validation logic, which caused a more severe error message to be created, masking the real issue. (GT-3209, Issue #198, #1024)</li>
    <li><I>Processors</I>. Utilized <code>FLOAT_NEG</code> pcodeop to simplify PowerPC <code>fneg</code> instructions. (GT-2781, Issue #387)</li>
    <li><I>Processors</I>. Added 6502 <code>I</code> status bit save and restore. (GT-2826, Issue #469)</li>
    <li><I>Processors</I>. Corrected alternate register definitions in z80 processor. (GT-2876, Issue #520)</li>
    <li><I>Processors</I>. Reviewed all processor modules for GhidraSleighEditor syntax errors. (GT-2902)</li>
    <li><I>Processors</I>. Added support for <code>RD</code>, <code>WR</code>, <code>FS</code>, and <code>GSBASE</code> instructions in x86. (GT-2940, Issue #554, #555)</li>
    <li><I>Processors</I>. Added fixes for sign extension of <code>ADD</code>, <code>AND</code>, <code>CMP</code>, and <code>SUB</code> instructions on x86-64bit. (GT-2955, Issue #881)</li>
    <li><I>Processors</I>. Updated PIC-30 division pcode to correct decompilation issue. (GT-3008)</li>
    <li><I>Processors</I>. Fixed x86 <code>AAM</code> instruction. (GT-3015)</li>
    <li><I>Processors</I>. Corrected x86 decode of <code>MOVBE</code> instruction. (GT-3039, Issue #822)</li>
    <li><I>Processors</I>. Corrected M68000 <code>mov3q</code> instruction decode and semantics. (GT-3080, Issue #905)</li>
    <li><I>Processors</I>. The JVM instruction <code>I2D</code> now correctly pushes an 8-byte double on the stack. (GT-3081)</li>
    <li><I>Processors</I>. Fixed problem displaying processor manuals in Windows Firefox. (GT-3084)</li>
    <li><I>Processors</I>. Encoding of <code>MOV</code> into debug registers has been relaxed. (GT-3117)</li>
    <li><I>Processors</I>. Corrected behavior of PowerPC <code>vectorPermute</code> pcodeop for emulation. (GT-3148)</li>
    <li><I>Processors</I>. Corrected MIPS relocation computation for <code>R_MIPS_26</code>, <code>R_MIPS16_26</code>, and <code>R_MICROMIPS_26_S1</code>. (GT-3154, Issue #1001)</li>
    <li><I>Processors</I>. Corrected the bit patterns for PowerPC VLE <code>rlwimi</code> and <code>rlwinm</code> instructions. (GT-3159, Issue #752)</li>
    <li><I>Processors</I>. Corrected instruction semantics for AARCH64 <code>BLR</code> instruction. (GT-3191)</li>
    <li><I>Processors</I>. Corrected fall-through override semantics for cases where pcode simply drops into the next address. (GT-3196, Issue #1083)</li>
    <li><I>Processors</I>. Corrected the semantics of the PowerPC <code>se_bmaski</code> instruction. (GT-3230, Issue #1123)</li>
    <li><I>Program API</I>. Corrected parameter storage which failed to properly refresh after undo/redo. (GT-3130, Issue #960)</li>
    <li><I>Program API</I>. Corrected function parameter ordinal numbering when more than one auto-parameter is present. (GT-3214)</li>
    <li><I>Project Manager</I>. Fixed a problem with creating Ghidra projects in Windows root directories (e.g., Z:\). (GT-2585)</li>
    <li><I>Project Manager</I>. Fixed a path-traversal vulnerability that could occur when restoring a malicious project archive. (GT-3001, Issue #789)</li>
    <li><I>Scripting</I>. <code>GhidraScript.askDomainFile()</code> now correctly throws a CancelledException when the cancel button is clicked. (GT-2841)</li>
    <li><I>Scripting</I>. Removed deprecated scripting methods older than 5 releases. (GT-2949)</li>
    <li><I>Security</I>. Removed use of nonsecure XMLEncoder/XMLDecoder from Ghidra code base. (GT-3198, Issue #1090)</li>
    <li><I>Sleigh</I>. Corrected Sleigh compiler bug which performed improper bounds checking for named register offset specification when space wordsize is not one (1). (GT-3034, Issue #831)</li>
    <li><I>Testing:CUnits</I>. Fixed error logging in pcodetest for reporting an error when running a compile command. (GT-3199, Issue #1089)</li>
    <li><I>Version Tracking</I>. Fixed NullPointerException in Version Tracking hashing algorithm. (GT-2976)</li>
</ul>
</blockquote>

<H1 align="center">Ghidra 9.0.4 Change History (May 2019)</H1>
<blockquote><p><u>Bugs</u></p>
<ul>
    <li><I>Multi-User:Ghidra Server</I>. Corrected severe script error in svrAdmin.bat introduced with 9.0.3 build. (GT-2874)</li>
    <li><I>GUI</I>. Restored the default 'p' key binding for creating pointers within the listing display. (GT-2854)</li>
</ul>
</blockquote>

<H1 align="center">Ghidra 9.0.3 Change History (April 2019)</H1>
<blockquote><p><u>New Features</u></p>
<ul>
    <li><I>GUI</I>. Function tags are now viewable from Functions Window table using new column. (GT-2114)</li>
</ul>
</blockquote>

<blockquote><p><u>Improvements</u></p>
<ul>
    <li><I>Decompiler</I>. Improved modeling of CFG on Windows 10. (GT-2755, Issue #340)</li>
    <li><I>Patcher</I>. Renamed patch directory to <install dir>/Ghidra/patch and added README.txt that explains how the patch directory is used. (GT-2734)</li>
    <li><I>Search</I>. Updated the Decompiler Data Type Finder to find references inside of nested array access in a line of Decompiler C output. (GT-2756, Issue #416)</li>
    <li><I>Sleigh</I>. Improved error reporting for SLEIGH compiler. (GT-2820, Issue #364)</li>
</ul>
</blockquote>

<blockquote><p><u>Bugs</u></p>
<ul>
    <li><I>Analysis</I>. Code that checks for thunks no longer throws an exception if the PC is not set for the processor. (GT-2730)</li>
    <li><I>Analysis</I>. Made a fix to enable Apply button when changing tool options. (GT-2801, Issue #40)</li>
    <li><I>Data Types</I>. Fixed concurrent modification exception when replacing one datatype for another that results in some other datatype being renamed. (GT-2736)</li>
    <li><I>Decompiler</I>. Fixed dynamic variables and equates in 16-bit x86 programs. (GT-2745, Issue #336)</li>
    <li><I>Decompiler:Java</I>. Fixed DEX decompilation regression issue. (GT-2743, Issue #350)</li>
    <li><I>Eclipse Integration</I>. Fixed exception in Eclipse GhidraDev plugin that occurred when performing certain actions on a Ghidra project that was imported from a previously exported Archive File. (GT-2721, Issues #283, #383)</li>
    <li><I>GUI</I>. Improved documentation on how to deal with HiDPI monitor issues in Linux. In the <I>&lt;ghidra_installation&gt;</I>/support/launch.properties file, change VMARGS=-Dsun.java2d.xrender from false to true.</li> 
    <li><I>Importer</I>. Fixed an exception that occurred when batch importing APK files. (GT-2767, Issue #426)</li>
    <li><I>Multi-User:Ghidra Server</I>. Restored ability to execute svrAdmin script in development mode. (GT-2740) </li>
    <li><I>Processors</I>. The 6502 Zero page indexed addressing has been corrected to only access the Zero page. (GT-2759, Issue #201)</li>
    <li><I>Processors</I>. The M68000 BCD arithmetic instructions now have pcode semantics that allow disassembly to continue. (GT-2807, Issue #227)</li>
    <li><I>Search</I>. Fixed NullPointerException in Decompiler Data Type Reference Finder. (GT-2754, Issue #407)</li>
</ul>
</blockquote>

<H1 align="center">Ghidra 9.0.2 Change History (April 2019)</H1>
<blockquote><p><u>Bugs</u></p>
<ul>
    <li><I>Analysis</I>. Constant reference analysis boundary controls for speculative references has been fixed. Speculative references are references created from computed constants passed as parameters, stored to a location, or from indexed offsets from a register. (GT-2723, Issue #228)</li>
    <li><I>Decompiler</I>. Fixed Decompiler handling of Function Definition data types. (GT-2704, Issue #247)</li>
    <li><I>Decompiler</I>. Fixed rendering bug in the Decompiler when the "Find" dialog is closed. (GT-2716, Issue #282)</li>
    <li><I>Decompiler</I>. Fixed "Free Varnode" exception in RuleConditionalMove. (GT-2726, Issue #294)</li>
    <li><I>Diff</I>. Fixed exceptions that can occur in the Diff View for programs with overlays. (GT-2706)</li>
    <li><I>Documentation</I>. Corrected the spelling of "listener" throughout the source code. (GT-2702, Issue #235)</li>
    <li><I>Exporter</I>. Exporting a selection as Intel Hex will now allow a selection of any length. Previously this was restricted to multiples of 16 bytes. (GT-2703, Issue #260)</li>
    <li><I>GUI</I>. Fixed exception that occurs after disabling MyProgramChangesDisplayPlugin. (GT-2712)</li>
    <li><I>GUI</I>. Updated the "Open Program" dialog to disallow file drop operations. (GT-2705, Issue #252)</li>
    <li><I>Multi-User:Ghidra Server</I>. Corrected bug introduced into ghidraSvr.bat which could prevent Ghidra Server startup. (GT-2717, Issue #279)</li>
    <li><I>Processors</I>. The ARM Thumb CMP.W and LSL instructions have been changed to correctly decode. There are still issues to work out with Unpredictable execution when Rd is the PC. (GT-2722, Issue #280)</li>
    <li><I>Scripting</I>. MultiInstructionMemReference script has been corrected to consider input and output registers when placing a reference on an instruction. (GT-2723)</li>
</ul>
</blockquote>

<blockquote><p><u>Security</u></p>
<ul>
    <li><I>Basic Infrastructure</I>. Added a property to support/launch.properties to prevent log4j from using jansi.dll on Windows. (GT-2725, Issue #286)</li>
</ul>
</blockquote>

<H1 align="center">Ghidra 9.0.1 Change History (March 2019)</H1>
<blockquote><p><u>New Features</u></p>
<ul>
    <li><I>Scripting</I>. Created ShowEquatesInSelectionScript to show all equates within the current selection. (GT-2651, Issue #111)</li>
</ul>
</blockquote>

<blockquote><p><u>Improvements</u></p>
<ul>
    <li><I>Basic Infrastructure</I>. Updated commons-compress library to version 1.18. (GT-2657, Issue #171)</li>
    <li><I>Eclipse Integration</I>. Ghidra now connects to the Eclipse GhidraDev plugin on 127.0.0.1 rather than localhost. (GT-2691)</li>
    <li><I>GUI</I>. Turned on font anti-aliasing by default for Linux. (GT-2674, Issue #212)</li>
    <li><I>GUI</I>. Fixed Options Dialog slow scrolling speed. (GT-2679, Issue #27)</li>
    <li><I>Importer:ELF</I>. Corrected bug in ELF loader which can improperly process the GOT, PLT and relocations when multiple symbol tables exist within the ELF binary. (GT-2646, Issue #52)</li>
    <li><I>Multi-User:Ghidra Server</I>. Corrected the Ghidra Server service wrapper (YAJSW) configuration for Mac OS X to prevent a startup timeout condition which could occur. (GT-2637)</li>
    <li><I>Processors</I>. Added ARM/Thumb SRS instruction decodes for undefined modes. (GT-2676, Issue #216)</li>
</ul>
</blockquote>

<blockquote><p><u>Bugs</u></p>
<ul>
    <li><I>API</I>. Fixed equals method on Varnode class. (GT-2648, Issue #97)</li>
    <li><I>API</I>. Fixed a bug in MaskImpl.complementMask(). (GT-2694, Issue #187)</li>
    <li><I>Basic Infrastructure</I>. Fixed special character handling in idaxml.py. (GT-2669, Issue #75)</li>
    <li><I>Basic Infrastructure</I>. Ghidra now forces the locale to en_US by default. Only the en_US is currently supported. This fixes certain unexpected exceptions. (GT-2680, Issue #209)</li>
    <li><I>Diff</I>. Fixed exception occasionally encountered when starting a Diff session. (GT-2672, Issue #211)</li>
    <li><I>Documentation</I>. Fixed javadoc search box redirecting to broken links. (GT-2655, Issue #129)</li>
    <li><I>Function Graph</I>. Fixed Function Graph exception when generating tooltip. (GT-2650, Issue #65)</li>
    <li><I>GUI</I>. Updated window placement to keep windows on screen. (GT-1516, Issue #41)</li>
    <li><I>GUI</I>. Add/Edit References dialog now restricts users to creating refs in valid memory address spaces. (GT-2638)</li>
    <li><I>GUI</I>. Fixed exception when exiting Ghidra while a table is being edited. (GT-2642, Issue #51)</li>
    <li><I>GUI</I>. Fixed some touchpad scrolling issues. (GT-2647, Issue #2)</li>
    <li><I>GUI</I>. Fixed stack trace in the Data Type Manager's tooltip generation. (GT-2656, Issue #133)</li>
    <li><I>GUI</I>. User key binding settings for the Recently Used and Define Pointer actions no longer lost after re-launching tool. (GT-2659, Issue #152)</li>
    <li><I>GUI</I>. Toolbar buttons now respond to fast clicking. (GT-2689)</li>
    <li><I>Importer:Mach-O</I>. The Mach-O loader can now find import libraries found in Universal Binary files. (GT-2663, Issue #136)</li>
    <li><I>Importer:PE</I>. The PeLoader now correctly parses the GuardCFFunctionTable when table entries are more than 4 bytes each. (GT-2671, Issue #220)</li>
    <li><I>Multi-User:Ghidra Server</I>. Removed support for native OS authentication from Ghidra Server (removed modes -a2 and -a3) due to incompatibility with newer OS releases including Windows 10 and Windows Server 2016. Re-introduction of this will be considered for a future release. (GT-2653)</li>
    <li><I>PDB</I>. Corrected NullPointerException when processing PDB files. (GT-2673, Issues #138, #188)</li>
    <li><I>Processors</I>. Added missing PowerPC VLE conditional branch instructions: e_bdnz and e_bdz. (GT-2652, Issue #103)</li>
    <li><I>Processors</I>. Fixed instruction semantics for several instructions and added Control Flow Enforcement, NOP variants, CMP variants, UD1, and prefixed call instructions to X86 processor specification. (GT-2660, Issues #22, #53, #158, #157)</li>
    <li><I>Processors</I>. The M68000 MOVE instruction now correctly sets the CF and VF flags. (GT-2661, Issue #163)</li>
    <li><I>Processors</I>. Added four missing MOVEM instruction variants to the M68000 processor. (GT-2675, Issue  #219)</li>
    <li><I>Processors</I>. An incorrect usage of X instead of Y in indexed mode for the 6502 has been corrected. (GT-2677, Issue #201)</li>
    <li><I>Processors</I>. PPC VLE now disassembles base PPC instructions that are valid in VLE mode. (GT-2681, Issue #127)</li>
    <li><I>Processors</I>. Added support for ARM Thumb half BL instruction on processor variants prior to v6. (GT-2684, Issue #39)</li>
    <li><I>Scripting</I>. Fixed a bug in ImportSymbolsScript.py that prevented it from running. (GT-2668, Issue #170)</li>
</ul>
</blockquote>

<blockquote><p><u>Security</u></p>
<ul>
    <li><I>Basic Infrastructure</I>. Running Ghidra in debug mode no longer opens remotely accessible ports by default. (GT-2641, Issue #6)</li>
    <li><I>GUI</I>. The Defined Strings plugin no longer renders HTML in its table. (GT-2686, Issue #45)</li>
    <li><I>Project Manager</I>. Fixed an XXE vulnerability affecting projects and many other saved components. (GT-2643, Issue #71)</li>
</ul>
</blockquote>

</BODY>
</HTML>

