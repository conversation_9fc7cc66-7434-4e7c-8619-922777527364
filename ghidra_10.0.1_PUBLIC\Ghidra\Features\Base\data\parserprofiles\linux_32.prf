types.h
stddef.h
stddef.h
nmmintrin.h
syslimits.h
acct.h
acl.h
bitypes.h
epoll.h
errno.h
file.h
fsuid.h
kd.h
mman.h
mount.h
msg.h
mtio.h
param.h
pci.h
personality.h
poll.h
prctl.h
procfs.h
profil.h
raw.h
sem.h
sendfile.h
shm.h
signalfd.h
socketvar.h
soundcard.h
statvfs.h
syscall.h
sysctl.h
syslog.h
termios.h
timeb.h
timerfd.h
times.h
timex.h
ttychars.h
un.h
ustat.h
utsname.h
vt.h
wait.h
xattr.h
acl/libacl.h
aio.h
aliases.h
argp.h
asm-generic/int-l64.h
asm-generic/posix_types.h
asm-generic/sembuf.h
asm-generic/signal.h
asm-generic/stat.h
asm-generic/swab.h
asm-generic/unistd.h
asm/bootparam.h
asm/mce.h
asm/msr.h
asm/mtrr.h
asm/sigcontext.h
asm/sigcontext32.h
asm/statfs.h
asm/ucontext.h
asm/vm86.h
attr/xattr.h
bits/msq.h
bits/shm.h
bits/utmpx.h
bits/utsname.h
bzlib.h
dis-asm.h
ecpgerrno.h
elfutils/libasm.h
elfutils/libdw.h
elfutils/libdwfl.h
elfutils/libebl.h
envz.h
execinfo.h
fmtmsg.h
fpu_control.h
fstab.h
ftw.h
gcrypt.h
gettext-po.h
glob.h
gnu-versions.h
gnu/lib-names.h
gnu/libc-version.h
gshadow.h
iconv.h
idna.h
ieee754.h
ifaddrs.h
jpeglib.h
keyutils.h
krb5/kadm5_hook_plugin.h
krb5/localauth_plugin.h
krb5/locate_plugin.h
krb5/preauth_plugin.h
krb5/pwqual_plugin.h
langinfo.h
lastlog.h
ldap_schema.h
ldap_utf8.h
ldif.h
libaudit.h
libgen.h
libiberty.h
libpng12/png.h
libpng12/pngconf.h
linux/a.out.h
linux/acct.h
linux/adfs_fs.h
linux/affs_hardblocks.h
linux/agpgart.h
linux/aio_abi.h
linux/atm_eni.h
linux/atm_he.h
linux/atm_idt77105.h
linux/atm_nicstar.h
linux/atm_tcp.h
linux/atm_zatm.h
linux/atmarp.h
linux/atmbr2684.h
linux/atmclip.h
linux/atmlec.h
linux/atmmpc.h
linux/atmppp.h
linux/atmsvc.h
linux/auto_fs4.h
linux/auxvec.h
linux/ax25.h
linux/bfs_fs.h
linux/binfmts.h
linux/blkpg.h
linux/blktrace_api.h
linux/bpqether.h
linux/bsg.h
linux/byteorder/big_endian.h
linux/can/raw.h
linux/can/bcm.h
linux/can/netlink.h
linux/capi.h
linux/cdrom.h
linux/cgroupstats.h
linux/cm4000_cs.h
linux/cn_proc.h
linux/coda.h
linux/comstats.h
linux/connector.h
linux/cramfs_fs.h
linux/cyclades.h
linux/dcbnl.h
linux/dccp.h
linux/dlm_device.h
linux/dlm_netlink.h
linux/dlm_plock.h
linux/dm-log-userspace.h
linux/dn.h
linux/dqblk_xfs.h
linux/dvb/audio.h
linux/dvb/dmx.h
linux/dvb/frontend.h
linux/dvb/net.h
linux/dvb/video.h
linux/efs_fs_sb.h
linux/elf-fdpic.h
linux/elfcore.h
linux/errqueue.h
linux/eventpoll.h
linux/ext2_fs.h
linux/fb.h
linux/fd.h
linux/fib_rules.h
linux/fiemap.h
linux/filter.h
linux/firewire-cdev.h
linux/fuse.h
linux/futex.h
linux/gen_stats.h
linux/genetlink.h
linux/gfs2_ondisk.h
linux/gigaset_dev.h
linux/hdreg.h
linux/hiddev.h
linux/hidraw.h
linux/i2c-dev.h
linux/i2o-dev.h
linux/icmp.h
linux/icmpv6.h
linux/if_addrlabel.h
linux/if_arcnet.h
linux/if_arp.h
linux/if_bonding.h
linux/if_bridge.h
linux/if_fc.h
linux/if_frad.h
linux/if_hippi.h
linux/if_plip.h
linux/if_ppp.h
linux/if_strip.h
linux/if_tr.h
linux/if_tun.h
linux/if_tunnel.h
linux/igmp.h
linux/inet_diag.h
linux/inotify.h
linux/ip.h
linux/ip6_tunnel.h
linux/ip_vs.h
linux/ipmi.h
linux/ipsec.h
linux/ipv6.h
linux/ipv6_route.h
linux/irda.h
linux/isdn.h
linux/iso_fs.h
linux/ivtv.h
linux/ivtvfb.h
linux/ixjuser.h
linux/jffs2.h
linux/joystick.h
linux/keyboard.h
linux/kvm.h
linux/kvm_para.h
linux/loop.h
linux/lp.h
linux/map_to_7segment.h
linux/matroxfb.h
linux/mempolicy.h
linux/mii.h
linux/mman.h
linux/mroute.h
linux/msdos_fs.h
linux/msg.h
linux/mtio.h
linux/nbd.h
linux/ncp_fs.h
linux/ncp_mount.h
linux/net.h
linux/net_dropmon.h
linux/net_tstamp.h
linux/netfilter/nf_conntrack_sctp.h
linux/netfilter/nf_conntrack_tcp.h
linux/netfilter/nfnetlink_conntrack.h
linux/netfilter/nfnetlink_log.h
linux/netfilter/nfnetlink_queue.h
linux/netfilter/xt_AUDIT.h
linux/netfilter/xt_CHECKSUM.h
linux/netfilter/xt_CLASSIFY.h
linux/netfilter/xt_CONNSECMARK.h
linux/netfilter/xt_LED.h
linux/netfilter/xt_MARK.h
linux/netfilter/xt_NFLOG.h
linux/netfilter/xt_NFQUEUE.h
linux/netfilter/xt_SECMARK.h
linux/netfilter/xt_connbytes.h
linux/netfilter/xt_connmark.h
linux/netfilter/xt_conntrack.h
linux/netfilter/xt_dccp.h
linux/netfilter/xt_esp.h
linux/netfilter/xt_hashlimit.h
linux/netfilter/xt_iprange.h
linux/netfilter/xt_length.h
linux/netfilter/xt_limit.h
linux/netfilter/xt_multiport.h
linux/netfilter/xt_osf.h
linux/netfilter/xt_owner.h
linux/netfilter/xt_physdev.h
linux/netfilter/xt_policy.h
linux/netfilter/xt_rateest.h
linux/netfilter/xt_recent.h
linux/netfilter/xt_sctp.h
linux/netfilter/xt_statistic.h
linux/netfilter/xt_string.h
linux/netfilter/xt_tcpmss.h
linux/netfilter_arp/arpt_mangle.h
linux/netfilter_bridge/ebtables.h
linux/nfs4_mount.h
linux/nfs_fs.h
linux/nfs_idmap.h
linux/nfsd/debug.h
linux/nfsd/nfsfh.h
linux/nfsd/stats.h
linux/nfsd/syscall.h
linux/nl80211.h
linux/nubus.h
linux/nvram.h
linux/perf_event.h
linux/phantom.h
linux/phonet.h
linux/pktcdvd.h
linux/pmu.h
linux/poll.h
linux/ppdev.h
linux/pps.h
linux/ptp_clock.h
linux/qnx4_fs.h
linux/quota.h
linux/radeonfb.h
linux/raid/md_p.h
linux/random.h
linux/raw.h
linux/reiserfs_fs.h
linux/reiserfs_xattr.h
linux/resource.h
linux/rfkill.h
linux/route.h
linux/selinux_netlink.h
linux/sem.h
linux/serial_core.h
linux/serial_reg.h
linux/serio.h
linux/shm.h
linux/signalfd.h
linux/smb_fs.h
linux/smb_mount.h
linux/som.h
linux/sonypi.h
linux/sound.h
linux/spi/spidev.h
linux/suspend_ioctls.h
linux/synclink.h
linux/tc_act/tc_gact.h
linux/tc_act/tc_ipt.h
linux/tc_act/tc_mirred.h
linux/tc_act/tc_nat.h
linux/tc_act/tc_pedit.h
linux/tc_act/tc_skbedit.h
linux/tc_ematch/tc_em_cmp.h
linux/tc_ematch/tc_em_meta.h
linux/tc_ematch/tc_em_nbyte.h
linux/tc_ematch/tc_em_text.h
linux/tcp.h
linux/termios.h
linux/times.h
linux/timex.h
linux/tipc.h
linux/tipc_config.h
linux/udp.h
linux/uinput.h
linux/uio.h
linux/unistd.h
linux/usb/audio.h
linux/usb/cdc.h
linux/usb/gadgetfs.h
linux/usb/midi.h
linux/usbdevice_fs.h
linux/utime.h
linux/uuid.h
linux/vhost.h
linux/videodev.h
linux/watchdog.h
linux/wimax.h
linux/wimax/i2400m.h
linux/wireless.h
linux/x25.h
linux/xfrm.h
mntent.h
monetary.h
mtd/inftl-user.h
mtd/mtd-user.h
mtd/nftl-user.h
mtd/ubi-user.h
net/if_packet.h
net/if_ppp.h
net/if_shaper.h
net/if_slip.h
net/ppp-comp.h
net/route.h
netash/ash.h
netatalk/at.h
neteconet/ec.h
netinet/ether.h
netinet/icmp6.h
netinet/if_fddi.h
netinet/if_tr.h
netinet/igmp.h
netinet/ip6.h
netinet/ip_icmp.h
netinet/udp.h
netipx/ipx.h
netiucv/iucv.h
nss.h
obstack.h
openssl/opensslconf.h
openssl/bn.h
openssl/asn1_mac.h
openssl/asn1t.h
openssl/blowfish.h
openssl/camellia.h
openssl/cast.h
openssl/cmac.h
openssl/cms.h
openssl/conf_api.h
openssl/des.h
openssl/dso.h
openssl/engine.h
openssl/fips_rand.h
openssl/idea.h
openssl/krb5_asn.h
openssl/md2.h
openssl/md4.h
openssl/md5.h
openssl/ocsp.h
openssl/pkcs12.h
openssl/rc2.h
openssl/rc4.h
openssl/ripemd.h
openssl/seed.h
openssl/ts.h
openssl/txt_db.h
openssl/whrlpool.h
pr29.h
profile.h
protocols/routed.h
protocols/rwhod.h
protocols/talkd.h
protocols/timed.h
pty.h
punycode.h
rdma/ib_user_cm.h
rdma/ib_user_mad.h
rdma/rdma_netlink.h
rdma/rdma_user_cm.h
re_comp.h
readline/history.h
readline/readline.h
regexp.h
resolv.h
rpc/des_crypt.h
rpc/key_prot.h
rpc/pmap_clnt.h
rpc/pmap_prot.h
rpc/pmap_rmt.h
rpc/rpc_des.h
sasl/md5global.h
scsi/scsi.h
scsi/sg.h
security/pam_ext.h
security/pam_filter.h
security/pam_misc.h
security/pam_modules.h
security/pam_modutil.h
selinux/avc.h
selinux/get_context_list.h
selinux/label.h
shadow.h
slapi-plugin.h
sqlite3ext.h
stdio_ext.h
stringprep.h
sys/acct.h
sys/debugreg.h
sys/epoll.h
sys/eventfd.h
sys/fcntl.h
sys/fsuid.h
sys/gmon.h
sys/gmon_out.h
sys/inotify.h
sys/io.h
sys/kd.h
sys/kdaemon.h
sys/klog.h
sys/mount.h
sys/mtio.h
sys/pci.h
sys/perm.h
sys/personality.h
sys/prctl.h
sys/profil.h
sys/ptrace.h
sys/quota.h
sys/raw.h
sys/reboot.h
sys/reg.h
sys/sem.h
sys/sendfile.h
sys/signal.h
sys/signalfd.h
sys/socketvar.h
sys/soundcard.h
sys/statvfs.h
sys/swap.h
sys/sysctl.h
sys/sysinfo.h
sys/termios.h
sys/timerfd.h
sys/ttychars.h
sys/ultrasound.h
sys/unistd.h
sys/ustat.h
sys/vfs.h
sys/vlimit.h
sys/vt.h
sys/vtimes.h
sys/xattr.h
term_entry.h
termcap.h
tgmath.h
thread_db.h
tld.h
ttyent.h
uapi/nvme.h
ulimit.h
wordexp.h
xf86drmMode.h
gssapi_generic.h
gssapi_krb5.h
mechglue.h
auth_gss.h
auth_gssapi.h

-I/linux/include
-I/linux/include/sys
-I/linux/gcc/include
-I/linux/x86_64-redhat-linux5E/include
-I/linux/x86_64-redhat-linux5E/include/sys
-D_X86_
-D__STDC__
-D_POSIX_C_SOURCE
-D_BSD_SOURCE
-D__WORDSIZE=32
-D__builtin_va_list=void *
-D__DO_NOT_DEFINE_COMPILE
-D_Complex
-D_WCHAR_T
-D__NO_STRING_INLINES
-D__NO_LONG_DOUBLE_MATH
-D__signed__
-D__extension__=""
-D_Bool="bool"
-D__GLIBC_HAVE_LONG_LONG=1
-Daligned_u64=uint64_t
