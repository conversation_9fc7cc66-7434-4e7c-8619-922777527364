wrapper.java.classpath.1=${ghidra_home}/Ghidra/Features/GhidraServer/lib/GhidraServer.jar
wrapper.java.classpath.2=${ghidra_home}/Ghidra/Framework/FileSystem/lib/FileSystem.jar
wrapper.java.classpath.3=${ghidra_home}/Ghidra/Framework/DB/lib/DB.jar
wrapper.java.classpath.4=${ghidra_home}/Ghidra/Framework/Docking/lib/Docking.jar
wrapper.java.classpath.5=${ghidra_home}/Ghidra/Framework/Generic/lib/Generic.jar
wrapper.java.classpath.6=${ghidra_home}/Ghidra/Framework/FileSystem/lib/ganymed-ssh2-262.jar
wrapper.java.classpath.7=${ghidra_home}/Ghidra/Framework/Utility/lib/Utility.jar
wrapper.java.classpath.8=${ghidra_home}/Ghidra/Framework/Generic/lib/cglib-nodep-2.2.jar
wrapper.java.classpath.9=${ghidra_home}/Ghidra/Framework/Generic/lib/guava-19.0.jar
wrapper.java.classpath.10=${ghidra_home}/Ghidra/Framework/Generic/lib/jdom-legacy-1.1.3.jar
wrapper.java.classpath.11=${ghidra_home}/Ghidra/Framework/Generic/lib/log4j-core-2.12.1.jar
wrapper.java.classpath.12=${ghidra_home}/Ghidra/Framework/Generic/lib/log4j-api-2.12.1.jar
wrapper.java.classpath.13=${ghidra_home}/Ghidra/Framework/Generic/lib/commons-collections4-4.1.jar
wrapper.java.classpath.14=${ghidra_home}/Ghidra/Framework/Generic/lib/commons-lang3-3.9.jar
wrapper.java.classpath.15=${ghidra_home}/Ghidra/Framework/Generic/lib/commons-text-1.6.jar
wrapper.java.classpath.16=${ghidra_home}/Ghidra/Framework/Generic/lib/commons-io-2.6.jar
wrapper.java.classpath.17=${ghidra_home}/Ghidra/Framework/Generic/lib/gson-2.8.6.jar
wrapper.java.classpath.18=${ghidra_home}/Ghidra/Framework/Docking/lib/timingframework-1.0.jar
wrapper.java.classpath.19=${ghidra_home}/Ghidra/Framework/Docking/lib/javahelp-2.0.05.jar
