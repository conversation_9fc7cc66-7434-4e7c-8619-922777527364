/* ###
 * IP: GHIDRA
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * 
 *      http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
//Changes the dsiplay settings of the current data 
//from hex to decimal.
//@category Examples

import ghidra.app.script.GhidraScript;
import ghidra.docking.settings.FormatSettingsDefinition;
import ghidra.docking.settings.SettingsDefinition;
import ghidra.program.model.listing.Data;

public class ChangeDataSettingsScript extends GhidraScript {

    @Override
    public void run() throws Exception {
    	Data data = getDataAt(currentAddress);
    	if (data == null) {
    	    println("No data exists.");
    	    return;
    	}
    	int componentCount = data.getNumComponents();
    	if (componentCount == 0) {
    	    println("Data does not have any sub-components.");
    	    return;
    	}
    	for (int i = 0 ; i < componentCount ; ++i) {
    		Data component = data.getComponent( i );
    		if ( component == null ) {
    			break; // something bad and unexpected has happened
    		}
    		SettingsDefinition [] settings = component.getDataType().getSettingsDefinitions();
    		for (int j = 0; j < settings.length; j++) {
                if (settings[j] instanceof FormatSettingsDefinition) {
                    FormatSettingsDefinition format = (FormatSettingsDefinition)settings[j];
                    format.setChoice(component, FormatSettingsDefinition.DECIMAL);
                }
            }
    	}
    }
}
