/* ###
 * IP: GHIDRA
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * 
 *      http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include<stdlib.h>

typedef struct {
    long a;
    int b;
    char * c;
    short d;
} exampleStruct;

typedef exampleStruct * exampleStructPtr;


void setFirstAndThird(exampleStructPtr ptr, long x, char *str){
    ptr->a = x;
    ptr->c = str;
}

void setSecondAndFourth(exampleStructPtr ptr, int y, short z){
    ptr->b = y;
    ptr->d = z;
}
