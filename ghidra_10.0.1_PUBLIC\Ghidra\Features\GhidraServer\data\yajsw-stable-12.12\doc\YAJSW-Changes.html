<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>


















  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <meta content="text/html; charset=ISO-8859-1" http-equiv="content-type">

















  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <title>YAJSW -Changes</title>
</head>


<body>


















<div style="text-align: center;">
<table style="text-align: left; width: 1530px; height: 32px;" border="0" cellpadding="0" cellspacing="0">


















  <tbody>


















    <tr>


















      <td style="width: 33%;"><a href="http://sourceforge.net/projects/yajsw"><img style="border: 0px solid ; width: 150px; height: 40px;" src="http://sflogo.sourceforge.net/sflogo.php?group_id=224503&amp;type=15" alt="Get Yet Another Java Service Wrapper at SourceForge.net. Fast, secure and Free Open Source software downloads"></a></td>


















      <td style="width: 33%; text-align: center;">
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <h1>YAJSW - Changes</h1>


















      </td>


















      <td style="width: 33%;">&nbsp; </td>


















    </tr>


















  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </tbody>
</table>


















<div style="text-align: left;">
<h4>yajsw-stable-12.12</h4>









<ul>


  <li><a href="https://sourceforge.net/p/yajsw/bugs/140/">Bug</a>: Windows Java 9 won't Run via YAJSW: updated jna</li>


  <li><a href="https://sourceforge.net/p/yajsw/bugs/142/">Bug</a>: UAC elevation doesn't remember setting of wrapper.home: updated setenv.bat</li>


  <li><a href="https://sourceforge.net/p/yajsw/bugs/143/">New:</a>
&nbsp;Removing symbolic links configured by chkconfig: new
configuration properties: <span style="font-weight: bold; font-style: italic;">chkconfig_start_priority,
chkconfig_stop_priority</span>, updated &nbsp;daemon.vm</li>


  <li><a href="https://sourceforge.net/p/yajsw/bugs/144/">Update</a>: API is Missing an Interface: updated documenation</li>

  <li><a href="https://sourceforge.net/p/yajsw/bugs/145/">Bug</a>: wrapper doesn't delete pidfile</li>

  <li><a href="https://sourceforge.net/p/yajsw/discussion/810311/thread/9445a6cc/">New</a>: How to rotate and compress logs. new configuration property: <span style="font-weight: bold; font-style: italic;">wrapper.logfile.compress&nbsp;</span>compress older file when rotating</li>
  <li><a href="https://sourceforge.net/p/yajsw/yajsw/merge-requests/5/">Change</a>: Small fixes/improvements to the Bash scripts. thanks to&nbsp;            deliriumsky for the patch</li>
  <li><a href="https://sourceforge.net/p/yajsw/yajsw/merge-requests/6/">New</a>: Support for systemd: installDaemonD.sh, installDaemonNoPrivD.sh, uninstallDaemonD.sh, thanks to             deliriumsky for the patch</li>


</ul>


<h4>yajsw-stable-12.11 Oct 10th 2017</h4>









<ul>



  <li>Bug: windows:&nbsp;<span style="font-weight: bold; font-style: italic;">wrapper.affinity</span><em> </em>should support up to 64 processors</li>



  <li>Bug: wrong yajsw version logged</li>



</ul>



<h4>yajsw-stable-12.10 Oct 3rd 2017</h4>









<ul>






  <li>New: <span style="font-style: italic; font-weight: bold;">wrapper.ping.check_ack</span> property for bidirectional ping check</li>






  <li>New: <span style="font-style: italic; font-weight: bold;">wrapper.delay_shutdown</span> property. Delay shutdown to allow scripts, such as emails to terminate.</li>






  <li>Bug: bad manifest</li>






  <li>Bug: windows:&nbsp;<span style="font-weight: bold; font-style: italic;">wrapper.affinity</span><em> </em>should support up to 64 processors</li>





  <li>Bug: NPE when executing shell script</li>





  <li>Bug: Posix: cannot start process if wrapper.java.additional contains blank</li>




  <li>Bug: Cannot start jboss wildfily since 12.09: groovy-patch: disable java logging.</li>





  <li>Change: do not show messages "INFO: lib not found" for extended libs.</li>






</ul>






<h4>yajsw-stable-12.09 &nbsp;Mai 28th 2017</h4>









<ul>







  <li>&nbsp;Change: do not expose internaly required ports to the
network. use localhost loopback. @see:
https://sourceforge.net/p/yajsw/bugs/132/</li>







  <li>&nbsp;Change: tray open console performance.</li>







  <li>&nbsp;Change: upgrade beanutils. @see https://sourceforge.net/p/yajsw/bugs/131/</li>







  <li>&nbsp;Change: jvm controller: ignore key message if we already received correct key.</li>







  <li>&nbsp;Change: update tp netty-all-4.0.47. @see: https://sourceforge.net/p/yajsw/support-requests/25/</li>







  <li>&nbsp;Change: update to groovy-2.4.11 @see https://sourceforge.net/p/yajsw/support-requests/26/</li>







  <li>&nbsp;Bug: communication between wrapper and app is not logged</li>







  <li>&nbsp;Bug: @see https://sourceforge.net/p/yajsw/discussion/810311/thread/4dad0e51/. Update to jna-4.2.2</li>







  <li>&nbsp;Bug: posix JAVA_HOME bug. @see https://sourceforge.net/p/yajsw/patches/12/</li>







</ul>







<h4>yajsw-stable-12.08 January 14th 2017</h4>









<ul>









  <li>Bug: Unix supplemental groups</li>









  <li>Bug: setting jna_tmp has no effect</li>









  <li>Change: update to jna-4.2.2 + aix64 lib</li>









</ul>











<h4>yajsw-beta-12.07 October 23rd 2016</h4>











<ul>











  <li>Bug: Cannot specify colon in wrapper.console.title</li>











  <li>Bug: error in AlphaNumericComparator when sorting parameters and classpath</li>











  <li>Bug: posix: env vars are not inherited by subprocess if created with posix_spawn</li>











  <li>Bug: configuration include does not work</li>











  <li>Bug: configuration does not correctly read lists</li>











  <li>Change: support working dir with non latin chars</li>











  <li>Change: windows: change drive or UNC path when executing batch file</li>











</ul>












<h4>yajsw-beta-12.06 May 8th 2016</h4>












<ul>












  <li>Change: libs: netty-all-4.0.35.Final, commons-cli-1.3.1, commons-collections-3.2.2, commons-configuration2-2.0</li>












  <li>Bug: No application logs on yajsw 11.11 / Ubuntu 14.04</li>












  <li>Bug: Windows 10 DELAYED_AUTO_START<br>












  </li>












</ul>












<h4>yajsw-beta-12.05 Mars 13th 2016</h4>


















<ul>












  <li style="font-weight: normal;">Bug: java app arguments bad sort&nbsp;</li>












</ul>












NOTE: Windows: If you have installed java 8 and an older version on the same machine: set the configuration properties 
				wrapper.java.command = &lt;full path to java&gt;
				wrapper.ntservice.java.command = &lt;full path to java&gt;
<h4>yajsw-beta-12.04 Dec 24th 2015</h4>


















<ul>















  <li style="font-weight: normal;">Bug: windows service: bad quotes in java service command line.</li>















  <li>
    <span style="font-weight: normal;"></span>Bug: script not executed: too many concurrent executions</li>















  <li>Bug: posix_spawn: application does not set working dir.</li>














  <li>Bug: Windows 10: Exception in service install</li>














  <li>Bug: query daemon must be executed with root priv</li>














  <li>Bug: process does not start if space char in folder or environment</li>














  <li>Bug: Mac OS X Yosemite: service install fails</li>














  <li>Bug: 'java -jar wrapper.jar -c http://../wrapper.conf' throws class not found exception</li>














  <li>Bug: error writing from system tray icon to stdin of wrapped process</li>













  <li>Bug: some pdh should return long instead of int</li>














  <li>New:&nbsp;support app shutdown listener, similar to JSW</li>















  <li>New: keystore function. See documentation for details.</li>














  <li>Change: update to netty 4.0.33 and jna 4.2.1</li>














  <li>Change: fix APL license header, format source, organize imports</li>















</ul>















<h4>yajsw-beta-12.03 October 5th 2015</h4>


















<ul>


















  <li style="font-weight: normal;">Bug: windows service:
add
quotes to java command if it contains blank</li>


















  <li>
    <span style="font-weight: normal;">Bug:
genConfig: error parsing java command line</span><span style="font-weight: normal;"></span>
  </li>


















  <li>
    <span style="font-weight: normal;">Bug:
JVMController: use parameter instead of fixed timeout when reconnecting</span></li>
















  <li><span style="font-weight: normal;">Bug: wrapper hangs if tray port in use</span></li>
















  <li><span style="font-weight: normal;">Bug: bad quotes in java options</span></li>
















  <li><span style="font-weight: normal;">Bug: Randomly: killed sub-process does not close channel to wrapper thus not allowing restart of application<br>
















    </span></li>


















  <li><span style="font-weight: normal;">Change: Log
an error if folder listing returns null (in java this may be a network
hdd error)</span></li>


















  <li><span style="font-weight: normal;">Change:
update ahessian</span></li>


















  <li><span style="font-weight: normal;">Change:
updated groovy scripts: logging&nbsp;</span></li>


















  <li style="font-weight: bold;"><span style="font-weight: normal;">Change: </span>property:
wrapper.posix_spawn is now default for all posix OS</li>


















  <li style="font-weight: bold;"><span style="font-weight: normal;">Change: MyFileHandler due to
license conflict</span></li>


















  <li style="font-weight: bold;"><span style="font-weight: normal;">Change: log if a folder listing
returns null (java: error accessing a network drive)</span></li>


















</ul>

















<span style="font-weight: bold;">NOTE: property:
wrapper.posix_spawn is now default for all posix OS</span>
<h4>yajsw-beta-12.02 June 23rd 2015</h4>


















<ul>


















  <li>Bug: posix: when wrapper.app.env is set, "java" is not
found -&gt; changed execve to execvpe</li>


















  <li>New: property: <span style="font-weight: bold;">wrapper.posix_spawn</span>
: Note: in a next release this property will be set by default and will
override wrapper.fork_hack. This implementation is an adaption from
&nbsp;<a href="https://github.com/brettwooldridge/NuProcess">nuprocess</a></li>


















  <li>New: property: <span style="font-weight: bold;">wrapper.posix_vfork</span></li>


















  <li>Change: reverted to netty 4 lib: there are some projects
out there which require java 6.<br>


















  </li>


















</ul>


















<span style="font-weight: bold;">NOTE: requires java 6 or
higher for both wrapper and wrapped application</span>
<h4>yajsw-beta-12.01 April 28th 2015</h4>


















<ul>


















  <li>Bug: Quotes in java command line</li>


















  <li>Bug: multiple bugs in <span style="font-style: italic;">RuntimeJavaMain</span></li>


















  <li>Change: switched from <span style="font-weight: bold;">quartz</span>
to <a style="font-weight: bold;" href="http://sourceforge.net/projects/yacron4j/">yacron4j</a>.</li>


















  <li>Change: removed pause from batch files.</li>


















  <li>Change: updated groovy and netty libs</li>


















  <li>New: Configuration property: <span style="font-style: italic;">wrapper....script.&lt;n&gt;.maxConcInvoc</span></li>


















  <li>New: Support for <a href="http://sourceforge.net/projects/vfs-dbx/">vfs-dbx</a>
(dropbox). thus command files and automatic updates can be done from
dropbox.</li>


















  <li>New: Configuration property: <span style="font-style: italic;">wrapper.debug.level</span></li>


















  <li>New: Configuration property: <span style="font-style: italic;">wrapper.filter.debug.default</span></li>


















  <li>New: Configuration properties: <span style="font-style: italic;">wrapper.wrapperJar,
wrapper.appJar</span></li>


















  <li>New: Configuration property:&nbsp;<span style="font-style: italic;">wrapper.logfile.desc</span><br>


















  </li>


















  <li><a href="http://sourceforge.net/p/yajsw/patches/11/">Patch</a>:
Make the console output fill the window when resized&nbsp;</li>


















  <li><a href="http://sourceforge.net/p/yajsw/patches/10/">Patch</a>:
Startup under AIX 7.1 fails due to StringIndexOutOfBoundsException</li>


















  <li><a href="http://sourceforge.net/p/yajsw/patches/9/">Patch</a>:
Fix "No such file or directory" error on Mac OSX with Java 1.7</li>


















  <li><a href="http://sourceforge.net/u/gorenuru/yajsw/ci/05ca0a47f3bdb26000ca8351f96810053b825865/tree/src/yajsw/src/main/java/org/rzo/yajsw/os/posix/bsd/macosx/MacOsXService.java?diff=04d73af0c07bb00d9ca4bc583ef63d1bfc808cc4">Patch</a>:
Fix getPid() in MacOSXService</li>


















  <li><a href="http://sourceforge.net/u/gorenuru/yajsw/ci/bdc4256a1dd07b5ce878c6d0e457a24d42321c53/tree/src/yajsw/src/main/java/org/rzo/yajsw/os/posix/bsd/macosx/MacOsXService.java?diff=05ca0a47f3bdb26000ca8351f96810053b825865">Patch</a>:
MacOSX: Make sure that install script works on vanila machines (when no
custom services installed yet and directory is not exists)</li>


















  <li><a href="http://sourceforge.net/u/shekhargulati/yajsw/ci/f6f520b6bb4c6cd8107a5483d5dde911f6212b3f/tree/src/yajsw/src/main/java/org/rzo/yajsw/os/posix/bsd/macosx/MacOsXService.java?diff=04d73af0c07bb00d9ca4bc583ef63d1bfc808cc4">Patch</a>:
made sure wrapper works on Mac when installed in directory with spaces</li>


















  <li><a href="http://sourceforge.net/u/shekhargulati/yajsw/ci/48d3b5dc7ab39aec84d8afbf171d70819363d87b/tree/src/yajsw/src/main/java/org/rzo/yajsw/os/posix/bsd/macosx/MacOsXService.java?diff=f6f520b6bb4c6cd8107a5483d5dde911f6212b3f">Patch</a>:
MacOsxService does not append configuration parameters to the execution
command</li>


















</ul>


















<big><span style="font-weight: bold;">NOTE: requires
java 7 or higher.</span></big>
<h4>yajsw-alpha-12.00 September 14th 2014</h4>


















<ul>


















  <li><a href="http://sourceforge.net/p/yajsw/patches/9/">Bug</a>:
MacOsX - thanks for the patch</li>


















  <li>Bug: WrappedRuntimeProcess: sort arguments according to
configuration keys</li>


















  <li>Change: <span style="font-weight: bold;">update
to latest jars:</span> commons-collections-3.2.1,
commons-configuration-1.10, jna-4.1.0, netty-all-5.0.0.Alpha2-SNAPSHOT,
groovy-all-2.3.0-beta-2</li>


















  <li>Change: <span style="font-weight: bold;">move
groovy dependency to lib/extended</span></li>


















  <li>Change: do not add passwords to command line of processes
or services</li>


















  <li>Change: minor logging changes &nbsp;and other minor
changes.</li>


















  <li>Change:<span style="font-weight: bold;">
license change</span> - see documentation</li>


















  <li>Change: regex in configuration files: switched from jrexx
to dk.brics.automaton. <span style="font-weight: bold;">moved
jar to lib/extended</span>. functionality is thus optional. <span style="font-weight: bold;">NOTE: regex syntax may have
changed</span></li>


















</ul>


















<h4>yajsw-stable-11.11 January 25th 2014</h4>


















<ul>


















  <li>Bug:&nbsp;on application restart: stream of prev
process is open although new process started.</li>


















  <li><a href="http://sourceforge.net/p/yajsw/discussion/810310/thread/eb9151d2/?limit=25#9ec5">Bug</a>:
11.10 zip file incorrect version</li>


















  <li>Bug: Linux: quotes and spaces in command line paths</li>


















  <li>Bug: Bug: WrappedRuntimeProcess: mkdirs if folder for pid
file does not exist</li>


















  <li>Bug: Minor changes in RuntimeJavaMain</li>


















  <li>Bug: missing&nbsp;.processTerminated invocation in
WrappedRuntimeProcess</li>


















  <li>Bug: NPE when restartGC is set but no no output format is
set.</li>


















  <li>Change: logging in gobbler</li>


















  <li>New: new: Property <span style="font-weight: bold; font-style: italic;">wrapper.ntservice.process_priority</span></li>


















</ul>


















<h4>yajsw-stable-11.10 January 3rd 2014</h4>


















<ul>


















  <li><a href="http://sourceforge.net/p/yajsw/discussion/810311/thread/e80cba1e">Bug</a>:
Problems with quotes in windows services and if classpath path contains
blank</li>


















</ul>


















<h4>yajsw-stable-11.09 December 23rd 2013</h4>


















<ul>


















  <li>New: Property: <span style="font-weight: bold; font-style: italic;">wrapper.runtime.pidfile</span>:
create a pid file for runtime applications</li>


















  <li>New: Property: <span style="font-weight: bold; font-style: italic;">wrapper.image.javawrapper</span>:
wrap a runtime application with a java application, so that (on
winodws) we can restart the wrapper process and reconnect to the
secondary java wrapper without restarting the native application.</li>


















  <li>New: Property <span style="font-weight: bold; font-style: italic;">wrapper.filter.debug.&lt;trigger
key&gt;</span>=true to enable logging of trigger executions</li>


















  <li> New: Property: <span style="font-weight: bold; font-style: italic;">wrapper.debug.comm</span>:
enable tcp/ip communication trace with wrapped application</li>


















  <li>New: Property: <span style="font-weight: bold; font-style: italic;">wrapper.ntservice.stop_dependency</span>:
Linux stop dependency</li>


















  <li>New: source on sourceforge git</li>


















  <li>Change: source folder structure</li>


















  <li>Change: src folder structure to meet maven requirements.
Gradle build scripts adapted accordingly.</li>


















  <li>Change: monitor.gc when wrapper.java.monitor.gc message
template
is not set, but wrapper.java.monitor.gc.restart is set in this case no
gc information is logged to output, but gc data is sent to the
controller.</li>


















  <li>Change: app shutdown script: be invoked only once; avoid
triggering of shutdown hook when executing app shutdown script<br>


















assume that app shutdown script will stop the process. if not it will
be killed after timeout</li>


















  <li>Change: ahessian: (not relevant for yajsw): new: inverse
server rpc.</li>


















  <li><a href="http://sourceforge.net/p/yajsw/discussion/810310/thread/3c13d25a/">Bug</a>:
Searching for 32-bit JVMs on Windows x64</li>


















  <li><a href="https://sourceforge.net/p/yajsw/bugs/85/">Bug</a>:
error in mbean objectname when title includes ":"</li>


















  <li><a href="https://sourceforge.net/p/yajsw/bugs/82/">Bug</a>:
abs path wrapper.java.classpath with * in linux not working</li>


















  <li><a href="https://sourceforge.net/p/yajsw/bugs/90/">Bug</a>:
Linux service scripts not compatible with chkconfig</li>


















  <li><a href="http://sourceforge.net/p/yajsw/discussion/810311/thread/771ed450/?limit=50#2fc6">Bug</a>:
"Bad substitution" because of not escaped quotes</li>


















  <li>Bug: CyclicBufferFileInputStream: probable endless loop</li>


















  <li>Bug: Runtime process does not restart when invoked through
a trigger</li>


















</ul>


















<h4>yajsw-stable-11.08 October 27th 2013</h4>


















<ul>


















  <li><a href="http://sourceforge.net/p/yajsw/discussion/810310/thread/e4402e38/">Bug</a>:
System tray icon on windows 7</li>


















  <li><a href="http://sourceforge.net/p/yajsw/discussion/810310/thread/69a73119/">Change</a>:
check java 7 also for wrapped application and system tray icon</li>


















  <li><a href="http://sourceforge.net/p/yajsw/bugs/86/">Change</a>:
notify port issues for system tray icon</li>


















</ul>


















<h4>yajsw-stable-11.07 July 8th 2013</h4>


















<ul>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/6896276?message=14261772">Bug</a>:
checking java 7</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/7374820">Bug</a>:
Memory Leak -&gt; update to netty 3.5.6</li>


















  <li>Bug:&nbsp;wrapped applications started concurrently may
have the same out_, err_, in_ file names.</li>


















  <li><a href="http://sourceforge.net/p/yajsw/discussion/810311/thread/5035584f/">Bug</a>:
Linux: Permission denied since 11.4 </li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/8275368">Change</a>:
forward wrapper java options used in the configuration to the wrapped
application</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/7999129">New</a>:
new property: <span style="font-weight: bold; font-style: italic;">wrapper.ntservice.reduce_signals</span></li>


















  <li>New: new property: <span style="font-weight: bold; font-style: italic;">wrapper.console.use_interpolated</span></li>


















  <li>New: new property: <span style="font-weight: bold; font-style: italic;">wrapper.app.status.log.lines</span><br>


















  </li>


















</ul>


















<h4>yajsw-stable-11.06 April 20 2013</h4>


















<ul>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/7330619">Bug</a>:
Jna tmp folder</li>


















  <li><a href="http://sourceforge.net/tracker/index.php?func=detail&amp;aid=3608009&amp;group_id=224503&amp;atid=1061881">Bug</a>:
yajsw prints wrong version</li>


















  <li>Bug:&nbsp;netty: do not use <span style="font-style: italic;">sun.misc.Unsafe</span>,
this may crash the JVM (we do not need high performance)</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/6896276">Bug</a>:
windows &gt;= vista and java &gt;= 7:&nbsp; check that
java.net.preferIPv4Stack=true is set and set it in the windows batch
file and service command line</li>


















  <li>Bug: possible loop in JVMController.processStarted()</li>


















  <li>Change: minor changes in WrappedJavaProcess.getDOption(),
WrappedJavaProcess.reconnect()</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/7103764">New</a>:
new property: <span style="font-weight: bold; font-style: italic;">wrapper.restart.reload_cache</span></li>


















  <li>New: script: &nbsp;vfsCommandCondition.gv<br>


















  </li>


















</ul>


















<h4>yajsw-stable-11.05 &nbsp;March 3rd 2013</h4>


















<ul>


















  <li><a href="http://sourceforge.net/support/tracker.php?aid=3601783">Bug</a>:
WrapperJVMMain.WRAPPER_MANAGER.signalStopping() is broken</li>


















  <li>Bug: while (true) {wrappedJavaProcess.init();
wrappedJavaProcess.start(); wrappedJavaProcess.stop() } fails to
immediatly stop the application.</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/6896276">Bug</a>:
NPE in WrapperManagerImpl</li>


















  <li><a href="http://sourceforge.net/tracker/index.php?func=detail&amp;aid=3605341&amp;group_id=224503&amp;atid=1061881">Bug</a>:
&nbsp;[3605341] NoClassDefFoundError:
org/rzo/yajsw/srvmgr/server/Ser <span style="font-weight: bold;"></span><strong style="font-weight: bold;"></strong></li>


















  <li>Change: WrappedProcess.start() now resets the restart
counter.</li>


















</ul>


















<h4>yajsw-stable-11.04 January 11th 2013</h4>


















<ul>


















  <li>New: Autoupdate of wrapper and application. see <a href="http://yajsw.sourceforge.net/#mozTocId790812">documentation</a>
for details</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/6182590">New</a>:
command "-rw": remove and wait until the service has been removed.</li>


















  <li><a href="http://sourceforge.net/tracker/index.php?func=detail&amp;aid=3599318&amp;group_id=224503&amp;atid=1061884">New</a>:
property <span style="font-weight: bold; font-style: italic;">wrapper.logfile.maxdays</span>&nbsp;</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/6440092">Bug</a>:
Installing a multi-config wrapper as daemon</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/6131121">Bug</a>:
AIX, FreeBSD: Undefined symbol "stdout", error installing daemon</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/5591296">Bug</a>:
Mac OS Can't connect to Window</li>


















  <li><a href="https://sourceforge.net/tracker/index.php?func=detail&amp;aid=3590449&amp;group_id=224503&amp;atid=1061881">Bug</a>:
wrapper.app.env broken in 11.03</li>


















  <li><a href="https://sourceforge.net/tracker/index.php?func=detail&amp;aid=3587141&amp;group_id=224503&amp;atid=1061881">Bug</a>:
wrapper.cleanup_tmp not working as supposed</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/6071296">Bug</a>:
Nested braces / absolute path in wrapper.conf</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/6013828">Bug</a>:
Changed VFS so that it does not use an xml parser</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/5590999">Bug</a>:&nbsp;shutdown
wait timeout negative</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810310/topic/5869793">Bug</a>:
JarBuilder: Source code discrepancy</li>


















  <li>Change: make yajsw less talkative if wrapper.debug is not
set.</li>


















</ul>


















<span style="font-weight: bold;">NOTE</span>:
meaning of <span style="font-weight: bold; font-style: italic;">wrapper.logfile.maxfiles</span>
has changed: if logfile rolling is set to DATE, maxfiles refers to the
number of files per day not the total. Set maxdays to limit the total
number of files.
<h4>yajsw-stable-11.03 &nbsp;August 26th 2012</h4>


















<ul>


















  <li><a href="http://sourceforge.net/tracker/index.php?func=detail&amp;aid=3536827&amp;group_id=224503&amp;atid=1061881">Bug</a>:
11.02 does not work with Java 1.5</li>


















  <li><a href="https://sourceforge.net/tracker/index.php?func=detail&amp;aid=3541605&amp;group_id=224503&amp;atid=1061881">Bug</a>:
.svn dir in src\ahessian\org\jboss\netty\handler\ipfilter</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/5421120">Bug</a>:
java.io.IOException: The handle is invalid -&gt; print message
instead of stack trace</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/5435529/index/page/1">Bug</a>:
configuration file: error evaluating &nbsp;include directives with
env vars.</li>


















  <li><a href="https://sourceforge.net/tracker/?func=detail&amp;aid=3544415&amp;group_id=224503&amp;atid=1061883">Bug</a>:
Problems geting stdout with Solaris 10 sparc:</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4685606">Bug</a>:
Windows: totalRAM in WindowsXPSystemInformation</li>


















  <li>Bug: commons logging, default (log4j) does not log to file.</li>


















  <li>Bug: Error creating wrapper.app.pre.script</li>


















  <li>Bug: Application could not restart due to hacker TCP/IP
connection to wrapper controller</li>


















  <li>Bug: ahessian may hang similar <a href="http://bugs.sun.com/view_bug.do?bug_id=6822370">to
java bug in ReentrantLock</a></li>


















  <li>Change: when setting environment vars: use javas
System.getEnv()</li>


















  <li>Change: Executing jar files: classpath from manifest are
added to the classpath of the application.</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810310/topic/5355328">New</a>:
    <span style="font-weight: bold; font-style: italic;">wrapper.config.script.&lt;n&gt;</span>
for user defined scripts within the configuration</li>


















  <li>New: <span style="font-weight: bold; font-style: italic;">decode.gv</span>
groovy script for decoding properties within the configuration file</li>


















  <li>New: <span style="font-weight: bold; font-style: italic;">wrapper.script.&lt;n&gt;.&lt;state&gt;</span>:
allow multiple scripts for the same state transition. For compatibility<span style="font-style: italic; font-weight: bold;">&nbsp;wrapper.script.&lt;state&gt;</span>
will be interpreted as &lt;n&gt; being set to "". Execution
order of the scripts is the alpha-numeric order of &lt;n&gt;</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/5399307">New</a>:
Unset properties by setting&nbsp; ${return null}</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/5300585?message=11599335">New</a>:
\r\n line endings: &nbsp;wrapper.logfile.format=LPNTM\r\n</li>


















  <li>New: property <span style="font-weight: bold; font-style: italic;">wrapper.cleanup_tmp</span>
default: true, clean up the tmp folder of err_ ... files at startup.
Note: jna cleans up its files at startup.</li>


















  <li>New: property <span style="font-weight: bold; font-style: italic;">wrapper.tray.spawn_process</span></li>


















</ul>


















<h4>yajsw-stable-11.02 June 10th 2012</h4>


















<ul>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810310/topic/5308085">Bug</a>:
cmd line config override broken since 10.5</li>


















  <li><a href="http://sourceforge.net/tracker/?func=detail&amp;aid=3522537&amp;group_id=224503&amp;atid=1061881">Bug</a>:
Linux: evaluating wrapper.ntservice.additional</li>


















  <li>Bug: same script should not execute concurrently</li>


















  <li><a href="http://sourceforge.net/support/tracker.php?aid=3532511">Bug</a>:
WRAPPER log rolling doesn't work</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/5237464">Bug</a>:
rollmode DATE &nbsp;always keeps 10(=wrapper.logfile.maxfiles)
files for every date</li>


















  <li>Bug: in stop configuration include of main configuration
must be absolute.</li>


















  <li><a href="https://sourceforge.net/tracker/?func=detail&amp;aid=3519125&amp;group_id=224503&amp;atid=1061881">Bug</a>:
installDaemon.sh not doesn't work</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4685606/">Bug:</a>&nbsp;
totalRAM() hangs</li>


















  <li>New:<span style="font-style: italic; font-weight: bold;">
wrapper.script.reload</span> allow update of scripts without
restart of wrapper: file changed date is checked before script execution</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/5300585">New</a>:
    <span style="font-weight: bold; font-style: italic;">wrapper.script.encoding,
wrapper.log.encoding, wrapper.conf.encoding</span> configuration
properties to set the encoding of the script, log and configuration
files.</li>


















  <li><a href="http://sourceforge.net/tracker/?func=detail&amp;aid=3521453&amp;group_id=224503&amp;atid=1061881">New</a>:
    <span style="font-weight: bold; font-style: italic;">wrapper.console.pipestreams</span>
for wrapped java processes: stdout and stderr are streamed to the
wrapper instead of t-ing System.out and System.err</li>


















  <li>New: log os, yajsw, jvm versions</li>


















  <li>Change: upgraded libraries: configuration-1.8,
netty-3.5.0Beta1, commons-vfs2-2.0, jna-3.4.1</li>


















  <li>Change: <span style="font-weight: bold;">Separated
Posix and Windows batch files to folders bat and bin !</span></li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4685606/">Change</a>:
calculate totalRAM/freeRAM only when required.</li>


















  <li>Change: build: dependency versions defined in main build
script</li>


















</ul>


















<h4>yajsw-stable-11.01 April 11th 2012</h4>


















<ul>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4685606">Bug</a>:
handling ping timeout, in some cases ping timeout may
trigger too early, ping timeout may result in stop instead of restart.</li>


















  <li><a href="http://sourceforge.net/tracker/?func=detail&amp;aid=3398506&amp;group_id=224503&amp;atid=1061881">Bug</a>:&nbsp;Duplicate
parameters for launched service</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/5064936">Bug</a>:
Linux: app.wrapper.account doesn't with fork_hack</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/5163743/">Bug</a>:
Linux: wrapper.fork_hack spawns java with wrong jna lib</li>


















  <li>Bug: Linux: randomly process will not restart</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/5120673">Bug</a>:
wrapper.on_exit.1=SHUTDOWN
&nbsp;System.exit(1) does not shutdown wrapper</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/5078302">Bug</a>:
exception when stopping wrapper console using CNTRL-C</li>


















  <li>Change: value for property <span style="font-weight: bold; font-style: italic;">wrapper.on_exit</span>:
    <span style="font-weight: bold; font-style: italic;">STOP</span>:
will cause the application to stop (note: wrapper will stop depending
on the wrapper.control property), <span style="font-weight: bold; font-style: italic;">SHUTDOWN</span>:
will stop the application and the wrapper</li>


















  <li><a href="https://sourceforge.net/tracker/?func=detail&amp;aid=3414960&amp;group_id=224503&amp;atid=1061884">Change</a>:
java system property &nbsp;<span style="font-weight: bold; font-style: italic;">jna.tmpdir</span>
for setting the tmp folder for jna library files.Please refer to the
documentation. <span style="font-weight: bold; font-style: italic;">wrapper.tmp.path</span>
per default set to jna.tmpdir</li>


















  <li>Change: per default tmp files are created in
&lt;yajsw&gt;/tmp</li>


















  <li>Change: per default log files are created in
&lt;yajsw&gt;/log</li>


















  <li>Change: reworked log file rolling.</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/5078958">Change</a>:
use wrapper.app.parameter &nbsp;for native exes</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/5145839">Change</a>:
commandCondition.gv: commands for start/stop of
cyclic thread dump</li>


















  <li><a href="http://sourceforge.net/tracker/index.php?func=detail&amp;aid=3506101&amp;group_id=224503&amp;atid=1061881">Change</a>:
ROLLNUM always appended
to logfile</li>


















  <li>Change: Linux: installDaemon.sh, startDaemon.sh,
stopDaemon.sh calls sudo</li>


















  <li>New: properties for restarting the application in case of
excessive memory or full scan gc usage:&nbsp;<span style="font-weight: bold; font-style: italic;">wrapper.java.monitor.gc.threshold</span>,
    <span style="font-weight: bold; font-style: italic;">wrapper.java.monitor.gc.restart</span>,
    <span style="font-weight: bold; font-style: italic;">wrapper.java.monitor.heap.restart</span></li>


















  <li><a href="https://sourceforge.net/tracker/index.php?func=detail&amp;aid=3506101&amp;group_id=224503&amp;atid=1061881">New</a>:
wrapper.logfile.rollmode= DATE<br>


















  </li>


















  <br>


















</ul>


















<h4>yajsw-stable-11.0 &nbsp;Februray 22nd 2012</h4>


















<ul>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4959106">Bug</a>:
System tray icon causes exception</li>


















  <li><a href="https://sourceforge.net/tracker/index.php?func=detail&amp;aid=3485343&amp;group_id=224503&amp;atid=1061881">Bug</a>:
genConfig: does not handle jdk 6 wildcard in classpath: note: yajsw
handles wild cards differently !</li>


















  <li><a href="https://sourceforge.net/tracker/index.php?func=detail&amp;aid=3485343&amp;group_id=224503&amp;atid=1061881">Bug</a>:
error resolving wild cards in case classpath is "lib/<span style="color: red;"> </span>folder/*"</li>


















  <li><a href="https://sourceforge.net/tracker/index.php?func=detail&amp;aid=3332556&amp;group_id=224503&amp;atid=1061881">Bug</a>:
Specifying java command in environment: better logging if command not
found.</li>


















  <li><a href="https://sourceforge.net/tracker/index.php?func=detail&amp;aid=3401298&amp;group_id=224503&amp;atid=1061881">Bug</a>:
Not equals configuration under Windows and Unix: <span style="font-weight: bold;">replaced in unix batches yajsw
with wrapper !!</span></li>


















  <li>Bug: When installing a service "remember" required
environment variables.<span style="font-weight: bold;"><span style="font-weight: bold;"></span><br>


















    </span></li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810310/topic/4975334/index/page/1">Change</a>:
    <span style="font-weight: bold; font-style: italic;">wrapper.java.customProcName</span>:
if this is an absolute path, the given path is used.</li>


















  <li><a href="https://sourceforge.net/tracker/?func=detail&amp;aid=3155811&amp;group_id=224503&amp;atid=1061881">Change</a>:
    <span style="font-weight: bold; font-style: italic;">wrapper.java.command</span>
must be a file: Windows: if .exe is missing it is automatically
added.&nbsp;</li>


















  <li><a href="https://sourceforge.net/tracker/index.php?func=detail&amp;aid=3463563&amp;group_id=224503&amp;atid=1061884">Change</a>:
Enhanced logging when determining java command.</li>


















  <li>Change: update to jna release 3.4.0</li>


















  <li>Change: update to netty release 3.3.1</li>


















  <li>Change: update to groovy release 1.8.6</li>


















  <li><a href="http://sourceforge.net/tracker/?func=detail&amp;aid=3434665&amp;group_id=224503&amp;atid=1061881">New</a>:
Configuration property <span style="font-weight: bold; font-style: italic;">wrapper.save_interpolation</span>,
default: value <span style="font-weight: bold;">true</span></li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810310/topic/5001499">New</a>:
Configuration properties for windows services:<span style="font-weight: bold; font-style: italic;">
wrapper.ntservice.delayed_autostart</span>, <span style="font-weight: bold; font-style: italic;">wrapper.ntservice.failure_actions.reset_period</span>,
    <span style="font-weight: bold; font-style: italic;">wrapper.ntservice.failure_actions.reboot_msg</span>,&nbsp;<span style="font-weight: bold; font-style: italic;">wrapper.ntservice.failure_actions.command</span>,&nbsp;<span style="font-weight: bold; font-style: italic;">wrapper.ntservice.failure_actions.action</span>,&nbsp;<span style="font-weight: bold; font-style: italic;">wrapper.ntservice.failure_actions.action_delay</span></li>


















  <li><a href="https://sourceforge.net/tracker/?func=detail&amp;aid=3303944&amp;group_id=224503&amp;atid=1061884">New</a>:
property <span style="font-weight: bold; font-style: italic;">wrapper.ntservice.java.command</span>
for running the service wrapper process with a java command different
then wrapper.java.command.</li>


















  <li>New: property<span style="font-weight: bold; font-style: italic;">
wrapper.ntservice.java.customProcName</span> for copying the java
command of the service wrapper process.</li>


















</ul>


















<br>


















<a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4767821">NOTE</a>:
<span style="font-weight: bold;">JBoss 7</span>:&nbsp;runs
only with java version prior to 6_17 or higher than 7_0.
<h4>yajsw-beta-10.12 December 18th 2011</h4>


















<ul>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4886027">Bug</a>:
Error evaluating ${...} expressions in configuration. Error installing
daemon on Linux</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810310/topic/4851007">Bug</a>:
yajsw-beta-10.11 manifest missing jars. Note: jars&nbsp; moved from
lib/core to lib/extended</li>


















</ul>


















<h4>yajsw-beta-10.11 November 27th 2011</h4>


















<ul>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4767821">Bug</a>:
Jboss 7 startup fails logging initialisation -&gt; do not use
java.util.logging in WrapperJVMMain</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4776215">Bug</a>:
Error resolving ".." and "." in wrapper.java.classpath property</li>


















  <li><a href="http://sourceforge.net/tracker/?func=detail&amp;aid=3424500&amp;group_id=224503&amp;atid=1061881">Bug</a>:
Reworked setenv.gv script</li>


















  <li>Bug: Error parsing groovy script -&gt; remove
index.list from glazedlist jar file.</li>


















  <li><a href="https://sourceforge.net/tracker/index.php?func=detail&amp;aid=3435522&amp;group_id=224503&amp;atid=1061883">Bug</a>:
PosixService.stop() fixed timeout. thanks to ddahlmann for the patch</li>


















  <li><a href="http://sourceforge.net/tracker/?func=detail&amp;aid=3423578&amp;group_id=224503&amp;atid=1061881">Bug</a>:
maxmemory parameter doesn't work without initmemory</li>


















  <li><a href="http://sourceforge.net/tracker/?func=detail&amp;aid=3424500&amp;group_id=224503&amp;atid=1061881">Change</a>:
Added logging in application scripts</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4807968">Change</a>:
Absolute Minimum Files To Run On Windows -&gt; removed unused jar
files, moved jar files to lib/extended</li>


















  <li>Change: "Remember" environment variables and system
properties
when installing a service. Up to now only interpolations were
"remembered"</li>


















  <li><a href="http://sourceforge.net/tracker/?func=detail&amp;aid=3434665&amp;group_id=224503&amp;atid=1061881">New</a>:
Interpolated config options become "hard coded" -&gt; new
configuration property: <span style="font-weight: bold; font-style: italic;">wrapper.save_interpolated</span>,
default value: true</li>


















  <li>New: Sample configuration file for oracle odi</li>


















  <li>New: Sample configuration file for JBoss 7</li>


















  <li>New: Sample script for thread dump on application exit. </li>


















</ul>


















<h4>yajsw-beta-10.10 October 15th 2011</h4>


















<ul>


















  <li>Bug: genConfig: error parsing jvm options.</li>


















  <li>Bug: process may not restart, if application start fails
after first restart</li>


















  <li>Bug: tray icon: possible port leak if connection fails.</li>


















  <li>Bug: tray icon: multicast discovery: may cause message
flood if running on multiple computers in a network</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810310/topic/3808825/index/page/2">Bug</a>:
windows: install service: bad java exe</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4637783">Bug</a>:
application not started on linux: new property: <span style="font-weight: bold; font-style: italic;">wrapper.fork_hack</span></li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4755774">Bug</a>:
Linux: application restarts every 25 days.<br>


















  </li>


















  <li>Change: tray icon: windows: close popup menue on mouse
click.
note: java implements this only with awt, not with swing -&gt;
using
jna to listen to mouse events outside java. If anyone knows how to
implement this for linux, pls let me know.</li>


















  <li>Change: split ahessian.jar -&gt; ahessian.jar,
hessian4.jar in future these may be extracted to a separate project.</li>


















  <li>Change: AbstractWrappedProcess: remove synchronized from
stop(), so we can stop a process while it is restarting/starting.</li>


















  <li> <a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4678860?message=10670101">New</a>:
added authentication to sendMail script.</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4694425">New</a>:
windows: UAC support: WrapperExe: if required, elevate process for
service start, stop, install, remove.</li>


















  <li>New: windows: support service installation with<a href="http://msdn.microsoft.com/en-us/library/windows/desktop/ms685155%28v=VS.85%29.aspx">
delayed autostart</a>:<span style="font-style: italic;"><span style="font-weight: bold;">
wrapper.ntservice.starttype=DELAYED_AUTO_START</span></span></li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4746090">New</a>:
tray icon: property<span style="font-weight: bold; font-style: italic;">
wrapper.tray.look_and_feel</span> = &lt;L&amp;F full
class name&gt;</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4746090">New</a>:
tray icon: display virtual and private memory.</li>


















</ul>


















<h4>yajsw-beta-10.9 August 14th 2011</h4>


















<ul>


















  <li>Bug: Corrupt source file in distribution</li>


















  <li>Bug: NPE in ahessian.</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4589260">Bug</a>:
genConfig: Error parsing -jar with space in jar path.</li>


















  <li>Bug: application not restarted on server with high load:
Thread race issue</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4641352">Bug</a>:
Linux: genConfig: NPE.</li>


















  <li>Bug: class not found exception in applications which use
commons configuration: error in build script</li>


















  <li>Bug: error in build script: adjusted dependencies</li>


















  <li><a href="https://sourceforge.net/tracker/?func=detail&amp;aid=3381793&amp;group_id=224503&amp;atid=1061881">Bug</a>:
misspelled "relative" in default configuration.</li>


















  <li>Bug: RESTART state change trigger may stop the application</li>


















  <li><a href="https://sourceforge.net/tracker/?func=detail&amp;aid=3384304&amp;group_id=224503&amp;atid=1061881">Bug</a>:
    <span style="font-size: 11pt;">Error in
DateFileHandler.rotateDate() using 24h time</span></li>


















  <li><span style="font-size: 11pt;"><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4635191/index/page/1">Bug</a>:
    </span><em>monitor gc: could not find minorGCBean</em></li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4606898">Bug</a>:
Windows: missing WrappedProcess.getCluster()</li>


















  <li>New: method<span style="font-weight: bold; font-style: italic;">
WrappedProcess.gc()</span> &amp; gc button in system tray
icon console.</li>


















  <li>New: method <span style="font-weight: bold; font-style: italic;">WrappedProcess.dumpHeap(String
file)</span> &amp; dump heap button in system tray icon
console</li>


















  <li><a href="https://sourceforge.net/tracker/index.php?func=detail&amp;aid=3385477&amp;group_id=224503&amp;atid=1061884">New</a>:
method <span style="font-weight: bold; font-style: italic;">WrapperJVMMain.WRAPPER_MANAGER.signalStopping(long
waitHint)</span>.Thanks to&nbsp;<a href="http://sourceforge.net/users/ddahlmann/" title="Dennis Dahlmann">ddahlmann</a> for the patch.</li>


















  <li><a href="https://sourceforge.net/tracker/index.php?func=detail&amp;aid=3385477&amp;group_id=224503&amp;atid=1061884">New</a>:
method <span style="font-weight: bold; font-style: italic;">WrapperJVMMain.WRAPPER_MANAGER.getProperties()</span>.</li>


















  <li>New: Windows: method <span style="font-weight: bold; font-style: italic;">WrapperJVMMain.WRAPPER_MANAGER.getStopReason()</span>.
Returns a string indicating why the application is being stopped:
"SERVICE", "COMPUTER", "USER", "PING_TIMEOUT", "RESTART", null. This
method can be called within a shutdown hook of the application.</li>


















  <li><a href="https://sourceforge.net/tracker/?func=detail&amp;aid=3299021&amp;group_id=224503&amp;atid=1061883">New</a>:
Tray Icon: Configuration properties: <span style="font-weight: bold; font-style: italic;">wrapper.ntservice.autoreport.waitready</span>,
    <span style="font-weight: bold; font-style: italic;">wrapper.tray.text.dialog_exit_tray</span>,
    <span style="font-weight: bold; font-style: italic;">wrapper.tray.text.dialog_stop</span>,
    <span style="font-weight: bold; font-style: italic;">wrapper.tray.text.dialog_restart</span>,
    <span style="font-weight: bold; font-style: italic;">wrapper.tray.text.dialog_exit_wrapper</span>,
    <span style="font-weight: bold; font-style: italic;">wrapper.tray.commands</span><span style="font-weight: bold; font-style: italic;"></span>.
Thanks to <a href="http://sourceforge.net/users/shacharo/" title="shachar ochayon">shacharo</a> for the patch.</li>


















  <li>New: Tray Icon: Dialog before stopping
wrapper/application/tray controlled by property:&nbsp;<span style="font-weight: bold; font-style: italic;">wrapper.tray.dialog</span>.
Default: true</li>


















  <li>New: Tray Icon: Script to set tray icon state color</li>


















  <li><a href="https://sourceforge.net/tracker/?func=detail&amp;aid=3181494&amp;group_id=224503&amp;atid=1061884">New</a>:
windows bat: runConsoleW.bat, systemTrayIconW.bat</li>


















</ul>


















<h4>yajsw-beta-10.8 June 26th 2011</h4>


















<ul>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4438875">Bug</a>:&nbsp;Linux:
daemon is not shut down cleanly</li>


















  <li>Bug: <span style="font-style: italic;">wrapper.restart.reload_configuration&nbsp;</span>error
on application restart</li>


















  <li>Bug: genConfig aborts for "java -jar app.jar"</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4544940">New</a>:
Windows Vista and higher: handle session 0 for services: <span style="font-weight: bold; font-style: italic;">wrapper.ntservice.logon_active_session</span>:
wait for user to logon and logon the application as this user. <span style="font-style: italic; font-weight: bold;">wrapper.ntservice.desktop</span>:
the name of the desktop for the application. Should be set to&nbsp;<span style="font-style: italic; font-weight: bold;">WinSta0\\</span><em style="font-style: italic;"><span style="font-weight: bold;">Default</span>.</em></li>


















  <li>New: Configuration property: <span style="font-weight: bold; font-style: italic;">wrapper.java.monitor.gc</span>,
    <span style="font-weight: bold; font-style: italic;">wrapper.java.monitor.gc.intervall</span>
to print gc logging to System.err</li>


















  <li>New: Configuration property: <span style="font-weight: bold; font-style: italic;">wrapper.filter.missing.autostop.&lt;n&gt;</span>.
Default true. If set to false the trigger will remain active after the
trigger has been activated, resulting in continuous trigger monitoring.</li>


















  <li>Change: use commons EnvironmentConfiguration to resolve
environment variables in configuration -&gt; correct handling of
case
under windows.</li>


















  <li>Change: systemInformation().totalRAM() hangs on some
machines
-&gt; avoid invocation if configuration does not require RAM
calculation</li>


















  <li>Change: win64: got Process.getCommandline to work so we do
not need to call wmi.</li>


















  <li>Change: do not replace "\\" by "/" in environment variables.</li>


















  <li>Change: newest jna and groovy libraries.</li>


















</ul>


















<h4>yajsw-beta-10.7 May 21th 2011</h4>


















<ul>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4438875">Bug</a>:
Error restarting application when using&nbsp;<span style="font-style: italic;">wrapper.stop.conf</span></li>


















  <li>Bug: Windows: <span style="font-style: italic;">wrapper.java.classpath.5=C:/test/yajsw/lib/core/commons/*.jar</span>
does not resolve files. Issue was: new File("C:").getAbsolutePath()
returns current working dir instead of C:/</li>


















  <li><a href="https://sourceforge.net/tracker/?func=detail&amp;aid=3291045&amp;group_id=224503&amp;atid=1061881">Bug</a>:
Default startup timeout is 15 and not 30 as documented</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4515877">Bug</a>:
Linux: Error in environment variable expansion</li>


















  <li>Bug: setting <span style="font-style: italic;">wrapper.app.pre_main.script&nbsp;</span>causes
class not found exception</li>


















  <li>Bug: <span style="font-style: italic;">wrapper.restart.reload_configuration
    </span>does not reload the configuration file.</li>


















  <li>Bug: Deadlock if WrappedProcess.start() is called while
process is restarting.</li>


















  <li>New: Configuration Properties <span style="font-style: italic; font-weight: bold;">wrapper.java.monitor.deadlock</span>,
    <span style="font-weight: bold; font-style: italic;">wrapper.java.monitor.heap</span>
for details please refer to the configuration documentation.</li>


















  <li>New: Windows: <span style="font-style: italic; font-weight: bold;">wrapper.console.minimized</span>
for indicating if the console of the application should be minimized at
startup.</li>


















</ul>


















<h4>yajsw-beta-10.6 March 19th 2011</h4>


















<ul>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4417845">Bug</a>:
property should be <span style="font-style: italic;">wrapper.logfile.format</span>
instead of&nbsp;wrapper.logf.format <strong></strong></li>


















  <li><a href="http://sourceforge.net/tracker/?func=detail&amp;aid=3211705&amp;group_id=224503&amp;atid=1061881">Bug</a>:
class cast exception when&nbsp;<span style="font-style: italic;">wrapper.java.app.jar</span>
is set</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4411803">Bug</a>:
yajsw exception when using jrockit jvm.</li>


















</ul>


















<h4>yajsw-beta-10.5 March 6th 2011</h4>


















<div style="margin-left: 40px;">This release is dedicated
to <a style="font-weight: bold;" href="https://market.android.com/details?id=com.andblind.tunes">My
Blind Tunes</a>,
my kids' first android app. Please support them by downloading the app
and if you like it by giving them 5 stars rating. Thanks !</div>


















<ul>


















  <li>New: logging format: added '<span style="font-weight: bold;">N</span>' option&nbsp;
-&gt; display application name.&nbsp;</li>


















  <li>New: one wrapper/service multiple processes: <span style="font-weight: bold; font-style: italic;">java -jar
wrapper.jar -c conf/p1.conf conf/p2.conf</span>. Service name is
taken from
the first configuration.</li>


















  <li>New: <span style="font-weight: bold; font-style: italic;">maxStartup.gv</span>
sample script to check that application reports startup within a
timeout by printing a "started" messasge on the console.</li>


















  <li>New: sample for running eclipse equinox OSGI server with
clean shutdown</li>


















  <li>New: <span style="font-weight: bold; font-style: italic;">maxDuration.gv</span>
sample script for checking that an application does not run longer than
a given duration</li>


















  <li>New: <a href="http://gradle.org/">gradle</a>
build script for yajsw</li>


















  <li>Change: Default logging format: <span style="font-weight: bold;">LPNTM</span></li>


















  <li>Change: ahessian and srvmgr now in separate jars.</li>


















  <li>Change: reworked logging.</li>


















  <li>Change: reworked windows Cluster.java and Pdh.java</li>


















  <li>Change: reworked FileUtils moved windows specific code to
os.ms.win32 package</li>


















  <li>Change: newest groovy and netty releases.</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4041254">Bug</a>:
Windows 2008 handling of user credentials&nbsp; in service</li>


















  <li>Bug: groovy script initialization may cause memory leak if
process is restarted often</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4379421">Bug</a>:
Windows: Windows Services manager reports error when service stops</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4384689">Bug</a>:
Environment variables are not set in application</li>


















  <li><a href="http://sourceforge.net/tracker/?func=detail&amp;aid=3127090&amp;group_id=224503&amp;atid=1061881">Bug</a>:
Linux: GenConfig: command line max 1024</li>


















  <li><a href="http://sourceforge.net/tracker/?func=detail&amp;aid=3089790&amp;group_id=224503&amp;atid=1061881">Bug</a>:
Remove shutdown hooks to avoid memory leaks</li>


















  <li><a href="http://sourceforge.net/tracker/?func=detail&amp;aid=3093129&amp;group_id=224503&amp;atid=1061881">Bug</a>:
In case wrapper.jar.app.jar is set, add the jar to the classpath</li>


















</ul>


















<h4>yajsw-beta-10.4 October 10th 2010</h4>


















<ul>


















  <li><a href="http://sourceforge.net/tracker/index.php?func=detail&amp;aid=2861404&amp;group_id=224503&amp;atid=1061881">Bug</a>:
Win64: Exception when switching application account&nbsp; -&gt;
use newest jna release.<strong></strong></li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/3763993">Bug</a>:
Vista: Wait for service to start before showing "service not started"</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/3789790">Bug</a>:
Linux: account group not set</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/3784442">Bug</a>:
Linux: NPE when starting a daemon</li>


















  <li>Bug: Thread race condition within the wrapper when
restarting an application.</li>


















  <li><a href="https://sourceforge.net/projects/yajsw/forums/forum/810311/topic/3862134">Bug</a>:
Ping timeout on Windows 2008</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/3815146">New</a>:
new configuration property <span style="font-style: italic; font-weight: bold;">wrapper.app.env.&lt;key&gt;=&lt;value&gt;</span>
for setting application environment variables.</li>


















  <li><a href="http://sourceforge.net/projects/yajsw/forums/forum/810310/topic/3808825">New</a>:
Windows: <span style="font-style: italic;"><span style="font-weight: bold;">WrapperJVMMain.WRAPPER_MANAGER.reportServiceStartup()</span>
    </span>and configuration property &nbsp;<span style="font-style: italic; font-weight: bold;">wrapper.ntservice.autoreport.startup</span></li>


















  <li>New: Tested on Unbutu 10.10 32 and 64 bit, OpenJdk jdk 6<br>


















  </li>


















  <li>Change: Windows: configuration file: convert environment
keys to lower case.</li>


















  <li>Change: newest netty release.</li>


















  <li>Change: ahessian changes for android</li>


















  <li>Change: <span style="font-style: italic; font-weight: bold;">wrapper.daemon.run_level_dir</span>
set to a script in wrapper.conf.default should handle most linux
distros.</li>


















  <li>Change: genConfig: do not set user account, since most
users will comment this out.</li>


















  <li>Known Issue: Linux: wrapper cannot reconnect to java
application, wrapper file descriptors not closed on fork of application.</li>


















</ul>


















<h4>yajsw-beta-10.3 July 29th 2010</h4>


















<ul>


















  <li>Bug: ClasscastException when &nbsp;defining
wrapper.filter.trigger.1=,
&nbsp;wrapper.filter.script.1=,wrapper.filter.action.1=</li>


















  <li>Bug: Defining&nbsp;wrapper.filter.trigger.1=x,
&nbsp;wrapper.filter.script.1=,&nbsp;wrapper.filter.trigger.2=x,
&nbsp;wrapper.filter.script.2= will execute only one script</li>


















  <li>Bug: Windows Services: under high load, on server reboot,
or on
high value of wrapper.startup.delay, application start may take longer
than windows service timeout -&gt; start application in a separate
thread and return to windows service controller asap</li>


















  <li>Bug: under high load on application restart: restart
triggered by
multiple state changes -&gt; supress restart if we are already
restarting.</li>


















  <li>Bug: &nbsp;Linux: service does not start at boot
-&gt; exit codes in line with <a href="http://refspecs.linux-foundation.org/LSB_3.2.0/LSB-Core-generic/LSB-Core-generic/tocsysinit.html">LSB</a>.
On the same occasion: changed posix daemon template to conform to LSB</li>


















  <li>Bug: [<a href="http://sourceforge.net/tracker/?func=detail&amp;aid=3011626&amp;group_id=224503&amp;atid=1061881">3011626</a>]
wrapper.script.STOP
doesn't work</li>


















  <li>Bug: [<a href="http://sourceforge.net/tracker/?func=detail&amp;aid=3011629&amp;group_id=224503&amp;atid=1061881">3011629</a>]
Windows: service shutdown does not wait for application termination.</li>


















  <li>Bug: Exception in CyclicBufferFileInputStream if file is
closed.</li>


















  <li>Bug: In case the same filter trigger fires for consecutive
lines, at random the scripts are called with the wrong line.</li>


















  <li>Bug: Cannot start application in case main method is
inherited from a super class.</li>


















  <li><span style="font-weight: bold;">!! Change: [<a href="http://sourceforge.net/tracker/?func=detail&amp;aid=3011629&amp;group_id=224503&amp;atid=1061881">3011629</a>]
new configuration properties: </span><span style="font-style: italic;">
wrapper.filter.script.&lt;n&gt;.timeout</span>,<span style="font-style: italic;">
wrapper.script.&lt;state&gt;.timeout</span><span style="font-weight: bold;">.</span></li>


















  <li>Change: updated to newest releases of: groovy, jna, quartz,
velocity</li>


















  <li>Change: System tray icon now uses asyncrhonous hessian over
netty and
multicast discovery instead of rmi.</li>


















  <li>Change: [<a href="http://sourceforge.net/tracker/?func=detail&amp;aid=3004022&amp;group_id=224503&amp;atid=1061884">3004022</a>]
Linux: a group is defined by setting
wrapper.app.account = &lt;group&gt;\&lt;user&gt; (note:
use \\ in
configuration file).&nbsp;if no group is configured change to
default group of the user. </li>


















  <li><span style="font-weight: bold;">!! Change:
Posix: the syntax of </span><span style="font-style: italic; font-weight: bold;">wrapper.daemon.run_level_dir</span><span style="font-weight: bold;"> has changed</span>:&nbsp;wrapper.daemon.run_level_dir
per default ist set to&nbsp;<span style="font-style: italic;">&lt;wrapper.daemon.dir&gt;/rcX.d</span>,
for Ubuntu this property should be set to /etc/rcX.d.</li>


















  <li><span style="font-weight: bold;">!! Change:
Posix: the property <span style="font-style: italic;">wrapper.daemon.k_order</span>
has been removed</span>. Use&nbsp;&nbsp;<span style="font-style: italic;">wrapper.daemon.update_rc </span>instead.</li>


















  <li><span style="font-weight: bold;">!! New:
Posix: &nbsp;</span><span style="font-style: italic; font-weight: bold;">wrapper.daemon.update_rc</span><span style="font-weight: bold;"> property for defining start and
stop run levels</span>. Syntax is similar to the <a href="http://www.debuntu.org/how-to-manage-services-with-update-rc.d">update-rc.d</a>
command. Default value is the default of &nbsp;that command.<span style="font-weight: bold;"></span></li>


















  <li><span style="font-weight: bold;"></span>New:
System tray icon console: pause, clear &amp; filter
console output.</li>


















  <li>New: wrapper.filter.trigger.1=x,
&nbsp;wrapper.filter.script.1=,&nbsp;wrapper.filter.trigger.2=x,
&nbsp;wrapper.filter.script.2= will execute scripts and actions in
alpha-numeric order of&nbsp; trigger key (&lt;n&gt;) thus
allowing one to define the order in which scripts are invoked for the
same trigger or for the same state change.</li>


















</ul>


















<h4>yajsw-beta-10.2 April 12th 2010</h4>


















<ul>


















  <li>Bug: genConfig: generating configuration file for "java
-jar x.jar"</li>


















  <li>Bug: Windows: when server is under high load: cannot stop
wrapped process: isRunning() returns true although process is stopped</li>


















  <li>Bug: sendMail.gv: add try/catch - thanks to jeff 303 for
the patch</li>


















  <li>!! Bug: since beta 10.1: Socket.ReuseAddress(true) in
wrapper -&gt; multiple wrappers use same port.</li>


















  <li>Bug: Mac OS X: genConfig uses posix methods instead of BSD.</li>


















  <li>Bug: Mac OS X: socket exception when restarting
application. -&gt; keep wrapper server socket bound on restart.</li>


















  <li>Changed: removed counter in &nbsp;WrapperManagerImpl.
Now waiting until startup timeout.</li>


















  <li>Changed: logging in WrapperManagerImpl if wrapper.debug=true</li>


















  <li>Changed: extended JSW migration guide to include
environment variables</li>


















  <li>New: experimental - multicast discovery: service manager
client
can discover service manager servers and the port on which they are
listening.</li>


















  <li>New: sample configurations for activemq and jboss</li>


















  <li>New: configuration property <span style="font-style: italic;">wrapper.java.debug.port</span>
-&gt; sets the java -Xdebug ...&nbsp; property and overrides
timeouts to maximum value</li>


















  <li>New: configuration property<span style="font-style: italic;"> wrapper.jmx.user</span>, <span style="font-style: italic;">wrapper.jmx.password</span>
-&gt; sets the credentials for remote access to the wrapper jmx
server</li>


















  <li>New: included sample security profile file. Fine grained
security profile can be generated using src\build\secmanager.</li>


















  <li>New: <span style="font-style: italic;">wrapper.app.pre_main.script</span>:
script executed before the
main method is invoked but after the WrapperManager has been configured
and a connection has been established to the wrapper.</li>


















</ul>


















<h4></h4>


















<h4>yajsw-beta-10.1 February 20th 2010</h4>


















<ul>


















  <li>Bug: Windows: JVM crashed when getting process environment</li>


















  <li>Bug: ServiceManagerServer: minor bugs.</li>


















  <li>Bug: Linux genConfig: NullPointerException getting
classpath from environment</li>


















  <li>Bug: Linux: Classpath File not found</li>


















  <li>Change: Network Install: can use yajsw zip from sourceforge</li>


















  <li>Change: Hessian Android-able, first step to service manager
client on android -&gt; removed hessian.jar from lib folder</li>


















</ul>


















<h4>yajsw-beta-10.0 February 7th 2010</h4>


















<ul>


















  <li>Bug:&nbsp;genconfig : error parsing java command line</li>


















  <li>Bug: Exception in VFS due to
DocumentBuilderFactory.newInstance(), when xerces is available in a jar
manifest but not in classpath</li>


















  <li>Bug: genConfig: get CLASSPATH from process environment</li>


















  <li>Bug: [2931270] Linux daemon&nbsp;only stops
after&nbsp;timeout expired</li>


















  <li>Bug: Linux: File handlers not closed: handlers and pipes
are now closed</li>


















  <li>Bug: Linux: ConnectionRefusedException on application
restart</li>


















  <li>Bug: In case of gobler panic: do not restart if already in
restart state.</li>


















  <li>Bug: Gobler checks old process -&gt; used process id</li>


















  <li>Bug: Linux: classpath wildcard error when path is absolute</li>


















  <li>Change: WrappedProcess.stop(): stop process if process is
in state RESTART.</li>


















  <li>Change: [2910062]move println to logging. Thanks to lokman
for the patch.</li>


















  <li>New: ServicesManager client + server - prototype windows
only</li>


















  <li>New: network loading: can load a zip -&gt; does not
required webdav. Demo changed to run from yajsw.sourceforge.net</li>


















</ul>


















<h4><br>


















</h4>


















<h4>yajsw-alpha-9.5 November 24th 2010</h4>


















<ul>


















  <li>Bug: Posix: pid file location</li>


















  <li>Bug: genConfig: &nbsp;<code>-Xms512m -Xmx1024m
-XX:MaxPermSize=256m not parsed correctly</code></li>


















  <li>Bug: MacOsX fork process</li>


















  <li>Bug: "operation on non socket" error on windows -&gt;
switched
from mina to netty, so that we can use OIO
instead of NIO.&nbsp; Added HANDLE_FLAG_PROTECT_FROM_CLOSE in
WindowsXPProcess.start(). This seems to help, but do not ask me why.</li>


















  <li>Bug: Since 9.4: error using commons Configuration in java
application. -&gt; split wrapper.jar &nbsp;in 2 jars:
wrapper.jar,
wrapperApp.jar.</li>


















  <li>Bug: Windows: service does not stop when application stops</li>


















  <li>Bug: Restart native application</li>


















  <li>Bug: Solaris installDaemon&nbsp;</li>


















  <li>Bug: [2875747] Posix: wrapper.shutdown.timeout has no effect</li>


















  <li>Bug: Posix: systemTrayIcon: start daemon -&gt; daemon
may hang:
tray icon&nbsp; should not start a sub process but call
&lt;link&gt;
start</li>


















  <li>Bug: Stack overflow in CyclicBufferFilePrintStream in case
wrapper is slower than application.</li>


















  <li>Bug: [892090] Enabling the tray icon is broken and not well
documented</li>


















  <li>Change: Posix: added conf/pid/description to daemon script
header</li>


















  <li>Change: optimized CyclicFileBuffer</li>


















  <li>Change: [2897101] Posix: multiple daemon run
levels:&nbsp;<span style="font-style: italic;">wrapper.daemon.run_level_dir.&lt;n&gt;,&nbsp;wrapper.daemon.k_order.&lt;n&gt;,&nbsp;wrapper.daemon.s_order.&lt;n&gt;</span>
for same &lt;n&gt; a k or s link is created in the given folder.</li>


















  <li>Change: newest jna.jar and groovy.jar</li>


















  <li>Change: genConfig: rewrite parsing java command line
-&gt; tools.jar no longer required.</li>


















  <li>Change: restructured src folder <span style="font-weight: bold;"></span><strong></strong></li>


















  <li>New: property to check how often a message must appear on
the screen within a time period: &nbsp;<span style="font-style: italic;">wrapper.filter.missing.trigger.&lt;n&gt;=poll
sucsessfull, 1, 10</span></li>


















  <li>New: property to define a script for calculation of restart
delay. <span style="font-style: italic;">wrapper.restart.delay.script</span>.
Thus restart delay can be set for example to a linear function, or to
check availability of resources before restarting.</li>


















  <li>New: property to set a pre application script which is
executed
within the wrapped application before the main method is executed: <span style="font-style: italic;">&nbsp;wrapper.app.pre.script</span>.
This can be used to define network printers and disks before starting a
service.</li>


















  <li>New: Windows Cluster: New method&nbsp; <span style="font-style: italic;">process.cluster.groupInfo</span>:
returns a Map&lt;String, String&gt; with &lt;group-name,
current-node-of-group&gt;. One may thus start/stop the
application&nbsp;
when specific cluster groups change.</li>


















  <li>New: Java Web Start support: boot wrapper through java web
start</li>


















  <li>New: enabled multiple commands: java -jar wrapper.jar <span style="font-style: italic;">-ity</span>
&lt;conf&gt; : install, start service and start tray icon.</li>


















</ul>


















<span style="font-weight: bold;">NOTE</span>:&nbsp;<br>


















take care when building wrapper.jar, wrapperApp.jar, wrapperWS.jar: <br>


















wrapper.jar <span style="font-style: italic;">should not
include</span> the classes: ...yajsw.app.* <span style="font-style: italic;">except for</span> *Impl*. <br>


















wrapperApp.jar <span style="font-style: italic;">includes
only</span> the classes ...yajsw.app.* + ...yajsw.boot.* <span style="font-style: italic;">except for</span> *Impl*<br>


















both have the same manifest.<br>


















<br>


















wrapperWS.jar is built by running the class WebStartBooter with JVM
option -verbose:class and using JarBuilder (see src/builder/...). This
jar has to be signed. You may use build/sign.bat for this.
<h4><br>


















</h4>


















<h4>yajsw-alpha-9.4 September 20 2009</h4>


















<ul>


















  <li>Bug: NPE in WindowsXPProcess.kill</li>


















  <li>Bug: windows: user account/password&nbsp;</li>


















  <li>Bug: win64 reconnect: getting command line NPE in case
server is overloaded</li>


















  <li>Bug: MacOsX: launchd typo</li>


















  <li>Bug: endless loop in CyclicFileBuffer</li>


















  <li>Bug: windows: service description corrupt</li>


















  <li>Change: newest mina.jar and jna.jar</li>


















  <li>New: loglevel STATUS, but log first 20 lines of app output
to see if something went wrong during startup.</li>


















  <li>New: unit tests using testNG and Groovy and jmock</li>


















  <li>New: groovy scripting in wrapper configuration -&gt;
groovy jar now part of core libs</li>


















  <li>New: enter passwords through CLI or system tray icon</li>


















</ul>


















NOTE: this release is buggy on MacOsX this will be solved in a next
release
<h4>yajsw-alpha-9.3 August 28 2009</h4>


















<ul>


















  <li>Bug: do not log restart when stopping service</li>


















  <li>Bug: Windows:&nbsp; time out on service stop</li>


















  <li>Bug: Remote Launch: /x/x could not be copied</li>


















  <li>Change: service install: if service is already installed
uninstall it first then reinstall it</li>


















  <li>Change: System Tray Icon now runs in a separate process.
New: Buttons for: stop wrapper, start service, wrapper thread dump</li>


















  <li>Change: Remote Launch: allow /**/ pattern in path for
recursive folder download</li>


















  <li>Change: JVMController: logging changed to info.</li>


















  <li>Change: avoid jna multiple loading of same library </li>


















  <li>New: cofiguration sample for <a href="http://db.apache.org/derby/">Apache Derby</a></li>


















</ul>


















<h4>yajsw-alpha-9.2 August 20 2009</h4>


















<ul>


















  <li>Bug: wrapper hangs on cntrl-c</li>


















  <li>Bug: network launch: webdav does not work -&gt; new
configuration-snapshot jar</li>


















  <li>Bug: network launch: handling of wild cards reworked. For
now supporting: .../*/*.jar only to one level.</li>


















  <li>Bug: service wrapper stops before application if condition
script is active</li>


















  <li>Bug: allow stopper configuration to set its own logging
properties</li>


















  <li>Bug: system tray hangs on wrapper exit</li>


















  <li>Change: jackrabit jar now includes only the webdav client</li>


















  <li>New: network launching demo: remote launch tomcat</li>


















</ul>


















<h4>yajsw-alpha-9.1 July 28 2009</h4>


















<ul>


















  <li>Bug: vfs-snapshot, configuration-snapshot jars built with
jdk 1.6 instead of jdk 1.5</li>


















  <li>Bug: [<span><strong>2828484]&nbsp;</strong></span>wrapper.java.initmemory.relative</li>


















</ul>


















<h4>yajsw-alpha-9.0 July 26 2009</h4>


















<ul>


















  <li>New: enable platform dependent includes:
include=${&lt;system-prop&gt;}.x.conf</li>


















  <li>New:<span style="font-style: italic;">
wrapper.restart.reload_configuration</span></li>


















  <li>New: Experimental: support jnlp similar
configuration:&nbsp;<span style="font-style: italic;">wrapper.codebase,
&nbsp;wrapper.cache</span>, &nbsp;<span style="font-style: italic;">
wrapper.cache.local</span>,&nbsp;<span style="font-style: italic;">wrapper.resource.&lt;n&gt;</span></li>


















  <li>New: Support JNLP configuration<span style="font-style: italic;"><br>


















    </span></li>


















  <li>New: support remote configuration file<span style="font-style: italic;"><br>


















    </span></li>


















  <li>New: <span style="font-style: italic;">wrapper.startup.delay</span></li>


















  <li>Bug:<span style="font-style: italic;"> </span>Windows:
Handle leak on native application restart</li>


















  <li>Bug: genConfig throws Exception when java command contains
main args</li>


















  <li>Bug: ubuntu 9.04-x86 crash in genConfig.sh</li>


















  <li>Bug: when wrapper.control = TIGHT, do not stop application
on JVMMain.WRAPPER_MANAGER.restart()</li>


















  <li>Bug: Windows: service requires long time to stop, since
alpha-8.4</li>


















  <li>Bug: Windows bat files</li>


















  <li>Bug: Posix shell scripts</li>


















  <li>Bug: Linux daemon script generation</li>


















  <li>Change: genConfig: service name of java application:
console title or main class name.</li>


















</ul>


















<h4>yajsw-alpha-8.4 June 1st 2009</h4>


















<ul>


















  <li>Bug: WrappedProcess.reconnect(pid) does not connect</li>


















  <li>Bug: WrappedRuntimeProcess: wrapper.working.dir not set</li>


















  <li>Bug: Exception on restart due to ping timeout</li>


















  <li>Bug: call to java "-Ddir=c:\" will cause a parse exception
in the java launcher</li>


















  <li>New: <span style="font-style: italic;">wrapper.control</span></li>


















  <li>New: windows cluster aware: <span style="font-style: italic;">wrapper.windows.cluster.script</span>&nbsp;</li>


















  <li>Bug: GenConfig: split jvmArgs so that we can quote them</li>


















</ul>


















<h4>yajsw-alpha-8.3 May 4th 2009</h4>


















<ul>


















  <li>New: property <span style="font-style: italic;">wrapper.ntservice.additional.&lt;n&gt;</span>
for setting service wrapper java options, generally this would be for
setting -Xmx</li>


















  <li>New: property <span style="font-style: italic;">wrapper.exit_on_main_exception</span>
for setting exit code of java application in case the main method
throws an exception</li>


















  <li>Bug: wrapping groovy scripts.</li>


















  <li>New: groovified YAJSW : WrapperBuilder</li>


















  <li>New: Sample: nutch recrawl script, nutch solr crawl script</li>


















  <li>Bug: Windows: on shutdown of wrapper jna
DeleteNativeLibrary exec may hang -&gt; do not spwan this exec.</li>


















  <li>Bug: wrapper.java.classpath.&lt;n&gt; = ${var}
&nbsp;not expanded</li>


















  <li>Bug: replace out.print with log.info</li>


















</ul>


















<h4>yajsw-alpha-8.2 April 16th 2009
</h4>


















<ul>


















  <li>New: Property <span style="font-style: italic;">wrapper.java.dump.override
= true</span> : Output thread dump to the wrapper tee stream,
thus overriding any streams set with System.setErr()</li>


















  <li>Bug: threaddump with backlog appender</li>


















  <li>Bug: space in application jar path</li>


















</ul>


















<h4>yajsw-alpha-8.1 April &nbsp;14th 2009</h4>


















<ul>


















  <li>Bug: Windows: avoid double quotes in command line when
starting a process or installing a service</li>


















  <li>Bug: Windows: error in case classpath file has absolute
path.</li>


















  <li>New: divided &lt;yajsw&gt;/lib directory to sub
directories. <a href="index.html#libFolder">see here</a>.
-&gt;&nbsp;<a href="http://sourceforge.net/tracker/?func=detail&amp;aid=2750853&amp;group_id=224503&amp;atid=1061884"><strong style="font-weight: normal;">Allow for more compact
distributions</strong></a></li>


















  <li>Bug: thread dump: use "original" stream to bypass logger
streams.</li>


















</ul>


















<h4>yajsw-alpha-8.0 April 11th 2009</h4>


















<ul>


















  <li>NEW: configuration property <span style="font-style: italic;">wrapper.script.&lt;state&gt;&nbsp;</span><span style="font-style: italic;">wrapper.script.&lt;state&gt;
.args</span></li>


















  <li>NEW : State <span style="font-style: italic;">SHUTDOWN</span></li>


















  <li>NEW: configuration property <span style="font-style: italic;">wrapper.port, wrapper.port.min,
wrapper.port.max</span></li>


















  <li>Bug: Posix, Windows: handling of spaces in path</li>


















  <li>NEW: support for FreeBSD 32 and 64</li>


















  <li>Change:
DEFAULT_EXIT_ON_MAIN_TERMINATE&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp; = -1 for compatibility with JSW</li>


















  <li>NEW: Linux, Solaris: System Tray Console: display threads,
cpu, memory, handles (handles == number of &nbsp;file descriptors)</li>


















  <li>NEW: commandCondition.gv script</li>


















  <li>NEW: runScript.bat to test scripts</li>


















  <li>Change: Install Service:
"remember" the lookup variables (eg ${..}) so we run the service with
the same environment variables as those used when the service was
installed -&gt; when an env var is changed the service has to be
reinstalled.</li>


















  <li>Change: wrapper.java.command: first check if this
is relative to wrapper.jar parent folder (eg wrapper home), thus
defining wrapper.java.command=jre/bin/java will resolve to
&lt;yajsw&gt;/jre/bin/java if this file exists</li>


















</ul>


















<h4>yajsw-alpha-7.1 March 22nd 2009</h4>


















<ul>


















  <li>NOTE:
WrapperExe now sets its working dir -&gt; may require change in
configuration if files are relative (example: use scripts/sendMail.gv
instead of ../scripts/sendMail.gv)</li>


















  <li>Bug: Linux: + MacOsX: missing templates folder + templates
folder not used&nbsp;</li>


















  <li>Bug: WindowsXPProcess: Exception while reading console
title&nbsp;under SYSTEM user</li>


















  <li>Bug: wrong state of WrappedProcess in case process does not
start.</li>


















  <li>Tested: Linux 64 bit (Suse 11.1 64 bit) :OK</li>


















  <li>Tested: FreeBSD 32 bit (FreeBSD 7.1) : OK (note: does not
yet run on FreeBSD 64 bit)</li>


















  <li>Bug: missing src folder</li>


















  <li>Change: check arguments in groovy scripts</li>


















  <li>Bug: parse trigger configuration properties.</li>


















  <li>Change: package refactoring for BSD + MacOsX</li>


















  <li>Tested: Oracle/JRockit JVM on Win32 : OK</li>


















  <li>Change: add log to groovy scripts - see example in
sendMail.gv</li>


















  <li>Change: log groovy script exceptions, so we may have these
on file.</li>


















  <li>Change:
wrapper working directory: executing runConsole or startService or
startDaemon should work with the same configuration even if files are
relative -&gt; set working directory of &nbsp;WrapperExe and
WrapperMainService* to the wrapper home directory (the location of
wrapper.jar)</li>


















  <li>New:
Alpha Release for solaris. Tested runConsole with sol-10-u6-ga1-x86.
Note: could not get stdout/in/err -&gt; workaround using default
file
numbers 0, 1, 2. genConfig + Daemon not yet tested.</li>


















</ul>


















<h4>yajsw-alpha-7.0 Feb 28th 2009</h4>


















<ul>


















  <li>New:
Initial Mac OS X support, Note: wrapper runs only with JDK 1.5 (32 bit)
Application may run on JDK 1.6 (64 bit), still buggy, no system tray
support</li>


















  <li>Bug: Linux:&nbsp; daemon.vm</li>


















  <li>New: Mac OS X:<span style="font-style: italic;">
wrapper.launchd.dir, wrapper.launchd.template</span> properties</li>


















  <li>Change:&nbsp;daemon.vm template and launchd.plist.vm
now in directory <span style="font-style: italic;">templates</span>.</li>


















  <li>Bug: Windows: Handle, thread, file, memory leak when
restarting process</li>


















  <li>New:
Extended System Tray to include application and wrapper status,
application resources (currently only winodws), writing to System.in (
currently only standard ascii characters)</li>


















  <li>New: full win64 support (tested on Windows 2003 x64).
Wrapper and Application may run on 64 bit jdk.</li>


















  <li>Bug: TeeInputStream.</li>


















  <li>Change: switched to MINA 2.0.0-M4 -&gt; use Timer
instead of &nbsp;KeepAliveFilter</li>


















  <li>Bug: Thread leak</li>


















  <li>Change: reworked linux scripts</li>


















  <li>Bug: class loading : slf4j issue</li>


















  <li>Bug: Linux: System.out hangs</li>


















  <li>Change:<span style="font-style: italic;">
wrapper.console.visible</span>: default: false</li>


















  <li>Change: using Cycler (Thread.sleep)&nbsp; instead of
Timer, so we are not influenced by system time changes.</li>


















  <li>Change: pinger Thread with high priority, so we do not miss
pings.</li>


















  <li>Bug: short living applications state remains running</li>


















</ul>


















<h4>yajsw-alpha-6.0 January 5th, 2009</h4>


















<ul>


















  <li>New: property <span style="font-style: italic;">wrapper.groovy</span>:
wrap groovy scripts</li>


















  <li>New: property <span style="font-style: italic;">wrapper.stop.conf</span>
: stop a wrapped application by invoking another application</li>


















  <li>Change: genConfig: paths in generated configuration are now
realtive to workingDir.</li>


















  <li>New: script for sending SNMP traps</li>


















  <li>New:
Linux:
properties<span style="font-style: italic;">&nbsp;wrapper.daemon.run_level_dir</span>,&nbsp;<span style="font-style: italic;">wrapper.daemon.k_orde</span>r,
&nbsp;<span style="font-style: italic;">wrapper.daemon.s_order</span>.
wrapper -i now installs symbolic links to
daemon script</li>


















  <li>New: property <span style="font-style: italic;">wrapper.ntservice.starttype,&nbsp;</span><span style="font-style: italic;">wrapper.ntservice.account,&nbsp;</span><span style="font-style: italic;">wrapper.ntservice.password</span></li>


















  <li>Change: Linux: default pid dir: /var/run</li>


















  <li>New:
property <span style="font-style: italic;">wrapper.app.account</span>
and <span style="font-style: italic;">wrapper.app.password</span>
Is
used for Linux and Windows. It is used for nt services, posix daemons,
and console applications.</li>


















  <li>Bug: file conf/daemon.gv missing in alpha-5.1</li>


















  <li>New: property <span style="font-style: italic;">wrapper.tray,
wrapper.tray.icon</span> : enable system tray icon.</li>


















  <li>New: script for displaying tray messages</li>


















  <li>New: properties&nbsp;<span style="font-style: italic;">wrapper.java.maxmemory,
wrapper.java.maxmemory.relative, &nbsp;wrapper.java.initialmemory,
wrapper.java.initialmemory.relative</span></li>


















  <li>Bug: Linux: spaces in command line calling application
removed.</li>


















  <li>New: genConfig: get application account</li>


















  <li>Change: Log file: directory is created if it does not exist</li>


















</ul>


















<h4>yajsw-alpha-5.1 December 22, 2008</h4>


















<ul>


















  <li>Bug: genConfig.bat and genConfig.sh</li>


















</ul>


















<h4>yajsw-alpha-5.0 December 21, 2008</h4>


















<ul>


















  <li>New: groovy scripts can now be passed arguments from the
wrapper configuration properties</li>


















  <li>New:
condition scripts for controlling process execution through conditions
such as time interval, file existence, mail receiption etc.</li>


















  <li>New: groovy scripts maintain their bindings between
invocations</li>


















  <li>New: -q, -qs options for service status query</li>


















  <li>Linux: configuration generator now available for Linux</li>


















  <li>Linux:
java -jar wrapper.jar options -i (install) -t (start) -s(stop) -q/-qs
(query) are now available for Linux and Windows.&nbsp;</li>


















  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>


















    <li>-i creates a script for starting a wrapper daemon and
copies the file to /etc/init.d</li>


















    <li>-t starts the daemon</li>


















    <li>-s stops the daemon</li>


















    <li>-u removes the script from the daemon dir.</li>


















  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>


















  <li>New: STATE_ABORT: in case the process could not be started
after max_failed_invocations.</li>


















</ul>


















<h4>yajsw-alpha-4.0 &nbsp;November 16 2008</h4>


















<ul>


















  <li>New:
Configuration Generator, Given the pid of a running process, generate a
configuration file to wrap the process (currently available only for
win32)</li>


















  <li>New: JMX support</li>


















  <li>Resolved bug with wrapper.tmp.path</li>


















</ul>


















<h4>yajsw-alpha-3.1&nbsp;November 5 2008</h4>


















<ul>


















  <li>Resolved bug with wrapper.timer.cron&nbsp;</li>


















</ul>


















<h4> yajsw-alpha-3.0&nbsp;November 2 2008</h4>


















<ul>


















  <li>repackaged source code, renamed some classes. NOTE:
&nbsp;org.rzo.yajsw.WrapperMain -&gt;
org.rzo.yajsw.app.WrapperJVMMain</li>


















  <li>added support for wrapping native exes. Restriction: cannot
reconnect to native exes.</li>


















  <li>added support for Linux. The following functions are not
yet available for Linux: visible console, automatic install of daemons</li>


















  <li>This version is stable on Windows and is being used in
production on win32 and win64</li>


















</ul>


















<h4>yajsw-alpha-2.0 August 20 2008</h4>


















<ul>


















  <li>Regular &nbsp;Expressions Filters</li>


















  <li>Groovy and Shell Script actions</li>


















  <li>Sample Groovy script for sending emails</li>


















  <li>Job Scheduling with Quartz</li>


















  <li>Windows services now bug free</li>


















  <li>-Xrs is inherited to sub-processes if running as service</li>


















  <li>Cpu affinity and process priority</li>


















  <li>Automatic JVM selection</li>


















  <li>Separate class loading for YAJSW libraries and for JVM
Application</li>


















</ul>


















<h4>yajsw-alpha-1.0 April 14 2008</h4>


















Initial release<br>


















This Alpha 1.0 Release is a proof of concept implementing only the main
functions and configuration properties.<br>


















<br>


















</div>


















</div>


















</body>
</html>
