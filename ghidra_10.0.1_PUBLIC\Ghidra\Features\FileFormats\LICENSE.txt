Ghidra software is released under the Apache 2.0 license. In addition, 
there are numerous 3rd party components that each have their 
own license. The license file for each of these licenses can be found
in the licenses directory in the installation root directory.

The 3rd party files in this module are as follows:


BSD:

	lib/baksmali-1.4.0.jar
	lib/util-1.4.0.jar

Apache License 2.0:

	src/main/java/ghidra/file/formats/sparseimage/SparseImageDecompressor.java
	src/main/java/ghidra/file/formats/sparseimage/ChunkHeader.java
	src/main/java/ghidra/file/formats/sparseimage/SparseConstants.java
	src/main/java/ghidra/file/formats/sparseimage/SparseHeader.java
	src/main/java/ghidra/file/formats/android/xml/AndroidXmlConvertor.java
	lib/AXMLPrinter2.jar
	lib/dex-reader-api-2.0.jar
	lib/dexlib-1.4.0.jar
	lib/dex-translator-2.0.jar
	lib/dex-reader-2.0.jar
	lib/dex-ir-2.0.jar

INRIA License:

	lib/asm-debug-all-4.1.jar

LGPL 2.1:

	lib/sevenzipjbinding-all-platforms-16.02-2.01.jar
	lib/sevenzipjbinding-16.02-2.01.jar

Public Domain:

	src/main/java/ghidra/file/formats/ios/png/CrushedPNGUtil.java

Bouncy Castle License:

	lib/bcprov-jdk15on-1.68.jar

