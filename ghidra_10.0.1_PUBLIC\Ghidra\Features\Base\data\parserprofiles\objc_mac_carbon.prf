objc\objc-runtime.h
objc\objc-load.h
objc\objc-auto.h
objc\objc-exception.h
fp.h
MacTypes.h
CFMachPort.h
CGRemoteOperation.h
Quickdraw.h
TextUtils.h
Accessibility.h
AE.h
AEDataModel.h
AEHelpers.h
AEInteraction.h
AEMach.h
AEObjects.h
AEPackObject.h
AERegistry.h
AEUserTermTypes.h
AIFF.h
Aliases.h
Appearance.h
AppleDiskPartitions.h
AppleEvents.h
AppleHelp.h
AppleScript.h
ApplicationServices.h
ASDebugging.h
ASRegistry.h
AssertMacros.h
ATS.h
ATSFont.h
ATSLayoutTypes.h
ATSTypes.h
ATSUnicode.h
ATSUnicodeDirectAccess.h
ATSUnicodeDrawing.h
ATSUnicodeFlattening.h
ATSUnicodeFonts.h
ATSUnicodeGlyphs.h
ATSUnicodeObjects.h
ATSUnicodeTypes.h
AvailabilityMacros.h
AVLTree.h
AXActionConstants.h
AXAttributeConstants.h
AXConstants.h
AXError.h
AXNotificationConstants.h
AXRoleConstants.h
AXTextAttributedString.h
AXUIElement.h
AXValue.h
AXValueConstants.h
Carbon.h
CarbonCore.h
CarbonEvents.h
CarbonEventsCore.h
CarbonSound.h
CGBase.h
CFArray.h
CFAttributedString.h
CFBag.h
CFBase.h
CFBinaryHeap.h
CFBitVector.h
CFBundle.h
CFByteOrder.h
CFCalendar.h
CFCharacterSet.h
CFData.h
CFDate.h
CFDateFormatter.h
CFDictionary.h
CFFTPStream.h
CFHost.h
CFHTTPAuthentication.h
CFHTTPMessage.h
CFHTTPStream.h
CFLocale.h
CFMessagePort.h
CFNetDiagnostics.h
CFNetServices.h
CFNetwork.h
CFNetworkDefs.h
CFNotificationCenter.h
CFNumber.h
CFNumberFormatter.h
CFPlugIn.h
CFPlugInCOM.h
CFPreferences.h
CFPropertyList.h
CFRunLoop.h
CFSet.h
CFSocket.h
CFSocketStream.h
CFStream.h
CFString.h
CFStringEncodingExt.h
CFTimeZone.h
CFTree.h
CFURL.h
CFURLAccess.h
CFUserNotification.h
CFUUID.h
CFXMLNode.h
CFXMLParser.h
CGAffineTransform.h
CGBitmapContext.h
CGColor.h
CGColorSpace.h
CGContext.h
CGDataConsumer.h
CGDataProvider.h
CGDirectDisplay.h
CGDirectPalette.h
CGDisplayConfiguration.h
CGDisplayFade.h
CGError.h
CGEvent.h
CGEventSource.h
CGEventTypes.h
CGFont.h
CGFunction.h
CGGeometry.h
CGGLContext.h
CGImage.h
CGImageDestination.h
CGImageProperties.h
CGImageSource.h
CGLayer.h
CGPath.h
CGPattern.h
CGPDFArray.h
CGPDFContentStream.h
CGPDFContext.h
CGPDFDictionary.h
CGPDFDocument.h
CGPDFObject.h
CGPDFOperatorTable.h
CGPDFPage.h
CGPDFScanner.h
CGPDFStream.h
CGPDFString.h
CGPSConverter.h
CGSession.h
CGShading.h
CGWindowLevel.h
CMApplication.h
CMCalibrator.h
CMDeviceIntegration.h
CMFloatBitmap.h
CMICCProfile.h
CMMComponent.h
CMScriptingPlugin.h
CMTypes.h
CodeFragments.h
Collections.h
ColorPicker.h
ColorSync.h
CommonPanels.h
Components.h
Components.k.h
ConditionalMacros.h
ControlDefinitions.h
Controls.h
CoreFoundation.h
CoreGraphics.h
CoreServices.h
DateTimeUtils.h
Debugging.h
Devices.h
Dialogs.h
Dictionary.h
DigitalHubRegistry.h
Displays.h
Drag.h
DriverServices.h
DriverSynchronization.h
Endian.h
Events.h
Files.h
FindByContent.h
Finder.h
FinderRegistry.h
FixMath.h
Folders.h
FontPanel.h
Fonts.h
FontSync.h
Gestalt.h
Help.h
HFSVolumes.h
HIArchive.h
HIGeometry.h
HIMovieView.h
HIObject.h
HIServices.h
HIShape.h
HITextUtils.h
HITheme.h
HIToolbar.h
HIToolbox.h
HIView.h
HTMLRendering.h
IBCarbonRuntime.h
ICAApplication.h
ICACamera.h
ICADevice.h
Icons.h
IconStorage.h
ImageCapture.h
ImageCodec.h
ImageCodec.k.h
ImageCompression.h
ImageCompression.k.h
ImageIO.h
Ink.h
InternetConfig.h
IntlResources.h
Keyboards.h
KeychainCore.h
KeychainHI.h
LangAnalysis.h
LanguageAnalysis.h
LaunchServices.h
Lists.h
LowMem.h
LSInfo.h
LSOpen.h
MacApplication.h
MacErrors.h
MacHelp.h
MachineExceptions.h
MacLocales.h
MacMemory.h
MacTextEditor.h
MacWindows.h
Math64.h
MDImporter.h
MDItem.h
MDQuery.h
MDSchema.h
MediaHandlers.h
MediaHandlers.k.h
Menus.h
Metadata.h
MixedMode.h
Movies.h
Movies.k.h
MoviesFormat.h
Multiprocessing.h
MultiprocessingInfo.h
NameRegistry.h
Navigation.h
NavigationServices.h
Notification.h
NSL.h
NSLCore.h
NumberFormatting.h
OpenScripting.h
OpenTransport.h
OpenTransportProtocol.h
OpenTransportProviders.h
OSA.h
OSAComp.h
OSAGeneric.h
OSServices.h
OSUtils.h
OT.h
Palettes.h
Pasteboard.h
PEFBinaryFormat.h
PictUtils.h
PLStringFuncs.h
PMApplication.h
PMCore.h
PMDefinitions.h
PMErrors.h
PMIOModule.h
PMPluginHeader.h
PMPrintAETypes.h
PMPrinterBrowsers.h
PMPrinterModule.h
PMPrintingDialogExtensions.h
PMRaster.h
PMTemplate.h
PMTicket.h
Power.h
PPDLib.h
Print.h
PrintCore.h
Processes.h
QD.h
QDOffscreen.h
QDPictToCGContext.h
QTML.h
QTSMovie.h
QTSMovie.k.h
QTStreamingComponents.h
QTStreamingComponents.k.h
QuickdrawText.h
QuickTime.h
QuickTimeComponents.h
QuickTimeComponents.k.h
QuickTimeErrors.h
QuickTimeMusic.h
QuickTimeMusic.k.h
QuickTimeStreaming.h
QuickTimeVR.h
QuickTimeVRFormat.h
Resources.h
ScalerStreamTypes.h
Scrap.h
Script.h
SCSI.h
SearchKit.h
SecCertificateSupport.h
SecurityCore.h
SecurityHI.h
SFNTLayoutTypes.h
SFNTTypes.h
SKAnalysis.h
SKDocument.h
SKIndex.h
SKSearch.h
SKSummary.h
Sound.h
Sound.k.h
SpeechRecognition.h
SpeechSynthesis.h
StringCompare.h
SystemSound.h
TargetConditionals.h
TextCommon.h
TextEdit.h
TextEncodingConverter.h
TextEncodingPlugin.h
TextServices.h
Threads.h
Timer.h
ToolUtils.h
Translation.h
TranslationExtensions.h
TranslationServices.h
TSMTE.h
TypeSelect.h
UnicodeConverter.h
UnicodeUtilities.h
UniversalAccess.h
URLAccess.h
UTCoreTypes.h
UTCUtils.h
UTType.h
Video.h
WebServicesCore.h
WSMethodInvocation.h
WSProtocolHandler.h
WSTypes.h

-IT:\SDKs\MacOSX10.4u.sdk\usr\include
-IT:\SDKs\MacOSX10.4u.sdk\Developer\Headers\CFMCarbon
-IT:\SDKs\MacOSX10.4u.sdk\System\Library\Frameworks\Kernel.framework\Versions\A\Headers
-IT:\SDKs\MacOSX10.4u.sdk\usr\lib\gcc\powerpc-apple-darwin8\4.0.1\include
-D__STRICT_ANSI__
-D__STDC_VERSION__=199900
-D__builtin_va_list="void *"
-D__const=""
-D_INTEGRAL_MAX_BITS=32
-D__m128="long long"
-D__m128i="long long"
-D__m128d="long long"
-D__ppc__
-D__GNUC__
-D__APPLE_CPP__
-D__BIG_ENDIAN__
-D__MACH__
-D_MAC_
-DTARGET_API_MAC_OSX
-DTARGET_CARBON
-DHANDLE="unsigned long"
-D_Bool="BOOL"
