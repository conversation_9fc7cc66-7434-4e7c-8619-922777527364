aio.h
arpa\inet.h
assert.h
complex.h
cpio.h
ctype.h
dirent.h
dlfcn.h
errno.h
fcntl.h
fenv.h
float.h
fmtmsg.h
fnmatch.h
ftw.h
glob.h
grp.h
iconv.h
inttypes.h
iso646.h
langinfo.h
libgen.h
limits.h
locale.h
math.h
monetary.h
mqueue.h
net\if.h
netdb.h
netinet\in.h
netinet\tcp.h
nl_types.h
poll.h
pthread.h
pwd.h
regex.h
sched.h
search.h
semaphore.h
setjmp.h
signal.h
spawn.h
stdarg.h
stdbool.h
stddef.h
stdint.h
stdio.h
stdlib.h
string.h
strings.h
stropts.h
sys\ipc.h
sys\mman.h
sys\msg.h
sys\resource.h
sys\select.h
sys\sem.h
sys\shm.h
sys\socket.h
sys\stat.h
sys\statvfs.h
sys\time.h
sys\times.h
sys\types.h
sys\uio.h
sys\un.h
sys\utsname.h
sys\wait.h
syslog.h
tar.h
termios.h
time.h
ulimit.h
unistd.h
utime.h
utmpx.h
wchar.h
wctype.h
wordexp.h

-IU:\linux_include\include
-IU:\linux_include\include\sys
-IU:\linux_include\i386-redhat-linux\4.1.2\include
-D_X86_
-D__STDC__
-D__WORDSIZE=32
-D__builtin_va_list=void *
-D__DO_NOT_DEFINE_COMPILE
-D_Complex
-D_WCHAR_T
