sal.h
assert.h
conio.h
crtdefs.h
crtdbg.h
crtwrn.h
ctype.h
basetsd.h
WinDef.h
WinNT.h
delayimp.h
direct.h
dos.h
errno.h
excpt.h
fcntl.h
float.h
fpieee.h
io.h
iso646.h
limits.h
locale.h
malloc.h
math.h
mbctype.h
mbstring.h
memory.h
minmax.h
new.h
omp.h
pgobootrun.h
process.h
rtcapi.h
search.h
setjmp.h
setjmpex.h
share.h
signal.h
srv.h
stdarg.h
stddef.h
stdexcpt.h
stdio.h
stdlib.h
string.h
tchar.h
time.h
use_ansi.h
vadefs.h
varargs.h
wchar.h
wctype.h
xlocinfo.h
xmath.h
ymath.h
yvals.h
CommDlg.h
WinUser.h
WinNls.h
internal.h
strsafe.h
/VisualStudio/VS12/src/align.h
/VisualStudio/VS12/src/assert.h
/VisualStudio/VS12/src/awint.h
/VisualStudio/VS12/src/conio.h
/VisualStudio/VS12/src/crtdbg.h
/VisualStudio/VS12/src/crtdefs.h
/VisualStudio/VS12/src/crtversion.h
/VisualStudio/VS12/src/crtwrn.h
/VisualStudio/VS12/src/cruntime.h
/VisualStudio/VS12/src/ctime.h
/VisualStudio/VS12/src/ctype.h
/VisualStudio/VS12/src/cvt.h
/VisualStudio/VS12/src/dbgint.h
/VisualStudio/VS12/src/direct.h
/VisualStudio/VS12/src/dos.h
/VisualStudio/VS12/src/ehdata.h
/VisualStudio/VS12/src/emmintrin.h
/VisualStudio/VS12/src/errmsg.h
/VisualStudio/VS12/src/errno.h
/VisualStudio/VS12/src/excpt.h
/VisualStudio/VS12/src/fcntl.h
/VisualStudio/VS12/src/fenv.h
/VisualStudio/VS12/src/file2.h
/VisualStudio/VS12/src/float.h
/VisualStudio/VS12/src/fltintrn.h
/VisualStudio/VS12/src/fpieee.h
/VisualStudio/VS12/src/immintrin.h
/VisualStudio/VS12/src/internal.h
/VisualStudio/VS12/src/internal_securecrt.h
/VisualStudio/VS12/src/inttypes.h
/VisualStudio/VS12/src/io.h
/VisualStudio/VS12/src/isa_availability.h
/VisualStudio/VS12/src/iso646.h
/VisualStudio/VS12/src/limits.h
/VisualStudio/VS12/src/locale.h
/VisualStudio/VS12/src/malloc.h
/VisualStudio/VS12/src/math.h
/VisualStudio/VS12/src/mbctype.h
/VisualStudio/VS12/src/mbdata.h
/VisualStudio/VS12/src/mbstring.h
/VisualStudio/VS12/src/memory.h
/VisualStudio/VS12/src/minmax.h
/VisualStudio/VS12/src/mmintrin.h
/VisualStudio/VS12/src/msdos.h
/VisualStudio/VS12/src/mtdll.h
/VisualStudio/VS12/src/new.h
/VisualStudio/VS12/src/nlsdownlevel.h
/VisualStudio/VS12/src/nlsint.h
/VisualStudio/VS12/src/nmmintrin.h
/VisualStudio/VS12/src/oscalls.h
/VisualStudio/VS12/src/pmmintrin.h
/VisualStudio/VS12/src/process.h
/VisualStudio/VS12/src/rtcapi.h
/VisualStudio/VS12/src/rtcsup.h
/VisualStudio/VS12/src/rterr.h
/VisualStudio/VS12/src/search.h
/VisualStudio/VS12/src/sect_attribs.h
/VisualStudio/VS12/src/setjmp.h
/VisualStudio/VS12/src/setjmpex.h
/VisualStudio/VS12/src/setlocal.h
/VisualStudio/VS12/src/share.h
/VisualStudio/VS12/src/signal.h
/VisualStudio/VS12/src/smmintrin.h
/VisualStudio/VS12/src/stdarg.h
/VisualStudio/VS12/src/stdbool.h
/VisualStudio/VS12/src/stddef.h
/VisualStudio/VS12/src/stdexcpt.h
/VisualStudio/VS12/src/stdint.h
/VisualStudio/VS12/src/stdio.h
/VisualStudio/VS12/src/stdlib.h
/VisualStudio/VS12/src/string.h
/VisualStudio/VS12/src/syserr.h
/VisualStudio/VS12/src/targetver.h
/VisualStudio/VS12/src/tchar.h
/VisualStudio/VS12/src/time.h
/VisualStudio/VS12/src/tmmintrin.h
/VisualStudio/VS12/src/use_ansi.h
/VisualStudio/VS12/src/vadefs.h
/VisualStudio/VS12/src/varargs.h
/VisualStudio/VS12/src/wchar.h
/VisualStudio/VS12/src/wctype.h
/VisualStudio/VS12/src/winheap.h
/VisualStudio/VS12/src/wmmintrin.h
/VisualStudio/VS12/src/wrapwin.h
/VisualStudio/VS12/src/xkeycheck.h
/VisualStudio/VS12/src/xlocinfo.h
/VisualStudio/VS12/src/xmath.h
/VisualStudio/VS12/src/xmmintrin.h
/VisualStudio/VS12/src/xmtx.h
/VisualStudio/VS12/src/xtgmath.h
/VisualStudio/VS12/src/xxcctype.h
/VisualStudio/VS12/src/xxdftype.h
/VisualStudio/VS12/src/xxfftype.h
/VisualStudio/VS12/src/xxlftype.h
/VisualStudio/VS12/src/xxwctype.h
/VisualStudio/VS12/src/xxxprec.h
/VisualStudio/VS12/src/ymath.h
/VisualStudio/VS12/src/yvals.h
shlobj.h
evntprov.h
uiautomation.h
aclapi.h
appcompatapi.h
capi.h
clusapi.h
cryptuiapi.h
cscapi.h
devpropdef.h
dhcpsapi.h
dwmapi.h
ehstorapi.h
functiondiscoveryapi.h
ipexport.h
icmpapi.h
iepmapi.h
imapi.h
ksopmapi.h
locationapi.h
lpmapi.h
mapi.h
mbnapi.h
mfapi.h
mgmtapi.h
mmdeviceapi.h
mprapi.h
msctfmonitorapi.h
ndfapi.h
netioapi.h
npapi.h
nspapi.h
ntdsapi.h
ntmsapi.h
ntsecapi.h
patchapi.h
portabledeviceapi.h
portabledeviceconnectapi.h
propapi.h
psapi.h
rdpencomapi.h
resapi.h
sapi.h
searchapi.h
sensapi.h
sensorsapi.h
setupapi.h
shellapi.h
shlwapi.h
srrestoreptapi.h
svrapi.h
t2embapi.h
tapi.h
uiautomationcoreapi.h
wcnapi.h
wdsclientapi.h
werapi.h
windowssideshowapi.h
wlanapi.h
wpapi.h
wpcapi.h
wscapi.h
wsdapi.h
wspiapi.h

-I/VisualStudio/VS12/include
-I/VisualStudio/Windows/v7.0a/Include
-I/VisualStudio/VS12/src
-D_M_IX86
-D_MSC_VER=9090
-D_INTEGRAL_MAX_BITS=32
-DWINVER=0x0900
-D_X86_
-D_WIN32
-D_USE_DECLSPECS_FOR_SAL=0
-D_USE_ATTRIBUTES_FOR_SAL=0
-DCRTDLL
-D_CRTBLD
-D_OPENMP_NOFORCE_MANIFEST
-DSTRSAFE_LIB
-DSTRSAFE_LIB_IMPL
-DLPSKBINFO=LPARAM
-D_WCHAR_T_DEFINED
-DCONST=const
-D_CRT_SECURE_NO_WARNINGS
-D_CRT_NONSTDC_NO_DEPRECATE
-D_CRT_NONSTDC_NO_WARNINGS
-D_CRT_OBSOLETE_NO_DEPRECATE
-D_ALLOW_KEYWORD_MACROS
-D_ASSERT_OK
-D__possibly_notnullterminated
-Dtype_info="void *"
-v0
