﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{343E9778-3C04-476E-8F90-A114AA7AA108}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
    <SpectreMitigation>false</SpectreMitigation>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
    <SpectreMitigation>false</SpectreMitigation>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>headers;$(VSInstallDir)DIA SDK\include;$(IncludePath)</IncludePath>
    <LibraryPath>$(VSInstallDir)DIA SDK\lib\amd64;$(LibraryPath)</LibraryPath>
    <ReferencePath>$(VCInstallDir)lib\amd64;</ReferencePath>
    <SourcePath />
    <ExcludePath>$(VCInstallDir)include;$(VCInstallDir)lib\amd64</ExcludePath>
    <OutDir>$(SolutionDir)..\..\build\os\win64\</OutDir>
    <IntDir>$(SolutionDir)..\..\build\tmp\pdb\$(Platform)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IncludePath>headers;$(VSInstallDir)DIA SDK\include;$(IncludePath)</IncludePath>
    <LibraryPath>$(VSInstallDir)DIA SDK\lib\amd64;$(LibraryPath)</LibraryPath>
    <ReferencePath>$(VCInstallDir)lib\amd64</ReferencePath>
    <SourcePath />
    <ExcludePath>$(VCInstallDir)include;$(WindowsSDK_IncludePath);$(MSBuildToolsPath);$(MSBuildToolsPath32);$(VCInstallDir)lib\amd64;</ExcludePath>
    <OutDir>$(SolutionDir)..\..\build\os\win64\</OutDir>
    <IntDir>$(SolutionDir)..\..\build\tmp\pdb\$(Platform)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <Optimization>Disabled</Optimization>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>DebugFull</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OutputFile>$(TargetPath)</OutputFile>
    </Link>
    <PostBuildEvent>
      <Command>
rem copy /y /v /b "$(VSInstallDir)DIA SDK\bin\amd64\msdia*.dll" "$(OutDir)"
set OS_DIR=$(SolutionDir)..\..\os
if exist "%OS_DIR%\win64" (
   mkdir "%OS_DIR%"
   mkdir "%OS_DIR%\win64"
   xcopy /Y /S "$(OutDir)" "%OS_DIR%"
)
  </Command>
    </PostBuildEvent>
    <PreLinkEvent>
      <Command>
      </Command>
    </PreLinkEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;WINVER=0x0601;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalOptions>
      </AdditionalOptions>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <OutputFile>$(TargetPath)</OutputFile>
    </Link>
    <PostBuildEvent>
      <Command>
rem copy /y /v /b "$(VSInstallDir)DIA SDK\bin\amd64\msdia*.dll" "$(OutDir)"
set OS_DIR=$(SolutionDir)..\..\os
if exist "%OS_DIR%\win64" (
   mkdir "%OS_DIR%"
   mkdir "%OS_DIR%\win64"
   xcopy /Y /S "$(OutDir)" "%OS_DIR%"
)
  </Command>
    </PostBuildEvent>
    <PreLinkEvent>
      <Command>
      </Command>
    </PreLinkEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="cpp\err.cpp" />
    <ClCompile Include="cpp\find.cpp" />
    <ClCompile Include="cpp\iterate.cpp" />
    <ClCompile Include="cpp\main.cpp" />
    <ClCompile Include="cpp\pdb.cpp" />
    <ClCompile Include="cpp\print.cpp" />
    <ClCompile Include="cpp\symbol.cpp" />
    <ClCompile Include="cpp\xml.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="headers\err.h" />
    <ClInclude Include="headers\find.h" />
    <ClInclude Include="headers\iterate.h" />
    <ClInclude Include="headers\pdb.h" />
    <ClInclude Include="headers\print.h" />
    <ClInclude Include="headers\symbol.h" />
    <ClInclude Include="headers\xml.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>