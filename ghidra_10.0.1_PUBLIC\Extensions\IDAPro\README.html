<html>
  <head>
    <title>IDA Exporters</title>
  </head>
  <body>
    <h1>Exporters from IDA to Ghidra</h1>
    <p>
      This directory contains plugins for IDA which facilitate the transfer of annotations from IDA Pro to Ghidra.
      The exporters export to an XML format, which Ghidra can then import.
      The plugins also facilitate transfer from Ghidra to IDA.
    </p>
    <p>
      The plugin is provided in Python for IDA versions 6 and 7.
      See the README file within each for further instruction.
    </p>
  </body>
</html>
