sdkddkver.h
sal.h
assert.h
conio.h
crtdefs.h
crtdbg.h
crtwrn.h
ctype.h
basetsd.h
WinDef.h
WinNT.h
delayimp.h
direct.h
dos.h
errno.h
excpt.h
fcntl.h
float.h
fpieee.h
io.h
iso646.h
limits.h
locale.h
malloc.h
math.h
mbctype.h
mbstring.h
memory.h
minmax.h
new.h
omp.h
pgobootrun.h
process.h
rtcapi.h
search.h
setjmp.h
setjmpex.h
share.h
signal.h
srv.h
stdarg.h
stddef.h
stdexcpt.h
stdio.h
stdlib.h
string.h
tchar.h
time.h
use_ansi.h
vadefs.h
varargs.h
wchar.h
wctype.h
xlocinfo.h
xmath.h
ymath.h
yvals.h
CommDlg.h
WinUser.h
WinNls.h
internal.h
strsafe.h
align.h
awint.h
crtversion.h
cruntime.h
ctime.h
cvt.h
dbgint.h
ehdata.h
emmintrin.h
errmsg.h
fenv.h
file2.h
fltintrn.h
immintrin.h
internal_securecrt.h
inttypes.h
isa_availability.h
mbdata.h
msdos.h
mtdll.h
nlsdownlevel.h
nlsint.h
nmmintrin.h
oscalls.h
pmmintrin.h
rtcsup.h
rterr.h
sect_attribs.h
setlocal.h
smmintrin.h
stdbool.h
stdint.h
syserr.h
targetver.h
tmmintrin.h
winheap.h
wmmintrin.h
wrapwin.h
xkeycheck.h
xmmintrin.h
xmtx.h
xtgmath.h
xxcctype.h
xxdftype.h
xxfftype.h
xxlftype.h
xxwctype.h
xxxprec.h
shlobj.h
evntprov.h
uiautomation.h
aclapi.h
appcompatapi.h
capi.h
clusapi.h
cryptuiapi.h
cscapi.h
devpropdef.h
dhcpsapi.h
dwmapi.h
ehstorapi.h
functiondiscoveryapi.h
ipexport.h
icmpapi.h
iepmapi.h
imapi.h
ksopmapi.h
locationapi.h
lpmapi.h
mapi.h
mbnapi.h
mfapi.h
mgmtapi.h
mmdeviceapi.h
mprapi.h
msctfmonitorapi.h
ndfapi.h
netioapi.h
npapi.h
nspapi.h
ntdsapi.h
ntmsapi.h
ntsecapi.h
patchapi.h
portabledeviceapi.h
portabledeviceconnectapi.h
propapi.h
psapi.h
rdpencomapi.h
resapi.h
sapi.h
searchapi.h
sensapi.h
sensorsapi.h
setupapi.h
shellapi.h
shlwapi.h
srrestoreptapi.h
svrapi.h
t2embapi.h
tapi.h
uiautomationcoreapi.h
wcnapi.h
wdsclientapi.h
werapi.h
windowssideshowapi.h
wlanapi.h
wpapi.h
wpcapi.h
wscapi.h
wsdapi.h
wspiapi.h

rpcproxy.h

-I/VC/VS12/src
-I/VC/VS12/include
-I/VC/SDK/v7.1A/Include
-D_M_IX86=300
-D_MSC_VER=1200
-D_INTEGRAL_MAX_BITS=64
-DWINVER=0x0900
-D_AMD64_
-D_M_AMD64
-D_M_X64
-D_WIN64
-D_WIN32
-D_USE_ATTRIBUTES_FOR_SAL
-D_CRTBLD
-D_OPENMP_NOFORCE_MANIFEST
-DSTRSAFE_LIB
-DSTRSAFE_LIB_IMPL
-DLPSKBINFO=LPARAM
-D_WCHAR_T_DEFINED
-DCONST=const
-D_CRT_SECURE_NO_WARNINGS
-D_CRT_NONSTDC_NO_DEPRECATE
-D_CRT_NONSTDC_NO_WARNINGS
-D_CRT_OBSOLETE_NO_DEPRECATE
-D_ALLOW_KEYWORD_MACROS
-D_ASSERT_OK
-DSTRSAFE_NO_DEPRECATE
-D__possibly_notnullterminated
-Dtype_info="void *"
-D_ThrowInfo=ThrowInfo
-D__unaligned=""
-v0
-D__inner_checkReturn=""
