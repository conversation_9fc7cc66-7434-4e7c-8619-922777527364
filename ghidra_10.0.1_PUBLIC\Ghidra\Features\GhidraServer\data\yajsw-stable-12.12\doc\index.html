<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <meta content="text/html; charset=ISO-8859-1" http-equiv="content-type">



























































  



  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  

  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <title>YAJSW - Yet Another Java Service Wrapper - Introduction</title>
</head>


<body>





















<div style="text-align: center;">
<div style="text-align: center;">
<h2><a class="mozTocH2" name="mozTocId964398"></a>Welcome
to YAJSW</h2>




























































<span style="font-weight: bold;">Yet Another Java Service
Wrapper<br>




























































</span>
<div style="text-align: left;"><span style="font-weight: bold;"></span>
<ul id="mozToc">




<!--mozToc h2 2 h3 3 h4 4 h5 5 h6 6--><li><a href="#mozTocId964398">Welcome
to YAJSW</a>
    
    
    
    
    <ul>




      <li>
        
        
        
        
        <ul>




          <li><a href="#mozTocId326435">Introduction</a></li>




          <li><a href="#mozTocId869046">Project information</a></li>




          <li><a href="#mozTocId941686">Changes</a></li>




          <li><a href="#mozTocId284533">Comparison
of wrapper frameworks</a></li>




          <li><a href="#mozTocId768274">Requirements
&amp; Supported Platforms</a></li>




          <li><a href="#mozTocId236130">Licensing</a></li>




          <li><a href="#mozTocId945046">Features</a></li>




          <li><a href="#mozTocId527639">Quick
Start</a>
            
            
            
            
            <ul>




              <li><a href="#mozTocId937620">View Screencast</a></li>




              <li><a href="#mozTocId212903">Installation of a java application using genConfig</a></li>




            
            
            
            
            </ul>




          </li>




          <li><a href="#mozTocId502224">Migrating from JSW</a></li>




          <li><a href="#mozTocId210483">Configuration</a></li>




          <li><a href="#mozTocId286492">Configuration
Generator&nbsp;</a></li>




          <li><a href="#mozTocId492960">Java
Application Launching</a></li>




          <li><a href="#mozTocId976628"> Using YAJSW from within a java application</a>
            
            
            
            
            <ul>




              <li><a href="#mozTocId379457">Starting
a native image&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; 























































                </a></li>




              <li><a href="#mozTocId765320">Starting
a java application&nbsp;&nbsp;&nbsp; &nbsp; 























































                </a></li>




              <li><a href="#mozTocId337498">Reading
the output stream of a wrapped application</a></li>




              <li><a href="#mozTocId125186">Writing
the input stream of a wrapped application</a></li>




              <li><a href="#mozTocId150522">Restarting/Stopping
the JVM from within a wrapped application</a></li>




              <li><a href="#mozTocId896633">Getting
a Thread Dump of the wrapped application</a></li>




              <li><a href="#mozTocId444599">Installing
a service























































                </a></li>




              <li><a href="#mozTocId162734">Starting
a service























































                </a></li>




              <li><a href="#mozTocId54821">Stopping
a service























































                </a></li>




              <li><a href="#mozTocId78366">Listening
to state change</a></li>




            
            
            
            
            </ul>




          </li>




          <li><a href="#mozTocId62345">Monitoring java applications</a>
            
            
            
            
            <ul>




              <li><a href="#mozTocId989589">Monitoring Heap</a></li>




              <li><a href="#mozTocId937413">Monitoring Memory</a></li>




              <li><a href="#mozTocId81014">Monitoring Deadlocks</a></li>




            
            
            
            
            </ul>




          </li>




          <li><a href="#mozTocId510864">Timers
and Conditions</a></li>




          <li><a href="#mozTocId986212">JMX
Support</a></li>




          <li><a href="#mozTocId451759">System
Tray Support</a></li>




          <li><a href="#mozTocId134633">Windows services</a></li>




          <li><a href="#mozTocId113692">Unix/Linux Daemon</a></li>




          <li><a href="#mozTocId193131">Mac
OS X Daemon</a></li>




          <li><a href="#mozTocId313747">Scripts</a>
            
            
            
            
            <ul>




              <li><a href="#mozTocId634930">Shell
Scripts</a></li>




              <li><a href="#mozTocId427834">Groovy
Scripts</a></li>




              <li><a href="#mozTocId336090">Sample
Scripts</a></li>




              <li><a href="#mozTocId228913">Condition
Scripts</a></li>




              <li><a href="#mozTocId719802">Testing
a script</a></li>




            
            
            
            
            </ul>




          </li>




          <li><a href="#mozTocId429644">MSCS Cluster Aware Wrapper</a></li>




          <li><a href="#mozTocId245576">Windows Session0 Isolation</a></li>




          <li><a href="#mozTocId691087">OS
Users</a></li>




          <li><a href="#mozTocId625680">Entering passwords</a></li>




          <li><a href="#mozTocId787535">YAJSW lib folder</a></li>




          <li><a href="#mozTocId543401">Temporary files</a></li>




          <li><a href="#mozTocId754583">Wrapper process java options</a></li>




          <li><a href="#mozTocId392911">Wrapping groovy scripts</a></li>




          <li><a href="#mozTocId361662">YAJSW groovified: WrapperBuilder</a></li>




          <li><a href="#mozTocId309423">Building YAJSW</a></li>




          <li><a href="#mozTocId538610">Network Launching of Applications - Experimental</a></li>




          <li><a href="#mozTocId778097">Wrapping JNLP - Experimental</a></li>




          <li><a href="#mozTocId531550">Java Web Start Support - Experimental - Currently only for windows</a></li>




          <li><a href="#mozTocId316423">Services Manager - Experimental - Currently only for windows</a></li>




          <li><a href="#mozTocId188498">Android Services Manager - experimental</a></li>




          <li><a href="#mozTocId790812">Service Update - Experimental - Currently windows only</a></li>




          <li><a href="#mozTocId966">Third
party Libraries</a></li>




        
        
        
        
        </ul>




      </li>




    
    
    
    
    </ul>




  </li>




</ul>
























































<span style="font-weight: bold;"></span><span style="font-weight: bold;"><br>





<br>




























































</span><span style="font-weight: bold;"></span>
<h3><a class="mozTocH3" name="mozTocId326435"></a>Introduction</h3>




























































YAJSW is a java centric implementation of the <a href="http://wrapper.tanukisoftware.org/">java service
wrapper by tanuki</a> (JSW).<br>




























































It aims at being mostly configuration compliant with the original. It
should therefore be easy to switch from JSW to YAJSW.<br>




























































<br>




























































JSW&nbsp;is a wonderful software I have been using for some time in
production. So why yet another framework ?<br>




























































<br>




























































&nbsp;&nbsp; &nbsp;The main reason is that I need a better
integration within my java job scheduling framework (rzomx)<br>




























































&nbsp;&nbsp; &nbsp;As of the next version the license of
JSW will change making it impossible for me to use it in rzomx<br>




























































&nbsp;&nbsp; &nbsp;I may need win64 support, which is
currently not available or not free for JSW.<br>










































<br>










































<h3><a class="mozTocH3" name="mozTocId869046"></a>Project information</h3>










































<a href="http://sourceforge.net/projects/yajsw/">YAJSW project is managed on sourceforge</a> <br>










































<br>


































<a href="http://sourceforge.net/projects/yajsw"><img style="border: 0px solid ; width: 150px; height: 40px;" src="http://sflogo.sourceforge.net/sflogo.php?group_id=224503&amp;type=15" alt="Get Yet Another Java Service Wrapper at SourceForge.net. Fast, secure and Free Open Source software downloads"></a><br>


































<br>










































Please refer to the project page for licensing, user forums, bug tracking, file downloads.<br>










































<br>










































Currently the project does not support mailing lists nor does it support a
source code repository. The source is included&nbsp;in the download file.<br>










































<br>










































<h3><a class="mozTocH3" name="mozTocId941686"></a><a href="YAJSW-Changes.html">Changes</a></h3>




























































<h3><a class="mozTocH3" name="mozTocId284533"></a>Comparison
of wrapper frameworks</h3>




























































<ul>




























































  <li>YAJSW : <a href="http://sourceforge.net/projects/yajsw/">Yet
Another Java Service Wrapper</a></li>




























































  <li>JSW : <a href="http://wrapper.tanukisoftware.org">Java
Service Wrapper</a></li>




























































  <li>ACD : <a href="http://commons.apache.org/daemon/">Apache
Commons Daemon</a></li>




























































  <li>L4J : <a href="http://launch4j.sourceforge.net/">Launch4j</a></li>




























































</ul>




























































Note: If you would like to add further frameworks or in case of errors
in the table please inform me.<br>




























































<br>




























































<table style="text-align: left; width: 1078px; height: 179px;" border="1" cellpadding="2" cellspacing="2">




























































  <tbody>




























































    <tr>




























































      <td style="font-weight: bold;" align="undefined" valign="undefined">Features/Framework</td>




























































      <td style="font-weight: bold;" align="undefined" valign="undefined">YAJSW</td>




























































      <td style="font-weight: bold;" align="undefined" valign="undefined">JSW</td>




























































      <td style="font-weight: bold;" align="undefined" valign="undefined">ACD</td>




























































      <td style="font-weight: bold;" align="undefined" valign="undefined">L4J</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">License</td>




























































      <td align="undefined" valign="undefined">APACHE/LGPL</td>




























































      <td align="undefined" valign="undefined">GPL/Commercial</td>




























































      <td align="undefined" valign="undefined">Apache</td>




























































      <td align="undefined" valign="undefined">BSD/MIT</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Run
as Windows Service</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Run
as UNIX Daemon</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Support
Mac OS X launchd</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Platform
indepemdent installation of services/Daemons</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Java
Embedable</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Wrap
Java Application</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">X</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Wrap
Groovy Script</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined"></td>




























































      <td align="undefined" valign="undefined"></td>




























































      <td align="undefined" valign="undefined"></td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Wrap
native executable</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Wrap
as executable</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">X</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Portable
Configuration</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Capture
and Log console output</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Stop
and Reconnect Wrapper</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Monitor
Application</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Restart
Application</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Single
Instance Enforcement</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">X</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Control
Process Priority</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Control
Process Affinity</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Alert
Emails</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">X
(Commercial)</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Scripting</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Timed
Events and Event commands</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">X
(Commercial)</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Configuration
generator</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Define
Process Name</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">X</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">Automatic
selection of JVM</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">X</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">JMX
Support</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































    </tr>




























































    <tr>




























































      <td align="undefined" valign="undefined">System
Tray + GUI Console</td>




























































      <td align="undefined" valign="undefined">X</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































      <td align="undefined" valign="undefined">&nbsp;</td>




























































    </tr>




























































  <tr>


























































      <td align="undefined" valign="undefined">Windows Cluster Aware</td>


























































      <td align="undefined" valign="undefined">X</td>


























































      <td align="undefined" valign="undefined"></td>


























































      <td align="undefined" valign="undefined"></td>


























































      <td align="undefined" valign="undefined"></td>


























































    </tr>


























































  
  
  
  
  <tr>





















































      <td align="undefined" valign="undefined">Network launcher</td>





















































      <td align="undefined" valign="undefined">X</td>





















































      <td align="undefined" valign="undefined"></td>





















































      <td align="undefined" valign="undefined"></td>





















































      <td align="undefined" valign="undefined"></td>





















































    </tr>





















































    <tr>





















































      <td align="undefined" valign="undefined">Support JNLP configuration</td>





















































      <td align="undefined" valign="undefined">X</td>





















































      <td align="undefined" valign="undefined"></td>





















































      <td align="undefined" valign="undefined"></td>





















































      <td align="undefined" valign="undefined"></td>





















































    </tr>





















































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <tr>




































      <td align="undefined" valign="undefined">Java Webstart launcher</td>




































      <td align="undefined" valign="undefined">X</td>




































      <td align="undefined" valign="undefined"></td>




































      <td align="undefined" valign="undefined"></td>




































      <td align="undefined" valign="undefined"></td>




































    </tr>




































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </tbody>
</table>




























































<h3><a class="mozTocH3" name="mozTocId768274"></a>Requirements
&amp; Supported Platforms</h3>




























































YAJSW requires Java 1.6 or higher.<br>




























































<br>




























































The
current release is stable for <span style="font-weight: bold;">win32
and&nbsp;win64</span> <br>




























































<div style="margin-left: 40px;">Tested on: Windows XP
prof, Windows 2000 server, Windows 2003 32 &amp; 64 bit
server, Windows 2008 32 &amp; 64 bit, Windows 7 32 &amp; 64 bit,
Windows Vista, Windows 8, Windows server 2012, Windows 10, Windows
server 2016</div>




























































<br>




























































The current release is stable for <span style="font-weight: bold;">Linux</span>.<br>




























































<div style="margin-left: 40px;">Tested on Suse 9, Suse
11.1 x86 and amd64, Ubuntu 9.04 32 &amp; 64 bit, Ubuntu 10.4 32 &amp; 64 bit, Ubuntu 12.04, CentOs 6 &amp; 7&nbsp;</div>




























































<br>




























































The current release is stable for <span style="font-weight: bold;">Mac
OS X</span><br>




























































<div style="margin-left: 40px;">Tested on 10.5.5<br>




























































</div>




























































<br>




























































The current release is alpha for <span style="font-weight: bold;">Solaris</span><br>




























































<div style="margin-left: 40px;">Tested on Solaris 10 u6 x86<br>




























































</div>




























































<br>




























































The current release is alpha for <span style="font-weight: bold;">FreeBSD</span><br>




























































<div style="margin-left: 40px;">Tested on FreeBSD 7.1 x86
and amd64</div>




























































<br>

The current release is stable for Raspberry pi<br>

<div style="margin-left: 40px;">Tested on Linux raspberrypi 3.18.7+</div>

<br>

<br>

Goal of YAJSW is to support all platforms as <a href="https://jna.dev.java.net/#building">JNA</a>.<br>




























































It should be easy to build it for other platforms supporting <a href="http://sourceware.org/libffi/">libffi</a><br>




























































<br>




























































If you have tested it, or ported it on other platforms please notify me.<br>




























































<br>




























































NOTE Windows vista, 7, etc:&nbsp;<span style="text-decoration: underline;"></span>the batch files have to be<a href="http://www.vistaclues.com/run-a-batch-file-as-an-administrator/"> run as administrator</a>.<br>











<br>











<a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/4767821">NOTE</a>:
<span style="font-weight: bold;">JBoss 7</span>:&nbsp;runs
only with java version prior to 6_17 or higher than 7_0. Jboss seems to be <a href="https://issues.jboss.org/browse/AS7-4968">aware</a> of this issue.<br>










<br>










<a href="http://sourceforge.net/projects/yajsw/forums/forum/810311/topic/5302435">NOTE</a>: It is recommended to run the wrapper with java 7.<br>









<br>









NOTE: If you are having <a href="http://www.howtogeek.com/68119/how-to-bring-app-icons-back-into-unitys-system-tray/">issues displaying the system tray icon on ubuntu unity</a><br>


<br>


NOTE: as of release 12.02 java 1.6 is required<br>

<br>

NOTE: raspberry pi: after invocation of installDaemon.sh: execute: "sudo update-rc.d

















































&lt;daemon name&gt; defaults", otherwise the service is not started automatically on boot.<br>



















































<h3><a class="mozTocH3" name="mozTocId236130"></a>Licensing</h3>




<br>




Prior to release 12.00: LGPL <br>




<br>




As of release 12.00 the following licenses apply:<br>




<br>




Apache V2.0<br>




<br>




Except for the following modules which are released with the LGPL license:<br>




<br>




- service manager: due to the license of &nbsp;glazed lists which is not compatible with APACHE<br>




- mail script: due to license of javaMail which is not compatible with APACHE<br>




<br>




























































<h3><a class="mozTocH3" name="mozTocId945046"></a>Features</h3>




























































<ul>




























































  <li>Wrap any native executable, java process or groovy script
and run it as windows service or linux daemon</li>




























































  <li>Easy Platform Independent installation of services/daemons</li>




























































  <li>Platform independent configuration</li>





















































  <li>Support for platform specific configuration</li>




























































  <li>Wild card for classpath directories and files</li>




























































  <li>Capture console output, log it, trigger scripts or restart
process on output matching regular expressions</li>




























































  <li>Monitor and automatically restart hanging or crashed
proceses</li>




























































  <li>Trigger process execution and termination &nbsp;at
specific cycles, times or scriptable conditions</li>




























































  <li>Embed the wrapper within java applications</li>




























































  <li>Read from process output or write to process input</li>




























































  <li>Support for RunAs / sudo</li>




























































  <li>Support
for System Tray Icon. Display tray messages from
a groovy script, for example in case of exceptions. Monitoring Console.</li>




























































  <li>Support for generation of configuration file</li>


























































  <li>Windows Cluster aware</li>





















































  <li>Support for remote network launching</li>





















































  <li>Support for jnlp configuration</li>














  <li>Windows Session0 support</li>




























































  <li>License: APACHE</li>




























































</ul>




























































<h3><a class="mozTocH3" name="mozTocId527639"></a>Quick
Start</h3>




















<h4><a class="mozTocH4" name="mozTocId937620"></a>Screencast</h4>



<br>



<div style="margin-left: 40px;">
<ul>



  <li><a href="http://www.youtube.com/watch?v=gX9tGInrIvg">Windows</a></li>



  <li><a href="https://youtu.be/qtwQf7X4g5Y">CentOs</a></li>



</ul>



</div>




































<h4><a class="mozTocH4" name="mozTocId212903"></a>Installation of a java application using genConfig</h4>




























































<div style="text-align: left;">&nbsp; &nbsp;<br>




























































<div style="margin-left: 40px;">Note: In the sequel script
file names end with ".bat". For Linux please use the corresponding
files which end with ".sh"</div>




























































<div style="margin-left: 40px;">
<ul>




























































  <li>&nbsp;<a href="https://sourceforge.net/project/showfiles.php?group_id=224503">download</a>
YAJSW</li>























  <li>Unpack the zip file to a folder &lt;yajsw&gt;.</li>























  <li>Do not remove or copy files from this folder to any another
folder. Do not rename any folders within &lt;yajsw&gt;. For this you
may have to change the manifest in the jar files.</li>




























































  <li>Call <span style="font-style: italic;">java
-version&nbsp;</span>and make sure that your default java
installation is 1.5 or higher</li>




























































  <li>If
this is not the case make sure that java 1.5 or higher is installed and
adapt the environment variable java_exe in bat/setenv.bat &nbsp;or
setenv.sh. NOTE: to display system tray java 1.6 is required. For
thread dump with locks java 1.6 is required.</li>




























































  <li>Start
the application you would like to wrap. For example start Tomcat. Check
the process id of your application. NOTE: we need the <span style="font-weight: bold;">pid of the
application</span> not the batch file which started the
application. You may use bat/runHelloWorld.bat, which starts a java application which prints some numbers on the console.</li>




























































  <li> Goto yajsw/bat and execute&nbsp;<span style="font-style: italic;"></span></li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li><span style="font-style: italic;">genConfig.bat
&lt;pid&gt;</span>&nbsp;</li>




























































    <li>NOTE: on posix systems (Linux) this command requires root
priviliges to access information on other processes</li>




























































    <li>This generates the file yajsw/conf/wrapper.conf which is
the configuration file for wrapping your application</li>




























































    <li>Stop your application</li>




























































    <li style="font-weight: bold;">Open conf/wrapper.conf
with a text editor and set the password for <span style="font-style: italic;">wrapper.app.password</span>
or comment out <span style="font-style: italic;">wrapper.app.account</span>
(#). Also check the value of &nbsp;<span style="font-style: italic;">wrapper.ntservice.name</span></li>




























































    <li>NOTE: you may also create a configuration file with a
text editor. Use the file<span style="font-style: italic;"> conf/wrapper.conf.ini</span> as starting point.</li>




























































    <li>NOTE: Mac OS X, Solaris: <span style="font-style: italic;">wrapper.working.dir</span>
is currently not determined automatically. You may have to edit this
property.</li>




























































    <li>NOTE:
configuration generation is currently implemented only for java
applications. It will be implemented for native image and groovy in a
later release. For native images or groovy scripts you will have to
create the wrapper.conf file with a text editor. </li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>Execute your wrapped application as console application by
calling</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li><span style="font-style: italic;">runConsole.bat</span><span style="font-style: italic;"></span></li>




























































    <li>check that your application (for example tomcat) is
running</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>To Install the application as service call</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li><span style="font-style: italic;">installService.bat</span><span style="font-style: italic;"></span></li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>To start the service:</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li><span style="font-style: italic;">startService.bat</span></li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>To stop the service:</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul style="font-style: italic;">




























































    <li>stopService.bat</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>To uninstall the service:</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li><span style="font-style: italic;">uninstallService.bat<span style="font-weight: bold;"></span></span></li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































</ul>




























































</div>




























































</div>




























































<h3><a class="mozTocH3" name="mozTocId502224"></a>Migrating from JSW</h3>



























































<br>



























































YAJSW configuration and command line interface are very similar to
those of JSW. YAJSW however follows a diffrent architecture and
therefore supports the JSW integration methods differntly. A <a href="Migrating%20from%20JSW.html">migration guide for JSW</a> users has therefore been implemented.<br>




























































<h3><a class="mozTocH3" name="mozTocId210483"></a>Configuration</h3>










































Configuration is mostly identical to <a href="http://wrapper.tanukisoftware.org/doc/english/properties.html">JSW</a>.
<br>










































<br>










































Detailed description of the YAJSWconfiguration properties is found&nbsp; <a style="font-weight: bold;" href="YAJSW%20Configuration%20Parameters.html">here</a>.<br>










































<br>




























































&nbsp;&nbsp; &nbsp;Properties can be defined in:<br>




























































&nbsp;&nbsp; &nbsp;<br>




























































&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; &nbsp;-
wrapper.bat command line attributes<br>




























































&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; -
WrappedProcess.getLocalConfiguration().setProperty()<br>




























































&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp; &nbsp;- java system properties
(example
-Dwrapper.config=conf\wrapper.conf, or
System.setProperty("wrapper.config",
"conf\wrapper.conf") )<br>




























































&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; &nbsp;- in
wrapper.conf file<br>




























































&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp; &nbsp;- as operating system
environment variables
(example for win32: set wrapper.java=c:\java\jdk1.5\bin\java.exe)<br>




























































&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; &nbsp;<br>




























































&nbsp;&nbsp;
&nbsp;Properties are searched in this order. Thus setting
wrapper.java
configuration in system properties will override the OS environment.<br>




























































<br>




























































&nbsp;&nbsp;&nbsp;
NOTE: In case of cascading wrapper applications, there are cases where
system properties of the wrapper should not be used by the wrapped
application, when starting sub processes. This can be avoided by
calling <span style="font-style: italic;">WrappedProcess.setUseSystemProperties(false)</span>;<br>




























































<br>




























































<div style="margin-left: 40px;"><span style="font-weight: bold;">NOTE:
Do not change the configuration or scripts while the wrapper or
application or service or daemon is running. To change the
configuration first stop the
wrapper and the application.<br>






















NOTE: Before changing service/daemon configuration properties: stop and uninstall the service or daemon.</span></div>




























































<br>




























































<h3><a class="mozTocH3" name="mozTocId286492"></a>Configuration
Generator&nbsp;</h3>




























































<br>




























































Creating a wrapper configuration file can be quite a nuisance. The
configuration generator helps you generate the configuration.<br>




























































Given
a process id (pid) the configuration generator examines the command
line of a running process and automatically generates a configuration
file for wrapping the given application.<br>




























































<br>




























































Usage:<br>




























































<div style="margin-left: 40px;"><span style="font-style: italic;">wrapper.bat -g
&lt;pid&gt;&nbsp;</span><span style="font-style: italic;">[-d &lt;default
configuration file&gt;]</span><span style="font-style: italic;"> &lt;output file&gt; </span><br>




























































<br>




























































<ul>




























































  <li>pid: process id of the process to wrap</li>




























































  <li>&lt;output file&gt; : name of the output
configuration file</li>




























































  <li>&lt;default
configuration file&gt; : optional input file for defining
properties
such as logging, restart, scripts etc. which are not available in the
command line of the current process.</li>




























































</ul>




























































NOTE: the configuration generator should not be used for processes
which are already wrapped.<br>




























































NOTE: currently the configuration generator only generates
configurations for java applications.</div>




























































<h3><a class="mozTocH3" name="mozTocId492960"></a>Java
Application Launching</h3>




























































The internal workings of application launching and classloading are
described <a href="application%20loading.html">here</a>.<br>




























































<br>



























































<h3><a class="mozTocH3" name="mozTocId976628"></a><a name="Api_Usage"></a> Using YAJSW from within a java application</h3>




























































YAJSW
may wrap applications through the shell scripts in
&lt;yajsw&gt;/bat.
These functions may also be embeded within a java application.
<h4><a class="mozTocH4" name="mozTocId379457"></a>Starting
a native image&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; <br>




























































</h4>




























































<div style="margin-left: 40px;">
<address>// configuration<br>






















Map configuration = new HashMap();<br>




























































</address>




























































<address>configuration.put("wrapper.image",
"notepad.exe");&nbsp;</address>




























































<address>WrappedProcess w = (WrappedProcess) WrappedProcessFactory.createProcess(configuration);<br>




























































// initialiase the process</address>




























































<address>w.init();<br>




























































// start the process</address>




























































<address>w.start();<br>




























































// wait at most 10 sec or until process termination</address>




























































<address>w.waitFor(10000);<br>




























































// stop the process</address>




























































<address>w.stop();</address>




























































</div>




























































<address><br>




























































</address>




























































<h4><a class="mozTocH4" name="mozTocId765320"></a>Starting
a java application&nbsp;&nbsp;&nbsp; &nbsp; <br>




























































</h4>




























































<div style="margin-left: 40px;">
<address>// global configuration<br>




























































System.setProperty("wrapper.config", &lt;conf file&gt;);</address>




























































<address>System.setProperty("wrapper.java.app.mainclass",
&lt;main class&gt;); // system properties overwrite properties
in conf file.<br>






















Map configuration = new HashMap();<br>






















WrappedProcess w = (WrappedProcess) WrappedProcessFactory.createProcess(configuration);</address>




























































<address>/ process specific configuration<br>




























































w.getLocalConfiguration().setProperty("wrapper.app.parameter.1",
"hello");<br>




























































// initialiase the process</address>




























































<address>w.init();<br>




























































// start the process</address>




























































<address>w.start();</address>




























































<address>w.waitFor(10000);<br>




























































// stop the process</address>




























































<address>w.stop();</address>




























































</div>




























































<address><br>




























































</address>




























































<h4><a class="mozTocH4" name="mozTocId337498"></a>Reading
the output stream of a wrapped application</h4>




























































<div style="margin-left: 40px;">
<address>WrappedProcess w = (WrappedProcess) WrappedProcessFactory.createProcess(new HashMap());<br>




























































// process specific configuration<br>




























































w.getLocalConfiguration().setProperty("wrapper.app.parameter.1",
"hello");<br>




























































// initialiase the process</address>




























































<address>w.init();<br>




























































// start the process</address>




























































<address>w.start();<br>




























































//
a drain warps access to the output and error streams of the wrapped
process. The process does not hang if the output is not consumed.<br>




























































// a drain must be started and stopped<br>




























































// the drain maintains a circular buffer<br>




























































// if &nbsp;since last call to readDrainLine() no new output has
been generated then "null" is returned<br>




























































// if readDrainLine() is not called for a "long time" the buffer may
overflow and you may "miss" some of the output<br>




























































// the drain first consumes the error stream, if no output is available
the output stream is consumed<br>




























































w.startDrain();<br>




























































System.out.println(w.readDrainLine());<br>




























































w.stopDrain();<br>




























































w.waitFor();</address>




























































</div>




























































<address><br>




























































</address>




























































<h4><a class="mozTocH4" name="mozTocId125186"></a>Writing
the input stream of a wrapped application</h4>




























































<div style="margin-left: 40px;">
<address>WrappedProcess w = (WrappedProcess) WrappedProcessFactory.createProcess(new HashMap());<br>




























































...<br>




























































// initialiase the process</address>




























































<address>w.init();<br>




























































// start the process</address>




























































<address>w.start();<br>




























































// get a print stream to the process<br>




























































PrintStream out = (PrintStream)w.getOutputStream();<br>




























































// send some text to System.in of the process<br>




























































out.println("my password");<br>




























































<br>




























































</address>




























































</div>




























































<address><br>




























































</address>




























































<address><br>




























































</address>




























































<h4><a class="mozTocH4" name="mozTocId150522"></a>Invoking the wrapper from within a wrapped application</h4>
<div style="margin-left: 40px;">The static object org.rzo.yajsw.app.WrapperJVMMain.WRAPPER_MANAGER implements functions for invoking methods which are transmitted to the wrapper.<br>
Note: you will need to add wrapperApp.jar to your compile dependency. It is not required in the run depencency.</div>
<br>
<div style="margin-left: 40px;"><span style="font-style: italic; font-weight: bold;">WRAPPER_MANAGER.restart()</span> : request restart of the wrapped application<br>
<span style="font-weight: bold; font-style: italic;">WRAPPER_MANAGER.stop()</span> : request stop of the wrapped application<span style="font-weight: bold;"><span style="font-style: italic;"></span></span><br>
<span style="font-weight: bold; font-style: italic;">WRAPPER_MANAGER.signalStopping(int timeoutHint)</span>
: signal to the wrapper how long (ms) the wrapped application needs to
stop. This will be generally called from within a shutdown hook.<br>
<span style="font-weight: bold; font-style: italic;">WRAPPER_MANAGER.getProperties()</span> : access the yajsw configuration<br>
<span style="font-weight: bold; font-style: italic;">WRAPPER_MANAGER.getPid()</span> : returns the pid of the wrapped application<br>
<span style="font-weight: bold; font-style: italic;">WRAPPER_MANAGER.setShutdownListener(Runnable listener)</span>
: The listener is invoked when the wrapper sends a stop command to the
wrapped application, which is before shutdown hooks are invoked.<br>
<span style="font-weight: bold; font-style: italic;">WRAPPER_MANAGER.threadDump()</span> : prints a thread dump to System.err<br>
<span style="font-weight: bold; font-style: italic;">WRAPPER_MANAGER.reportServiceStartup()</span>
: indicate to the wrapper that the wrapped application is up and
running. See configration property wrapper.ntservice.autoreport.startup</div>




























































<h4><a class="mozTocH4" name="mozTocId896633"></a>Getting
a Thread Dump of the wrapped application</h4>




























































<div style="margin-left: 40px;">
<address>WrappedJavaProcess w = ...<br>




























































<br>




























































w.requestThreadDump();</address>




























































</div>




























































<address><br>




























































</address>




























































<address></address>




























































<h4><a class="mozTocH4" name="mozTocId444599"></a>Installing
a service<br>




























































</h4>




























































<div style="margin-left: 40px;">
<address>System.setPropery("wrapper.config", &lt;conf
file&gt;);<br>




























































WrappedService w = new WrappedService();<br>




























































w.init();&nbsp;&nbsp;&nbsp; // read in configuration<br>




























































w.install(); // start the service<span style="font-weight: bold;"><br>




























































</span></address>




























































</div>




























































<h4><a class="mozTocH4" name="mozTocId162734"></a>Starting
a service<br>




























































</h4>




























































<div style="margin-left: 40px;">
<address>WrappedService w = ...<br>




























































w.init();&nbsp;&nbsp;&nbsp; // read in configuration<br>




























































w.start(); // run the service<span style="font-weight: bold;"><br>




























































</span></address>




























































</div>




























































<br>




























































<h4><a class="mozTocH4" name="mozTocId54821"></a>Stopping
a service<br>




























































</h4>




























































<div style="margin-left: 40px;">
<address>WrappedService w = ...<br>




























































w.init();&nbsp;&nbsp;&nbsp; // read in configuration<br>




























































w.stop(); // stop the service<span style="font-weight: bold;"><br>




























































</span></address>




























































</div>




























































<br>




























































<h4><a class="mozTocH4" name="mozTocId78366"></a>Listening
to state change</h4>




























































<div style="margin-left: 40px;">
<address>WrappedProcess w =...<br>




























































// initialiase the process</address>




























































w.init();<br>




























































w.addStateChangeListener(WrappedProcess.STATE_ABORT, new
StateChangeListener()<br>




























































&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; {<br>




























































&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp; public void stateChange(int
newState, int oldState)<br>




























































&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp; &nbsp;{<br>




























































&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp; Script script =
ScriptFactory.createScript("scripts/sendMail.gv", &nbsp;"some id",
&nbsp;w, &nbsp;new String[{"<EMAIL>", "process could
not be
started"});<br>




























































&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp; script.execute();<br>




























































&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp; &nbsp;});<br>




























































w.start();</div>




























































<br>




























































<h3><a class="mozTocH3" name="mozTocId62345"></a>Monitoring java applications</h3>















<div style="margin-left: 40px;">YAJSW implements features helping you to monitor your java
applications. Currently the following monitors have been impelemented:<br>















<br>















<div style="margin-left: 40px;">
<ul>















  <li>Memory</li>















  <li>Deadlocks</li>















</ul>















</div>















</div>















<h4><a class="mozTocH4" name="mozTocId989589"></a>Monitoring Heap</h4>















<div style="margin-left: 40px;">to enable set:<br>















<div style="margin-left: 40px;"><span style="font-weight: bold; font-style: italic;">wrapper.java.monitor.heap = true</span><br style="font-weight: bold; font-style: italic;">















<span style="font-weight: bold; font-style: italic;">wrapper.java.monitor.heap.threshold.percent = 90</span></div>















<br>















If the current used heap size exceeds 90% of the maximal heap size the following message is printed to System.err:<br>















<div style="margin-left: 40px;">wrapper.java.monitor.heap: HEAP SIZE</div>















<br>















By setting a triffer on this text one can execute actions or scripts.<br>















<br>















Example: send an email if the used heap exceeds 90%:<br>















<br>















<div style="margin-left: 40px;">wrapper.java.monitor.heap.threshold.percent = 20<br>















wrapper.filter.trigger.heap.mail=wrapper.java.monitor.heap: HEAP SIZE<br>















wrapper.filter.script.heap.mail=scripts/sendMail.gv<br>















wrapper.filter.script.heap.mail.args=<EMAIL>, Java low on memory
set java option -Xmx to a higher value or check for memory leaks.<br>















</div>















<br>















<span style="font-style: italic;"><span style="font-weight: bold;"></span></span></div>















<h4><a class="mozTocH4" name="mozTocId937413"></a>Monitoring Memory</h4>















<div style="margin-left: 40px;">to enable set:<br>















<div style="margin-left: 40px;"><span style="font-weight: bold; font-style: italic;">wrapper.java.monitor.gc=MYGC: {0\,number\,integer}\, {1\,number\,integer}\, {2\,number\,integer}</span></div>















<br>















The text "MYGC: {0\,number\,integer}\, {1\,number\,integer}\, {2\,number\,integer}" is a <a href="http://download.oracle.com/javase/1.4.2/docs/api/java/text/MessageFormat.html">java MessageFormat pattern</a> which is formatted with the following arguments: Used Heap,&nbsp;Minor GC Duration,&nbsp;Full GC Duration.<br>















The formatted text is logged to System.err when the garbage collector has been executed.<br>















A GC duration is 0 if it has not been executed.<br>















<br>















You can set your own pattern and write a program which scans the log file to analyze the heap requirements of your applicaiton.<br>















You could also use a trigger and script to write the data to a database, for later display or analysis.(see <a href="http://code.google.com/p/rrd4j/">rdd4j</a>)</div>















<h4><a class="mozTocH4" name="mozTocId81014"></a>Monitoring Deadlocks</h4>















<div style="margin-left: 40px;">to enable set:<span style="font-style: italic; font-weight: bold;"></span>
<div style="margin-left: 40px;"><span style="font-style: italic; font-weight: bold;">wrapper.java.monitor.deadlock = true</span></div>















<span style="font-style: italic; font-weight: bold;"><br>















</span><span style="font-style: italic;"></span>When a deadlock is detected the following text is printed to System.err:<br>















<div style="margin-left: 40px;">wrapper.java.monitor.deadlock: DEADLOCK IN THREADS:</div>















A thread dump of the threads involved is also printed.<br>















<br>















By setting a trigger on this text one can execute actions or scripts.<br>















<br>















Example: Monitor for deadlocks, display a tray icon message and restart the application if a deadlock is detected:<br>















<br>















<div style="margin-left: 40px;">wrapper.java.monitor.deadlock=true<br>















<br>















wrapper.filter.trigger.deadlock.tray=wrapper.java.monitor.deadlock: DEADLOCK IN THREADS:<br>















wrapper.filter.script.deadlock.tray=scripts/trayMessage.gv<br>















wrapper.filter.script.deadlock.tray.args=Deadlock Detected<br>















<br>















wrapper.filter.trigger.deadlock.restart=wrapper.java.monitor.deadlock: DEADLOCK IN THREADS:<br>















wrapper.filter.action.deadlock.restart=RESTART<br>















</div>















<br>















</div>















<h3><a class="mozTocH3" name="mozTocId510864"></a>Timers
and Conditions</h3>




























































<div style="margin-left: 40px;">A <span style="font-weight: bold;">timer</span>
allows one to automatically start/stop/restart the application at
specific times or intervals. If a timer is defined the wrapper service
runs as long as the timer is active. The wrapper uses the <a href="http://www.opensymphony.com/quartz/">Quartz</a> framework to start/stop the application. <br>






















<br>






















<div style="margin-left: 40px;">



















Example:<br>










































wrapper.timer.RESTART.cron=* * 0/12 * * ?<br>










































      <br>










































Starts the process immediatly and restart the process daily at 00:00
and 12:00 h.</div>






















<br>






















A <span style="font-weight: bold;">condition</span> is agroovy&nbsp;
script which is executed at fixed time intervals or only once. The
script controls the start/stop of the application. YAJSW includes the
following sample condition scripts:<br>






















<br>






















<div style="margin-left: 40px;">
<ul>






















  <li><span style="font-weight: bold;">timeCondition.gv</span>: This script will make sure that the process is running between the specified times:</li>






















</ul>






















<div style="margin-left: 40px;">Configuration Example:<br>






















<br>






















wrapper.condition.script=scripts/timeCondition.gv<br>






















wrapper.condition.script.args=23,1:50<br>






















<br>






















will start the application at 23:00 and will stop it at 1:50 the next day.<br>






















</div>






















<ul>






















  <li>commandCondition.gv: This script checks for the presence of a
command file. If the file is present the content of the file is read
and the command in the file is executed.</li>






















</ul>






















<div style="margin-left: 40px;">Configuration Example:<br>






















<br>






















&nbsp;wrapper.condition.script=scripts/commandCondition.gv <br>






















&nbsp;wrapper.condition.script.args=cmd.txt <br>






















&nbsp;wrapper.condition.cycle=1 <br>






















<br>






















</div>






















</div>






















<br>






















When using Timers or Conditions the functionality of the <span style="font-style: italic;">start()</span>
and <span style="font-style: italic;">stop()</span>
methods may be confusing.<br>




























































<br>




























































The first call to <span style="font-style: italic;">start()
</span>&nbsp;will trigger the timer or condition. <br>




























































If a condition is defined the first call to <span style="font-style: italic;">start()</span> will not
start the process. Subsequent calls to <span style="font-style: italic;">start(</span>) will start
the application.<br>




























































If a timer is defined the first call to <span style="font-style: italic;">start()</span>
&nbsp;will start the application only if the property <span style="font-style: italic;">wrapper.timer.cron.STAR</span>T
or&nbsp;<span style="font-style: italic;">wrapper.timer.simple.&lt;cmd&gt;.FIRST</span>
&nbsp;is not defined.<br>




























































<br>




























































A call to <span style="font-style: italic;">stop() </span>will
stop the application.<br>




























































To cancel the timer use the method<span style="font-style: italic;">
stopTimer()</span><br>




























































To cancel the condition use the method <span style="font-style: italic;">stopCondition()</span></div>




























































<h3><a class="mozTocH3" name="mozTocId986212"></a>JMX
Support</h3>




























































<div style="margin-left: 40px;">JMX Support is available
for the wrapper and for the wrapped application.<br>




























































<br>




























































To activate JMX support on the wrapper:<br>




























































<ul>







































  <li>add the property <span style="font-style: italic;">wrapper.jmx=true</span>
to the configuration file.</li>







































  <li>if port 1099 is not free set &nbsp;<span style="font-style: italic;">wrapper.jmx.rmi.port=&lt;a free port&gt;</span></li>




























































</ul>






















To activate authentication the credentials may be defined in the configuration:<br>






















<ul>






















  <li style="font-style: italic;">wrapper.jmx.rmi.user</li>






















  <li><span style="font-style: italic;">wrapper.jmx.rmi.password</span><br>






















  </li>






















</ul>




























































To activate JMX support for the wrapped application:<br>




























































<ul>







  <li>addt the following properties to the configuration file:</li>







  
  
  
  
  
  
  
  <ul>







    <li>wrapper.java.jmx=true</li>







    <li>
wrapper.java.additional.1=-Dcom.sun.management.jmxremote</li>







    <li>
wrapper.java.additional.jmx.1=-Dcom.sun.management.jmxremote.port=&lt;tcp/ip port&gt;</li>







    <li>
wrapper.java.additional.jmx.2=-Dcom.sun.management.jmxremote.authenticate=false</li>







    <li>
wrapper.java.additional.jmx.3=-Dcom.sun.management.jmxremote.ssl=false<br>







    </li>







  
  
  
  
  
  
  
  </ul>




























































</ul>




























































The name of the YAJSW&nbsp; MBeans are:<br>




























































<ul>




























































  <li>Wrapper:name=&lt;cosole.title&gt; or
&lt;service.name&gt; or "?"</li>




























































</ul>




























































</div>




























































<span style="font-weight: bold;"></span>
<h3><a class="mozTocH3" name="mozTocId451759"></a>System
Tray Support</h3>




























































<div style="margin-left: 40px;">YAJSW offers support for <a href="http://en.wikipedia.org/wiki/System_tray">system tray</a>.
That is for displaying a status icon and menue on the desktop.<br>




























































The <a href="http://java.sun.com/javase/6/docs/api/java/awt/SystemTray.html">java
system tray</a>
is available only for Sun&nbsp; JDK 1.6 or higher. Since YAJSW requires JDK 1.5
or higher the program will detect if &nbsp;system tray is
supported.<br>




























































<br>




























































System tray is activated by setting the property <span style="font-style: italic;">wrapper.tray = true</span><br>
















































The System Tray Icon is started in a separate process which communicates with the wrapper using netty asynchronous hessian.<br>














The port for communication can be broadcasted&nbsp; using multicast
discovery. For systems where multicast is not enabled, the port may be
defined in the configuration.<br>












Note: if you have multiple wrappers running you will need to make sure
that they use multicast discovery or that you set for each wrapper a
different tray port.<br>
















































The System Tray process is automatically started if the application is started as console.<br>














<br>






















When running the wrapper as a service the tray icon has to be started
separately. One may for example, on windows, create a link to start the
try icon from the startup folder.<br>
















































It can be started automatically on user logon by defining a link to the
systemTrayIconW.bat from the autostart folder on windows or the
according folder in linux.<br>














<br>






















The process can be started by calling "<span style="font-style: italic;">java -jar wrapper.jar <span style="font-weight: bold;">-y</span> &lt;conf file</span>&gt;".<br>
















































The batch script <span style="font-style: italic;">systemTrayIcon.bat</span> starts the process for the default configuration file.<br>














The batch script <span style="font-style: italic;">systemTrayIconW.bat</span> is similar, but will not display a console.<br>
















































<br>
















































Per configuration file only a single tray icon process can be started. If a tray icon process is
started while another one is already running, the process will abort.<br>




























































<br>




























































You may define your own icon to be displayed by setting the property <span style="font-style: italic;">wrapper.tray.icon=&lt;full
path to image file&gt;</span>.<br>




























































If not defined, a default icon is used.<br>














<br>




























































File formats supported by java such as jpg, npg, gif can be used.<br>














<br>




























































The
icon size is automatically scaled to fit the size required by the OS.
<br>














The state of the application is displayed by a colored circle on the
icon.<br>




























































<br>




























































<div style="margin-left: 40px;">RED
&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp; == IDLE<br>




























































GREEN &nbsp;&nbsp;&nbsp; &nbsp; &nbsp;== RUNNING<br>




























































</div>




























































<div style="margin-left: 40px;">ORANGE
&nbsp;&nbsp;&nbsp;== other (starting, restarting, stopping)<br>
















































BLACK &nbsp; &nbsp; &nbsp; == OFFLINE (no communication with the wrapper)<br>














BLUE &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;== WAITING (if <span style="font-style: italic;">wrapper.ntservice.autoreport.waitready == true&nbsp;</span>and <span style="font-style: italic;">WrapperJVMMain.WRAPPER_MANAGER.reportServiceStartup()</span> has not been called)













































</div>




























































<br>














The state color can be set from within a groovy script by calling: <br>














<br>














<div style="margin-left: 40px;">process.getTrayIcon().setUserColor(Color)</div>














<br>














See scripts/trayColor.gv for an example.<br>














<br>














The
system tray offers menues for starting stopping and restarting the
application. These however may not stop the wrapper (depending on the configuration <span style="font-style: italic;">wrapper.control</span>). <br>














The exit wrapper menue will
stop the wrapper. The exit menue will close the Tray Icon.<br>
















































<br>














The property <span style="font-style: italic;">wrapper.tray.commands</span> controls which menue points are displayed.<br>














<br>




























































The console menue will open a window displaying the
console output of the application. When the window is closed the
application continues to run.<br>




























































<br>




























































Using groovy scripts you may display notification messages:<br>




























































<div style="margin-left: 40px;">
<ul>




























































  <li>Access the system tray icon:</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li><span style="font-style: italic;">process.getTrayIcon()</span></li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>The following methods are available, where caption is the
header of the message and message is the content:</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li style="font-style: italic;">TrayIcon.error(caption,
message)</li>




























































    <li style="font-style: italic;">TrayIcon.info(caption,
message)</li>




























































    <li style="font-style: italic;">TrayIcon.warning(caption,
message)</li>




























































    <li style="font-style: italic;">TrayIcon.message(caption,
message)</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































</ul>




























































</div>




























































<br>




























































A sample script is provided for displaying an exception in case an
exeption is logged on the console.<br>












<br>












One may also set the look and feel of the tray icon menue and console window.<br>




























































<br>




























































NOTE: Linux, Solaris:<br>




























































Currently <span style="font-style: italic;">wrapper.ntservice.interactive</span>
is not automatically&nbsp; handled by YAJSW. You will have to make
sure that DISPLAY is exported and xhost + is called.<br>




























































<br>




























































NOTE: Mac OS X, FreeBSD:<br>




























































System tray is currently not supported.<br>




























































</div>

































































<h3><a class="mozTocH3" name="mozTocId134633"></a>Windows services</h3>






















<div style="margin-left: 40px;">When starting a windows service the
service is displayed as running as soon as the wrapper is up. The
wrapper process will then control and monitor the application.<br>






















<br>






















Stopping a service, depending on the<span style="font-style: italic;"> </span>application cleanup time, the <span style="font-style: italic;">wrapper.control</span>
property and the defined script properties, may require extra time.
After 2 minutes, the SCM snap-in timeout, a windows 1503 error message
is displayed. This message does not mean that the service is forcibly
killed. The service will continue to with the cleanup. Timeouts are
controlled by the wrapper using the according configuration properties.</div>






















<h3><a class="mozTocH3" name="mozTocId113692"></a><span style="font-weight: bold;">Unix/Linux Daemon</span></h3>




























































<div style="margin-left: 40px;">YAJSW tries to support
users with little or no Unix/Linux knowledge by offering similar
functions as for windows services.<br>




























































YAJSW offers the following&nbsp; functions. These functions are
also accessible through the API of <span style="font-style: italic;">WrappedService</span><br>




























































<div style="margin-left: 40px;">
<ul>




























































  <li>-i : install a daemon script</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li><span style="font-style: italic;">wrapper.daemon.template</span>
is a <a href="http://velocity.apache.org/">velocity</a>
script used to generate the daemon script.&nbsp;</li>




























































    <li>The script is placed in the directory &lt;<span style="font-style: italic;">wrapper.daemon.dir&gt;</span></li>




























































    <li>The daemon script is named&nbsp;<span style="font-style: italic;">&lt;wrapper.ntservice.name&gt;</span></li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>-r : remove the daemon script</li>




























































  <li>-t : start the daemon</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>starting the daemon creates the files <span style="font-style: italic;">&lt;wrapper.pidfile&gt;</span>
and <span style="font-style: italic;">&lt;wrapper.java.pidfile&gt;</span>.
These are used to stop the daemon or to check the status of the daemon.</li>




























































    <li>per default these files are named <span style="font-style: italic;">wrapper.&lt;wrapper.ntservice.name&gt;.pid&nbsp;</span><span style="font-style: italic;">wrapper.java.&lt;wrapper.ntservice.name&gt;.pid</span>
and are placed per default in the directory <span style="font-style: italic;">&lt;wrapper.daemon.pid.dir&gt;</span></li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>-p : stop the daemon</li>




























































  <li>-q : query the state of the daemon</li>




























































  <li>-qs : silent query of the daemon state</li>




























































</ul>




























































</div>




























































NOTE:<br>




























































Currently <span style="font-style: italic;">wrapper.ntservices.dependencies
</span>is not supported. It will be supported in future release.<br>







































See Configuration documentation for further properties.<br>












If you are having issues running your appliction and you are sure that your configuration is correct, try setting the property <span style="font-style: italic;">wrapper.fork_hack = true</span> </div>




























































<h3><a class="mozTocH3" name="mozTocId193131"></a>Mac
OS X Daemon</h3>




























































<div style="margin-left: 40px;">YAJSW tries to support
users with little or no Mac OS X&nbsp; knowledge by offering
similar functions as for windows services.<br>




























































For this the <a href="http://en.wikipedia.org/wiki/Launchd">launchd</a>
utility is used.<br>




























































YAJSW offers the following&nbsp; functions. These functions are
also accessible through the API of <span style="font-style: italic;">WrappedService<br>




























































<br>




























































</span>
<div style="margin-left: 40px;">
<div style="margin-left: 40px;">
<ul>




























































  <li>-i : install a daemon&nbsp;</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li><span style="font-style: italic;">wrapper.plist.template</span>
is used to generate the <a href="http://developer.apple.com/DOCUMENTATION/DARWIN/Reference/ManPages/man5/launchd.plist.5.html">plist
file</a>.&nbsp;</li>




























































    <li>The plist file is placed in the directory &lt;<span style="font-style: italic;">wrapper.launchd.dir&gt;</span></li>




























































    <li>The plist file name is:&nbsp; <span style="font-style: italic;">wrapper.&lt;wrapper.ntservice.name&gt;.plist</span></li>




























































    <li>The label key is: <span style="font-style: italic;">wrapper.&lt;wrapper.ntservice.name&gt;</span></li>




























































    <li><span style="font-style: italic;"><a href="http://developer.apple.com/DOCUMENTATION/DARWIN/Reference/ManPages/man1/launchctl.1.html">launchctl</a>
load &lt;plist file&gt; </span>is called to install the
file<br>




























































    </li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>-r : remove the daemon</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li><span style="font-style: italic;">launchctl
unload &lt;plist file&gt; </span>is called</li>




























































    <li>the plist file is removed</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>-t : start the daemon</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li><span style="font-style: italic;">launchctl
start &lt;plist label&gt; </span>is called</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>-p : stop the daemon</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li><span style="font-style: italic;">launchctl
stop &lt;plist label&gt; </span>is called</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>-q : query the state of the daemon</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li><span style="font-style: italic;">launchctl
list&nbsp;</span>is called and the result is analysed to
check if the daemon is installed and if it is running</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>-qs : silent query of the daemon state</li>




























































</ul>




























































</div>




























































</div>




























































</div>




























































<div style="margin-left: 40px;">NOTE: <br>




























































currently users are not properly
handeled by YAJSW. This will be implemented in future release. For now
you will have to execute the scripts within a user which has the
required rights.<br>




























































<br>




























































NOTE:<br>




























































Currently <span style="font-style: italic;">wrapper.ntservices.dependencies
</span>is not supported. It will be supported in future release.<br>












<br>












If you are having issues running your appliction and you are sure that your configuration is correct, try setting the property <span style="font-style: italic;">wrapper.fork_hack = true</span> </div>




























































<h3><a class="mozTocH3" name="mozTocId313747"></a>Scripts</h3>




























































<div style="margin-left: 40px;">YAJSW supports shell
(.bat, .sh) and groovy (.gv, .groovy) scripts.<br>



























































Scripts
may either be wrapped or they may be invoked from within the wrapper
triggered by state changes or by matching expressions to the console
output or as conditions which may control job executions.<br>



























































Here we describe scripts which are executed on state change or triggered by console output or as conditions.<br>



























































Wrapping groovy scripts is described in the <a href="#wrapping_groovy">sequel</a>.<br>



























































A builder for easy interface to YAJSW from within groovy scripts is also described <a href="#groovified">later</a>.<br>




























































Per default the scripts are placed in the ./scripts folder.<br>




























































<h4><a class="mozTocH4" name="mozTocId634930"></a>Shell
Scripts</h4>




























































Shell scripts are called with the following arguments:<br>




























































<div style="margin-left: 40px;">
<ul>




























































  <li>id&nbsp;</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>&lt;n&gt; from
wrapper.filter.script.&lt;n&gt;</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>state</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>the current state of WrappedProcess, defined in
Constants.STATE_*</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>count</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>the number of failed invocations</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>pid</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>the pid of the process. Negative if the process is not
running</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>exit code</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>exit code of the process. Negative if the process has not
exited</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>console line</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>the output line of the console, in case the script was
triggered by a filter or a regular expression</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>args</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>list of arguments are defined in the .args property</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































</ul>




























































</div>




























































<h4><a class="mozTocH4" name="mozTocId427834"></a>Groovy
Scripts</h4>




























































Groovy scripts offer much more flexibility and enable calls to the
WrappedProcess. <br>




























































Groovy
scripts are compiled and executed within the JVM of the wrapper. They
are thus highly performat and maintain their bindings inbetween calls.<br>




























































Grooy scripts are executed with the following default bindings:<br>




























































<div style="margin-left: 40px;">
<ul>




























































  <li>args</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>arguments defined in the .args property</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>callCount</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>counter of script invocation, starting with 0.</li>




























































    <li>Note:
When the script is first called, no user defined variables are bound in
the script. callCount can be used to define the required variables</li>




























































    <li>Example: if (callCount == 0) &nbsp;myVar = 0</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>id&nbsp;</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>&lt;n&gt; from
wrapper.filter.script.&lt;n&gt;</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>state</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>the current state of WrappedProcess, defined in
Constants.STATE_*</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>count</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>the number of failed invocations</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>pid</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>the pid of the process. Negative if the process is not
running</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>exitCode</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>exit code of the process. Negative if the process has not
exited</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>line</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>the output line of the console, in case the script was
triggered by a filter or a regular expression</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>process</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>WrappedProcess</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>context</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>a static map, allowing to share data between scripts</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































</ul>




























































</div>




























































<h4><a class="mozTocH4" name="mozTocId336090"></a>Sample
Scripts</h4>




























































The following sample groovy scripts have been implemented. Refer to the
script file for documentation on how to use it. Refer to the scripts folder for further scripts.<br>




























































<div style="margin-left: 40px;">
<ul>




























































  <li>sendMail.gv</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>send an email</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>snmpTrap.gv</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>send an snmp trap</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>trayMessage.gv</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>display a message on the system tray.</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>fileCondition.gv</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>make sure the process is running depending on existance
of an achor&nbsp; file</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>timeCondition.gv</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>make sure the process is running in a time interval</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































  <li>commandCondition.gv</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>




























































    <li>implements the functionality for wrapper.commandfile
property.</li>




























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>




























































</ul>




























































</div>




























































<br>




























































Example: Send an email &nbsp;and show a system tray message if an
exception is logged:<br>




























































<br>




























































<div style="margin-left: 40px;">add the following
properties to wrapper.conf file:<br>




























































<br>




























































wrapper.filter.trigger.exception.mail=Exception<br>




























































wrapper.filter.script.exception.mail=scripts/sendMail.gv<br>




























































wrapper.filter.script.exception.mail.args=<EMAIL>,
YAJSW exception, exception found in console output please check the log
file<br>




























































<br>




























































wrapper.filter.trigger.exception.tray=Exception<br>




























































wrapper.filter.script.exception.tray=scripts/trayMessage.gv<br>




























































wrapper.filter.script.exception.tray.args=Exception<br>




























































</div>




























































<br>




























































<h4><a class="mozTocH4" name="mozTocId228913"></a>Condition
Scripts</h4>




























































Condition
scripts are scripts executed cyclically. If a condition is defined the
call to wrapper.bat -c or wrapper.bat -t will trigger the condition
script but will not start the process. The process is started and
stopped within the script by calling process.start() or process.stop()<br>




























































<h4><a class="mozTocH4" name="mozTocId719802"></a>Testing
a script</h4>




























































In order to test a script and its configuration without running an
application and waiting for it to trigger:<br>




























































<br>




























































<div style="margin-left: 40px;"><span style="font-style: italic;">runScript.bat
&lt;configuration file&gt; &lt;script&gt;
[&lt;count&gt;]</span><br>




























































</div>




























































<br>




























































This
will search for the script and will determine its arguments in the
configuration. It will then create&nbsp;a WrappedProcess, but will
not
start it, and will execute the script &lt;count&gt; times.
Default
count is 1.<br>




























































Example:<br>




























































<br>




























































<div style="margin-left: 40px;">runScript.bat
conf/wrapper.conf scripts/sendMail.gv<br>




























































</div>




























































<br>




























































</div>




























































<h3><a class="mozTocH3" name="mozTocId429644"></a>MSCS Cluster Aware Wrapper</h3>


























































<div style="margin-left: 40px;">YAJSW may be configured to be <a href="http://en.wikipedia.org/wiki/MSCS">MSCS</a> cluster aware.<br>


























































<br>


























































The configuration property <span style="font-style: italic;">wrapper.windows.cluster.script</span>
sets a script which is called on first call to WrappedService.start()
and when the active node of the cluster changes. Within the script one
may define actions which are executed when the active node changes. The
sample script <span style="font-style: italic;">cluster.gv</span> enables the application only on the active node.<br>


























































<br>


























































NOTE: setting this property on none clustered nodes will crash the JVM.</div>



























































<h3><a class="mozTocH3" name="mozTocId245576"></a>Windows Session0 Isolation</h3>
















<div style="margin-left: 40px;">As of Windows Vista<a href="http://msdn.microsoft.com/en-us/library/bb756986.aspx"> it is, per default, not possible to run interactive services</a>, that is services which open a user interface window or a system tray icon.<br>
















If you want to run your service without change you will need a logged on user. You can:<br>
















<ul>
















  <li>set your computer to automatically log-on a user on boot using a tool such as <a href="http://technet.microsoft.com/en-us/sysinternals/bb963905.aspx">Sysinternals Autologon</a></li>
















  <li>set in the configuration file: <span style="font-weight: bold; font-style: italic;">wrapper.ntservice.logon_active_session = true</span>, YAJSW will wait for the user to log on and will logon the application to that user</li>
















  <li>and :<span style="font-style: italic; font-weight: bold;">wrapper.ntservice.desktop=</span><span style="font-style: italic; font-weight: bold;">WinSta0\\</span><em style="font-style: italic;"><span style="font-weight: bold;">Default</span></em><em>, &nbsp;</em><em><span style="font-style: italic;"></span></em>YAJSW will "associate" the application with the default desktop<em></em></li>
















  <li>and: <span style="font-weight: bold; font-style: italic;">wrapper.ntservice.dependency.1=LanmanServer</span>, the service will start after network is available.</li>
















  <li>and: <span style="font-weight: bold; font-style: italic;">wrapper.startup.timeout=300</span>, time for the user to log-on and for the application to startup.</li>
















</ul>















NOTE: using this feature can expose your computer to the <a href="http://en.wikipedia.org/wiki/Shatter_attack">shatter attack</a> security risk.
</div>
















<h3><a class="mozTocH3" name="mozTocId691087"></a>OS
Users</h3>




























































<div style="margin-left: 40px;">YAJSW consists of 2
processes: The wrapper (controller) and the wrapped application.<br>




























































Whereas
the controller requires rights to open a server socket and write to log
files, this may not be appropriate for the wrapped application.<br>




























































<br>




























































The controller should therefore be started as a privileged user,
generally root on Linux or administrator or system on Windows.<br>




























































<br>




























































The application can be started on a different user. The user is defined
by the property <span style="font-style: italic;">wrapper.app.account</span>
the according password, if required, is defined by <span style="font-style: italic;">wrapper.app.password</span>
YAJSW checks if the current user of the controller
is the application user. If not, the user is changed.<br>




























































<br>




























































NOTE Windows:<br>


















On MS-windows, per default, if not configured otherwise services are started under the SYSTEM account.<br>




























































On windows we are using the function <span style="font-style: italic;">CreateProcessAsUserW</span>
. This requires the <a href="http://www.microsoft.com/technet/prodtechnol/windowsserver2003/library/iis/08bc7712-548c-4308-a49c-d551a4b5e245.mspx">SE_ASSIGNPRIMARYTOKEN_NAME
and SE_INCREASE_QUOTA_NAME user rights</a>.<br>




























































We are not using <span style="font-style: italic;">CreateProcessWithLogonW</span>
because the command line length is limited to 1024 chars, which may be
too short for java applications.<br>




























































The password is defined by the property <span style="font-style: italic;">wrapper.ntservice.password</span>.<br>




























































If
the password is defined in the configuration file then it is not
visible in the command line of the application nor in the windows
services GUI .<br>




























































<br>




























































NOTE Linux:<br>




























































On Linux we use the functions <a href="http://www.gnu.org/software/libc/manual/html_node/Setting-User-ID.html#Setting-User-ID"><span style="font-style: italic;">setreuid</span> &nbsp;and setregid</a>.
This is available only on systems with the <code>_POSIX_SAVED_IDS</code>
feature.<br>




























































No password is required for changing the user.<br>






















One may define the user and group:  <span style="font-style: italic;">wrapper.app.account=&lt;group&gt;\\&lt;user&gt;</span><br>




























































<br>




























































NOTE JSW:<br>




























































Conform with JSW<span style="font-style: italic;">
wrapper.ntservice.account </span>&nbsp;is
used only for windows services and sets the user of the controller when
it runs as a service. Per default this is the System user. <br>




























































</div>




























































<h3><a class="mozTocH3" name="mozTocId625680"></a>Entering passwords</h3>













































<div style="margin-left: 40px;">User account names and passwords are
defined in the wrapper configuration. However for security reasons one
may require that the password be entered by the user who is
installing/starting the application. This can be achieved with groovy
scripting in the configuration file. YAJSW offers 2 utilities to get
user input. You may&nbsp; implement your own groovy scripts for this or
use the available utilities:<br>













































<br>













































<span style="font-style: italic;">wrapper.ntservice.password = ${<span style="font-weight: bold;">util.inquireCLI</span>('please enter password for winods service user')}</span><br>













































<span style="font-style: italic;">wrapper.app.password = ${<span style="font-weight: bold;">util.inquireTryIcon</span>('please enter password for winods service user')}</span><br>













































<br>













































<span style="font-weight: bold; font-style: italic;">util.inquireCLI(String)</span> prints the given message to the wrapper process console and waits for the user input.<br>













































<br>













































<span style="font-style: italic;"><span style="font-weight: bold;">util.inquireTryIcon</span></span><span style="font-weight: bold; font-style: italic;">(String)</span> displays the given message on the system tray icon and waits for the user input.The user will have to click the <span style="font-weight: bold;">respond</span> button in the tray icon menue and enter the required data.<br>












































<br>












































In general one will use CLI for the service password and tray icon for
getting the application user password if the application is supposed to
run as a service. When the application is started as a service there is
no CLI console to enter the data.<br>













































<br>













































NOTE: groovy scripting and the implemented utilities can be used not
only for passwords but for all other configuration properties.<br>













































&nbsp;</div>













































<h4>Keystore properties - Experimental</h4>


<div style="margin-left: 40px;">There are cases in which manual entry
of passwords is not feasible. In such cases new keystore feature of
yajsw can be used. Note however that this is not a 100% secure
solution. It is however much better than having passwords in clear text
in the configuration files.<br>


<br>


Secret data is refered in the configuration as follows:<br>


<br>


<div style="margin-left: 40px;">&lt;configuration.property&gt; =<span style="font-weight: bold;"> ${keystore</span> &lt;key&gt;<span style="font-weight: bold;">}</span></div>


<br>


Example:<br>


<div style="margin-left: 40px;">wrapper.ntservice.password = ${keystore servicePwd}<span style="font-style: italic;"> </span></div>


<br>


When the property is evaluated yajsw will lookup the key in the keystore and return its value.<br>


<br>


Keys and values should be entered into the keystore before the wrapper is run.<br>


Data is added to the keystore by invoking:<br>


<br>


<div style="margin-left: 40px;">&lt;bat&gt;/keystore &lt;key&gt; &lt;value&gt;</div>


<br>


Example:<br>


<div style="margin-left: 40px;">keystore servicePwd myverysecretyoushouldneverknow</div>


<br>


On first invocation of the above command yajsw will create the file <span style="font-weight: bold;">keystore</span> and add the key and value.<br>


<br>


When the wrapped application is started it renames the keystore file to <span style="font-weight: bold;">keystore.xxx</span>
indicating that the file is in use and that no further data can be
added. It restricts the access rights to the file so that only the user
of the service can access the file and keeps the file locked as long as
the service is running.<br>


<br>


In order to be able to access the secret data a potential attacker will have to:<br>


<ol>


  <li>stop the service</li>


  <li>have the user credentials of the service or of an admin</li>


  <li>analyze the source to find the password of the keystore</li>


</ol>


Note: this functionality requires java 1.7. Yajsw however requires java
1.6. For this reason this function is included in a separate jar file
in the extended/keystore folder. Since it is integrated into the groovy
property interpolator extended/groovy jar files are also required.<br>


<br>


Note: the following usage is not allowed:<br>


<br>


<div style="margin-left: 40px;">prop1 = ${keystore abc}<br>


prop2 = ${prop1}</div>


<br>


In this case prop2 will be set to ?unknown?.<br>


<br>


Since this is a new function: ideas and comments are welcome.<br>


</div>


<h3><a class="mozTocH3" name="mozTocId787535"></a><a name="libFolder"></a>YAJSW lib folder</h3>



























































<div style="margin-left: 40px;">If
you include YAJSW with your own distribution you may not require all
features. Thus you may reduce the size of the distribution by including
only libraries required for your functions.<br>



























































To help with this the third party libraries have been grouped into the following folders:<br>



























































<ul>



























































  <li>core</li>



























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>



























































    <li>The minimal set of libraries required.</li>



























































    <li>If a core library is missing an error is logged.</li>



























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>



























































  <li>extensions</li>



























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>



























































    <li>Libraries required for extended functionalities</li>



























































    <li>If an extension library is missing, no error is logged. However an exception may occur if the extension is missing</li>



























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>



























































  <li>groovy</li>



























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <ul>



























































    <li>Libraries required within groovy scripts. These are loaded when a groovy script is instatiated.</li>



























































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </ul>



























































</ul>



























































A description of the libraries and corresponding functions is found in the ReadMe.txt file in the according folder.<br>



























































<br>



























































</div>



























































<h3><a class="mozTocH3" name="mozTocId543401"></a>Temporary files</h3>













<div style="margin-left: 40px;">With each run YAJSW creates files in a temporary folder. The following files are created:<br>













<div style="margin-left: 40px;">
<div style="margin-left: 40px;">
<ul>













  <li>in_&lt;n&gt;</li>













  <li>out_&lt;n&gt;</li>













  <li>err_&lt;n&gt;</li>













  <li>jna&lt;n&gt;</li>













</ul>













</div>













</div>













These files are&nbsp;<a href="http://sourceforge.net/support/tracker.php?aid=3400173">removed automatically</a> whenever possible. You may define the location of the temporary folder as follows:<br>













<div style="margin-left: 40px;">
<ul>













  <li>Set jna.tmpdir in setenv.sh to set the tmp folder for jna&lt;n&gt; files</li>











  <li>Set wrapper.tmp.path in the configuration file for the other files.</li>













</ul>













</div>













<span style="font-weight: bold;">Note</span>: you need to set the full path to the temp folder.</div>













<h3><a class="mozTocH3" name="mozTocId754583"></a>Wrapper process java options</h3>



























































<div style="margin-left: 40px;">The java options for the wrapper are set in the shell script setenv.bat.<br>



























































The java options for the service are set in the configuration file with the properties <span style="font-style: italic;">wrapper.ntservice.additional.&lt;n&gt;.<br>



























































<br>



























































<span style="font-style: italic;"></span></span>The
memory requirements of the wrapper process are relatively low and are
constant. They may vary in case groovy scripts are used and they depend
on the operating system.<br>



























































On windows the following settings are recommended:<br>



























































<br>



























































Setting in the configuration file:<br>



























































<div style="margin-left: 40px;"><span style="font-style: italic;">wrapper.ntservice.additional.1 = -Xmx5m</span><br>



























































<span style="font-style: italic;"></span><span style="font-style: italic;">wrapper.ntservice.additional.2 = -server</span><br>



























































</div>



























































<span style="font-style: italic;"><br>



























































</span><br>


</div>



























































<h3><a class="mozTocH3" name="mozTocId392911"></a><a name="wrapping_groovy"></a>Wrapping groovy scripts</h3>



























































<div style="margin-left: 40px;">Here a configuration sample for wrapping groovy scripts:<br>



























































<br>



























































<div style="margin-left: 40px;">wrapper.helloworld_groovy.conf:<br>



























































<br>



























































<span style="font-style: italic;"># the groovy file to be executed</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.groovy=scripts/helloworld.gv</span><br style="font-style: italic;">



























































<span style="font-style: italic;"><br>



























































# the required jar files </span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.java.classpath.1=lib/extended/groovy/*.jar</span><br>



























































<br style="font-style: italic;">



























































<span style="font-style: italic;"># Name of the service</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.ntservice.name = groovy_helloworld_service</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;"># Display name of the service</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.ntservice.displayname =&nbsp; groovy_helloworld_service</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;"># Description of the service</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.ntservice.description = Sample groovy script</span><br>



























































</div>



























































<br>



























































Here the shell commands to install the script as a service and to start it<br>



























































<br>



























































<div style="margin-left: 40px; font-style: italic;">rem install it as a service<br>



























































java -jar wrapper.jar&nbsp; -i conf/wrapper.helloworld_groovy.conf<br>



























































rem start the service<br>



























































java -jar wrapper.jar -t conf/wrapper.helloworld_groovy.conf</div>



























































</div>



























































<h3><a class="mozTocH3" name="mozTocId361662"></a><a name="groovified"></a>YAJSW groovified: WrapperBuilder</h3>



























































<div style="margin-left: 40px;">YAJSW can be embeded in groovy scripts.<br>



























































Here an example for a groovy script to install tomcat as a service and to restart tomcat daily at 00:00 h<br>



























































Note that there are different way to implement this with YAJSW.<br>



























































Here will install 2 services: one for running tomcat, the other for running a groovy script which will install/start/stop tomcat<br>



























































<br>



























































The configuration file for running tomcat:<br>



























































Note
that we could do the complete configuration within the groovy script.
However it is better to separate configuration and logic.<br>



























































<br>



























































<div style="margin-left: 40px;">wrapper.tomcat.conf:<br>



























































<br>



























































<span style="font-style: italic;"># YAJSW: Application main class</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.java.app.mainclass = org.apache.catalina.startup.Bootstrap</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;"># working directory. This could be a system variable or could be set in the groovy script</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.working.dir = ${CATALINA_HOME}</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;"># classpath</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.java.classpath.1 = lib/*.jar</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.java.classpath.2 =&nbsp;bin/bootstrap.jar</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;"># &nbsp;additional options</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.java.additional.1 = -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.java.additional.2 = -Djava.util.logging.config.file=conf/logging.properties </span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.java.additional.3 = -Djava.endorsed.dirs=endorsed </span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.java.additional.4 = -Dcatalina.base=.</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.java.additional.5 =&nbsp; -Dcatalina.home=. </span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.java.additional.6 = -Djava.io.tmpdir=temp</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;"># restart tomcat if it crashes</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.on_exit.default=RESTART</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;">#restart tomcat if out of memory</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.trigger.1=java.lang.OutOfMemoryError</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.trigger.action=RESTART</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;"># Name of the service</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.ntservice.name = Tomcat</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;"># Display name of the service</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.ntservice.displayname = Tomcat</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;"># Description of the service</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.ntservice.description = Tomcat</span> </div>



























































<br>



























































The script for installing and restarting tomcat<br>



























































<br>



























































<div style="margin-left: 40px;">restart_tomcat.gv<br>



























































<br style="font-style: italic;">



























































<span style="font-style: italic;">/*</span><br style="font-style: italic;">



























































<span style="font-style: italic;">* script for restarting the tomcat service</span><br style="font-style: italic;">



























































<span style="font-style: italic;">*/</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;">import org.rzo.yajsw.groovy.*</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;">// get a wrapper builder</span><br style="font-style: italic;">



























































<span style="font-style: italic;">builder = new WrapperBuilder()</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;">// set the configuration file</span><br style="font-style: italic;">



























































<span style="font-style: italic;">builder."wrapper.config" = "conf/wrapper.tomcat.conf"</span><br style="font-style: italic;">



























































<span style="font-style: italic;">// set catalina home here if no system env var is available</span><br style="font-style: italic;">



























































<span style="font-style: italic;">//builder."CATALINA_HOME"=</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;">// get a service for starting tomcat</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;">// first set the app parameter </span><br style="font-style: italic;">



























































<span style="font-style: italic;">builder."wrapper.app.parameter.1" = "start"</span><br style="font-style: italic;">



























































<span style="font-style: italic;">// now get a service</span><br style="font-style: italic;">



























































<span style="font-style: italic;">tomcatService = builder.service()</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;">// get a process for stopping tomcat</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;">// first set the app parameter</span><br style="font-style: italic;">



























































<span style="font-style: italic;">builder."wrapper.app.parameter.1" = "stop"</span><br style="font-style: italic;">



























































<span style="font-style: italic;">// now get a process</span><br style="font-style: italic;">



























































<span style="font-style: italic;">tomcatStopProcess = builder.stop()</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;">// stop tomcat: equivalent to a call to catalina.bat stop</span><br style="font-style: italic;">



























































<span style="font-style: italic;">tomcatStopProcess.start()</span><br style="font-style: italic;">



























































<span style="font-style: italic;">// wait for this process to terminate</span><br style="font-style: italic;">



























































<span style="font-style: italic;">tomcatStopProcess.waitFor()</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;">if (tomcatStopProcess.exitCode == 0)</span><br style="font-style: italic;">



























































<span style="font-style: italic;">&nbsp;&nbsp;&nbsp; println "tomcat stopped"</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;">// check if the tomcat service is installed</span><br style="font-style: italic;">



























































<span style="font-style: italic;">if (!tomcatService.isInstalled())</span><br style="font-style: italic;">



























































<span style="font-style: italic;">&nbsp; tomcatService.install()</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;">// start the service</span><br style="font-style: italic;">



























































<span style="font-style: italic;">&nbsp;tomcatService.start()</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;">if (tomcatService.isRunning())</span><br style="font-style: italic;">



























































<span style="font-style: italic;">&nbsp; println "tomcat started"</span></div>



























































<br>



























































Next we need a configuration file for running the above script daily at 00:00 h<br>



























































<div style="margin-left: 40px;">wrapper.restart_tomcat_gv.conf<br>



























































<br>



























































<span style="font-style: italic;"># the groovy file to be executed</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.groovy=scripts/restart_tomcat.gv</span><br style="font-style: italic;">



























































<span style="font-style: italic;"><br>



























































# the required jar files </span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.java.classpath.1=lib/extended/groovy/*.jar</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;"># Name of the service</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.ntservice.name =&nbsp;restart_tomcat_gv</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;"># Display name of the service</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.ntservice.displayname =&nbsp; restart_tomcat_gv</span><br style="font-style: italic;">



























































<br style="font-style: italic;">



























































<span style="font-style: italic;"># Description of the service</span><br style="font-style: italic;">



























































<span style="font-style: italic;">wrapper.ntservice.description = script to restart tomcat<br>



























































<br>



























































# cron timer<br>



























































wrapper.timer.START.cron=0 0 0 * * ?</span></div>



























































<br>



























































At last we install the above script as a service and start it<br>



























































<br>



























































<div style="margin-left: 40px; font-style: italic;">rem install it as a service<br>



























































java -jar wrapper.jar&nbsp; -i conf/wrapper.restart_tomcat_gv.conf<br>



























































rem start the service<br>



























































java -jar wrapper.jar -t conf/wrapper.restart_tomcat_gv.conf</div>



























































<br>



























































Further samples for running the nutch crawler, nutch tomcat and nutch with solr are found in the conf and scripts folders.<br>



























































</div>




























































<h3><a class="mozTocH3" name="mozTocId309423"></a>Building YAJSW</h3>

















<div style="margin-left: 40px;">The source code is included in the download. <br>

















Go to the folder &lt;yajsw&gt;/build/gradle.<br>

















Execute gradlew.</div>

















<h3><a class="mozTocH3" name="mozTocId538610"></a><a name="NetworkLaunching"></a>Network Launching of Applications - Experimental</h3>

























































<div style="margin-left: 40px;">YAJSW offers a functionality similar to <a href="http://en.wikipedia.org/wiki/Java_web_start">java web start</a>
allowing users to run applications which are not or not completely
available on the current computer. We may for example use jar files
from a maven repository or from subversion. With YAJSW we however assume that we are in an
enterprise environment. Therefore we do not implement security features
such as sandbox. Nor do we, for now, implement features such as splash
screens or desktop icons. On the other hand we support other file
systems&nbsp; such as local files, HTTP, FTP, SFTP, HTTPS, WebDav,
Samba, Subversion, SMTP, ZIP, etc. For more details on the supported
file systems please refer to <a href="http://commons.apache.org/vfs/">commons vfs</a>.<br>

























































<br>

























































The property <span style="font-style: italic;">wrapper.codebase</span> defines the root base. All relative files specified in <span style="font-style: italic;">wrapper.resource</span> or <span style="font-style: italic;">wrapper.java.classpath</span> are supposed to be relative to this root.<br>

























































<br>

























































Example:<br>

























































<br>

























































<div style="margin-left: 40px;"><span style="font-style: italic;">wrapper.codebase=http://mysite.com/application-files<br>
























































<br>
























































</span>or<span style="font-style: italic;"><br>
























































<br>
























































</span><span style="font-style: italic;">wrapper.codebase=c:/dev</span></div>

























































<br>

























































The property <span style="font-style: italic;">wrapper.cache</span>
defines the path to the local cache. All remote files are copied to
this directory. On application startup we syncrhonize the cache with
the remote files by checking the file lastModified attribute.<br>
























































The boolean property <span style="font-style: italic;">wrapper.cache.local</span> indicates if local files should be copied to the cache. This property is false per default.<br>
























































<br>

























































Example:<br>

























































<br>

























































<div style="margin-left: 40px; font-style: italic;">wrapper.cache=${java.io.tmpdir}/myapp<br>
























































wrapper.cache.local=true</div>

























































<br>

























































NOTE: lazy download is not supported. All specified files are downloaded to the cache folder.<br>

























































NOTE: if the folder does not exist it is created.<br>
























































NOTE: if no cache is defined <span style="font-style: italic;">/yajsw_cache</span> is used as default cache folder<br>
























































<br>
























































<br>

























































The application may require more resources than just the classpath. These files (resources) are specified with the properties <span style="font-style: italic;">wapper.resource.&lt;n&gt;</span> and are downloaded &nbsp;to the <br>

























































<br>

























































Example:<br>

























































<div style="margin-left: 40px;"><span style="font-style: italic;">wrapper.resource.images = images/*.*</span><br style="font-style: italic;">
























































<span style="font-style: italic;">
wrapper.resource.lib.1=libs/mylib.dll</span><br>

























































<br>

























































are downloaded to:<br>

























































<br>
























































<span style="font-style: italic;">
${java.io.tmpdir}/myapp/images</span> and <span style="font-style: italic;">${java.io.tmpdir}/myapp/libs</span></div>

























































<br>

























































Classpath files may be relative or absolute. <br>

























































<br>
























































Example:<br>
























































<br>
























































<div style="margin-left: 40px;"><span style="font-style: italic;">wrapper.java.classpath.1=lib/test.jar</span><br style="font-style: italic;">
























































<span style="font-style: italic;">warpper.java.classpath.2=http://someothersite.com/lib2/test2.jar</span><br>
























































<br>
























































are downloaded to <br>
























































<br>
























































<span style="font-style: italic;">${java.io.tmpdir}/myapp/lib/test.jar</span> and <span style="font-style: italic;">${java.io.tmpdir}/myapp/lib2/test2.jar</span></div>
























































<br>

























































Configuration files may also be loaded remotely. <br>
























































<br>
























































Example:<br>
























































<br>
























































<div style="margin-left: 40px;">start the application as console<span style="font-style: italic;"><br>
























































<br>
























































java -jar wrapper.jar -c http//mysite.com/test/wrapper.conf</span><br>
























































<br>
























































install the application<br>
























































<br>
























































<span style="font-style: italic;">java -jar wrapper.jar -i http//mysite.com/test/wrapper.conf</span></div>

























































<br>
























































Within a conf file include files are absolute or relative. Relative
locations are assumed relative to the parent configuration &nbsp;file. <br>
























































<br>
























































Example:<br>
























































<br>
























































</div>
























































<div style="margin-left: 40px;">
<div style="margin-left: 40px;"><span style="font-style: italic;">include = wrapper_stop.conf</span><br style="font-style: italic;">
























































<span style="font-style: italic;">include = http//someothersite.com/conf2.conf</span><br style="font-style: italic;">
























































<br>
























































will include the cofiguration files:<br>
























































<span style="font-style: italic;"><br>
























































http//mysite.com/test/wrapper_stop.conf</span> and  <span style="font-style: italic;">http//someothersite.com/conf2.conf</span><br style="font-style: italic;">
























































</div>
























































</div>
























































<div style="margin-left: 40px;"><br>
























































Platform specific configuration can be defined in platform specific include configuratoin files:<br>
























































<br>
























































Example:<br>
























































<br>
























































<div style="margin-left: 40px;"><span style="font-style: italic;">include=${os.name}.{os.arch}.libs.conf</span><br>
























































<br>
























































On windows XP, x86 YAJSW will include the following file:<br>
























































<br>
























































<span style="font-style: italic;">windows XP.x86.libs.conf</span><br>
























































</div>
























































<br>
























































To launch an application we proceed as follows:<br>
























































<ul>
























































  <li>load the configuration file to memory.</li>























































  <li>if the configuration file is a remote file create a local interpolated copy, in which all includes have been resolved</li>
























































  <li>copy&nbsp; files to the cache if these are not in the cache or have different lastModified property</li>
























































  <li>execute the wrapper command (-c, -i, -u, -t, -p)</li>
























































</ul>
























































<br>





















































NOTE: currently the remote resources are downloaded only when the
service is installed. When the service is started the jar files from
the cache are used to start the application. To check for a new version
of the application you will have to reinstall the service.<br>




















































<br>




















































NOTE: currently the application is not started if the remote host is
not accessible, even if the files have been previously downloaded.<br>





















































<br>





















































NOTE: some transport protocols, such as webdav or ftp support folder
listing and therefore also support wild cards for classpath files.<br>




















































<br>




















































NOTE: setting http proxy: <span style="font-style: italic;">



java -jar wrapper.jar <span style="font-weight: bold;">-Dhttp.proxyHost=&lt;myproxy&gt; -Dhttp.proxyPort=8080</span> -c http//mysite.com/test/wrapper.conf</span><br>




















































NOTE: for now only http proxy settings is supported. In future other <a href="http://java.sun.com/javase/6/docs/technotes/guides/net/proxies.html">proxy settings</a> will be supported.<br>




















































NOTE: proxy exclusion list is currently not supported.<br>

















































<br>

















































NOTE: A batch script, remoteLauchTomcatDemo.bat,&nbsp; is provided to
test this feature. This will wrap a tomcat which is installed on my
test server (eprognos.com) and will start it localy on port 8081. The
configuration file is <a href="http://eprognos.com/webdav/wrapper.tomcat.conf">http://eprognos.com/webdav/wrapper.tomcat.conf</a><br>




















































<br>




















































This is a new feature. If you have suggestions or further requirements
on how to integrate the wrapper functionality with remote launching,
please post your ideas on the project forum.<br>





















































<br>

























































</div>

























































<h3><a class="mozTocH3" name="mozTocId778097"></a>Wrapping JNLP - Experimental</h3>





















































<div style="margin-left: 40px;">A call to <br>





















































<br>





















































<div style="margin-left: 40px;"><span style="font-style: italic;">java -jar wrapper.jar&nbsp; -c http://java.sun.com/javase/technologies/desktop/javawebstart/apps/draw.jnlp</span><br>





















































</div>





















































<br>





















































will parse the jnlp file, convert it to a wrapper configuration file and will launch the application as&nbsp;described in <a href="#NetworkLaunching">Network Launching.</a><br>





















































<br>





















































<div style="margin-left: 40px;"><span style="font-style: italic;">java -jar wrapper.jar&nbsp; -i http://java.sun.com/javase/technologies/desktop/javawebstart/apps/draw.jnlp</span></div>





















































<br>





















































will parse the jnlp file, convert it to a wrapper configuration file
save the file to the cache and install a service which will use the
configuration file to launch the application.<br>





















































<br>





















































However, the draw application requires the file javaws.jar, which is
not defined in draw.jnlp. This is achieved by adding some local
configuration as follows:<br>





















































<br>





















































<div style="margin-left: 40px;"><span style="font-style: italic;">java -jar wrapper.jar&nbsp; -c <span style="font-weight: bold;">-d conf/wrapper.javaws.conf </span>http://java.sun.com/javase/technologies/desktop/javawebstart/apps/draw.jnlp</span></div>





















































<br>





















































Currently only "simple" jnlp configurations are supported. The following jnlp tags/attributes are <span style="font-weight: bold;">NOT</span> supported:<br>





















































<ul>





















































  <li>External jnlp</li>





















































  <li>OS, arch, locale depdent configuration</li>





















































  <li>Applets</li>





















































  <li>package, extension, j2se, nativelib, component-desc, installer-desc, part, lazy, &lt;jar .. main-class&gt;, information</li>





















































</ul>





















































If you require one of these features please post a message on the project forum or tracker.<br>




















































<br>




















































NOTE: -d &lt;remote file&gt; is supported.</div>





















































<h3><a class="mozTocH3" name="mozTocId531550"></a>Java Web Start Support - Experimental - Currently only for windows</h3>





























<div style="text-align: center;"><img style="width: 533px; height: 452px;" alt="" src="WebStart.JPG"></div>






































<div style="margin-left: 40px;">Installing the same service on multiple
servers is a pain. YAJSW may help by providing a java webstart
bootstrap. This will invoke a jar file which will download YAJSW and
will call either runConsole or installService.<br>






































<br>






































The following <a href="http://yajsw.sourceforge.net/yajsw/yajsw.jnlp">live demo</a> will download YAJSW which will download and start a tomcat server<br>


























<br>


























Note: for windows 7, Vista &amp; co: you will need to run your browser
as administrator (right click -&gt; run as administrator) in order to
be able to start/install services.<br>


































<br>


































In a future release this feature will be used to drive yajsw tests on multiple platforms.<br>






































<br>






































For this function the following files are required:<br>






































<br>






































<div style="margin-left: 40px;">wrapper.jnlp : &nbsp;please set the following arguments:<br>






































<br>






































&nbsp;&nbsp;&nbsp;&nbsp; &lt;argument&gt;path-to-yajsw-folder-on-the-server&lt;/argument&gt;<br>





































&nbsp;&nbsp;&nbsp;&nbsp; &lt;argument&gt;destination-folder-on-local-computer&lt;/argument&gt;<br>






































&nbsp;&nbsp;&nbsp;&nbsp; &lt;argument&gt;yajsw-command (c,i,t,y)&lt;/argument&gt;<br>






































&nbsp;&nbsp;&nbsp;&nbsp; &lt;argument&gt;remote-configuration-of-application&lt;/argument&gt;<br>






































&nbsp;&nbsp;&nbsp;&nbsp; &lt;argument&gt;nogui&lt;/argument&gt; (optional)<br>





































<br>





































If you do not want yajsw to display a gui add <span style="font-weight: bold;">nogui</span> as last argument.<br>





































If a gui is enabeled you may leave certain arguments empty and have the user enter them.<br>






































<br>






































wrapperWS.jar<br>






































&nbsp; &nbsp; &nbsp; this is the file which is executed by java web start. The file has been signed as described <a href="http://weblogs.java.net/blog/2005/05/20/signing-jars-javanet-web-start-applications">here</a>. You may decide to sign it with your credentionals. The keystore and batch files for signing are found in the build folder<br>






































<br>






































remote-configuration-of-application<br>






































&nbsp;&nbsp;&nbsp; this file is the file used to remotely sart the applicaition as described <a href="#NetworkLaunching">here</a>.<br>






































<br>






































</div>






































</div>






































<h3><a class="mozTocH3" name="mozTocId316423"></a>Services Manager - Experimental - Currently only for windows</h3>



























<br>





























<div style="text-align: center;"><img style="width: 948px; height: 649px;" alt="" src="ServicesManager.JPG"></div>





























<div style="margin-left: 40px;">This new feature is currently
implemented for windows only. If you would like to have this feature
for other frameworks please post on the project forum.<br>

























<br>





























Managing services on multiple servers in a multi-platform environment
may be cumbersome. The YAJSW Services Manager allows one to :<br>





























<ul>





























  <li>View services and YAJSW console processes on multiple hosts:
Select one or multiple hosts and click "&gt;&gt;" to view their
services. Click "&lt;&lt;" to remove them from the services table.</li>





























  <li>Hosts are add using the "+" button. And removed using the "-" file.</li>

























  <li>All settings are saved in the "ServiceManager.ser" file.</li>





























  <li>Start/Stop services and YAJSW console processes:&nbsp;Select one or more services and click start/stop</li>





























  <li>Install/Uninstall YAJSW services: Select one or more services and click uninstall. To install click
install enter the path to a YAJSW configuration select the hosts and click install.</li>

























  <li>To update YAJSW console applications, the according wrapper must
be running. Select the console application and click the "reload
Console App" button.</li>





























  <li>To maintain an overview of the services you are interested in:
select the services you do not want to see and click "&lt;&lt;" to
hide them. To add them to the services list click "&gt;&gt;".</li>





























</ul>





























The services manager consists of a server and a client process.<br>
























The server process must be running on all managed hosts. To run/install
the server use the according scripts in the &lt;yajsw&gt;/bat folder<br>





























To start the user interface use the script under &lt;yajsw&gt;/bat folder.<br>


























<br>


























You may also start the server and client using java web start:<br>


























<br>


























<a href="http://yajsw.sourceforge.net/yajsw/serviceManagerServer.jnlp">start server using java web start</a><br>


























<br>


























<a href="http://yajsw.sourceforge.net/yajsw/serviceManagerClient.jnlp">start client using java web start</a><br>
























<br>
























NOTE: the client server communication is implemented through
asynchronous hessian rpc. This will hopefully be integrated in the
jboss netty project.<br>


























</div>





























<h3><a class="mozTocH3" name="mozTocId188498"></a>Android Services Manager - experimental</h3>






















<br>






















<div style="margin-left: 40px;">This android app allows one to monitor services and daemons on multiple servers on an android mobile phone.<br>






















<br>






















The android application connects to a single server process
called&nbsp; the ServiceManagerHub. The hub server, using multicast
discovery, connects to the Services Manager Server processes in the
network.<br>






















The android application connects to the hub process, retrieves the data and displays it on the smartphone.<br>






















<br>






















Currently the app implements 2 views:<br>






















<br>






















<ul>






















  <li>List of hosts : RED no connection to the Services Manager Server on the host, GREEN host is connected</li>






















  <li>List of services: RED: service is not running, GREEN: service is
running, YELLOW: YAJSW wrapper is running, but not the application.</li>






















</ul>






















Swipe the screen to change the view.<br>






















<br>






















In future releases further functions will be implemented. <br>






















<br>






















Feedback is welcome.</div>






















<h3><a class="mozTocH3" name="mozTocId790812"></a>Service Update - Experimental - Currently windows only</h3>








<div style="margin-left: 40px;">YAJSW can support the tedious task of updating the wrapper and/or the application of a service.<br>








The update can be done automatically when the service is stopped, or triggered manually through the tray icon.<br>








<br>








The update is defined in an update configuration file with the following properties:<br>








<br>








<div style="margin-left: 40px;">
<div style="margin-left: 40px;">
<ul>








  <li><span style="font-weight: bold; font-style: italic;">update.wrapper.src</span>: optional, VFS-URI to the wrapper zip file or folder</li>








  <li><span style="font-weight: bold; font-style: italic;">update.wrapper.dest</span>: must be defined if update.wrapper.src is defined, the destination folder on the server where yajsw will be extracted</li>








  <li><span style="font-weight: bold; font-style: italic;">update.app.config</span>: configuration of the application to be installed</li>








</ul>








</div>








</div>








All paths must be absolute.<br>








<br>








Example:<br>








<br>








<div style="margin-left: 40px;">update.wrapper.src =
zip:http://sourceforge.net/projects/yajsw/files/yajsw/yajsw-stable11.03/yajsw-stable-11.03.zip/download?use_mirror=heanet<br>








update.wrapper.desp = d:/install/yajsw/yajsw-stable-11.03<br>








update.app.config = http://mysite.com/myapp-1.1.conf</div>








<br>








The update can be triggered manually through the system tray icon: On
click of the update button the user is prompted to enter the path to
the update configuration file. This can be of any type supported by VFS.<br>








<br>








Example:<br>








<br>








<div style="margin-left: 40px;">http://myserver.com/update-1.1.conf</div>








<br>








If the configuration file path contains wild cards, the update will use the most recent matching file.<br>








<br>








The update can also be triggered through jmx.<br>








<br>








The update can also be done automatically. For this the following
properties can be defined in the configuration file of the application:<br>








<br>








<div style="margin-left: 40px;">
<ul>








  <li>wrapper.update.conf: path of the update configuration file, if
this property is set the configuration file entered through the tray
icon or jmx is ignored. If the path contains wild cards the most recent
matching file is used.</li>








  <li>wrapper.update.auto: if true, the update is triggered automatically whenever the service is stopped. default: false</li>








</ul>








</div>








<br>








Example:<br>








<br>








<div style="margin-left: 40px;">wrapper.update.conf=http://myserver.com/update-1.1.conf<br>








wrapper.update.auto=true</div>








<br>








The update is implemented as follows:<br>








<br>








- a process is spawned which uninstalls the service, and updates the
wrapper. and spawns a second process which will use the newly installed
wrapper<br>








- the second process then invokes the install and if &nbsp;the update
has not been started from within the service starts the service.<br>








<br>








Since this is a new function comments and suggestions are welcome.<br>








<br>








</div>








<h3><a class="mozTocH3" name="mozTocId966"></a>Third
party Libraries</h3>




























































<ul>




























































  <li><a href="http://commons.apache.org/configuration/">Commons
Configuration</a>: [APACHE] Configuration
is done using commons configuration, thus allowing a richer and more
flexible configuration. YAJSW additionaly supports OS environment
variables and java system properties.</li>




























































  <li><a href="https://github.com/twall/jna">JNA</a>
: [APACHE] The complete implementation is thus in java instead of C and will be
available on all platforms for&nbsp;which JNA will be available.</li>




























































  <li><a href="http://netty.io/">Netty</a> : [APACHE] TCP/IP
inter-process communication is done using jboss netty. This makes the
implementation of the communication readable and easily adaptable.</li>




























































  <li><a href="http://commons.apache.org/sandbox/commons-cli2/">Commons
CLI 2</a> : [APACHE] Command Line Interface can thus be easily extended.</li>




























































  <li><a href="http://quartz-scheduler.org/">Quatrz</a>
:  [APACHE] &nbsp;Job scheduling</li>




























































  <li><a href="http://www.karneim.com/jrexx/">JREXX</a>
: High Performance regular expression matching, as an alternative for
filter matching (no longer used since release 12.00)</li>




  <li><a href="http://www.brics.dk/automaton/index.html">dk.brics.automaton</a>: [BSD] High Performance regular expression matching (since release 12.00 due to license change)</li>




























































  <li><a href="http://groovy.codehaus.org/">Groovy</a>
: [APCAHE] for scripts.</li>




























































  <li><a href="http://java.sun.com/products/javamail/">JavaMail</a>
: [CDDL, GPL, BSD]&nbsp; used for sending emails (in sendMail.gv)</li>




























































  <li><a href="http://launch4j.sourceforge.net/">Launch4j</a>
: for generating wrapper.exe on windows</li>




























































  <li><a href="http://www.joda.org/joda-time/">Joda</a>: [APACHE]
for easier handling of time and date (used in timeCondition.gv)</li>




























































  <li><a href="http://tango.freedesktop.org/Tango_Icon_Library">Tango
Icon Library</a>: Icons for the system tray icon</li>




























































  <li><a href="http://www.snmp4j.org/">SNMP4J</a>:
[APACHE] for sending snmp traps (in snmpTrap.gv)</li>




























































  <li><a href="http://www.famfamfam.com/lab/icons/">famfamfam</a>
: icons</li>




























































  <li><a href="https://java.net/projects/abeille">Abeille</a>
: [LGPL, BSD] Gui forms designer</li>




























































  <li><a href="https://forms.dev.java.net/">JGoodies
FormLayout</a> : [BSD] Layout used by abeille</li>




























































  <li><a href="http://velocity.apache.org/">Apache
Velocity</a> : [APACHE] java template engine</li>






















































  <li><a href="http://commons.apache.org/vfs/">Commons VFS</a> : [APACHE] Virtual File System for downloading files in network launching</li>














































  <li><a href="http://testng.org/">testNG</a> unit test framework used with <a href="http://groovy.codehaus.org/Using+TestNG+with+Groovy">groovy</a></li>











































  <li><a href="http://www.jmock.org/">jMock</a> mock objects framework for unit tests</li>

























  <li><a href="http://publicobject.com/glazedlists/">GlazedLists</a>: [LGPL, MPL]GUI grid framework used in ServiceManagerClient</li>
























  <li><a href="http://www.caucho.com/resin-3.0/protocols/hessian-2.0-spec.xtp">Hessian</a>: [APACHE] Hessian is a compact binary protocol for connecting web
      services.</li>

















  <li><a href="http://www.gradle.org/">Gradle</a>: [APACHE] A build tool alternative to maven, ant &amp; co.</li>




























































</ul>




























































<address></address>
































































































<br>




























































- Ron<br>




























































<br>




























































<span style="font-weight: bold;"></span></div>




























































</div>




























































</div>




























































</body>
</html>
