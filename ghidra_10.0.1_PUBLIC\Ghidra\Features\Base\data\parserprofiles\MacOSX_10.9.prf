/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX10.9.sdk/usr/stdio.h
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX10.9.sdk/usr/include/stdint.h
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX10.9.sdk/usr/include/ctype.h
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX10.9.sdk/usr/include/sys/types.h
sys/cdefs.h
sys/socketvar.h
libc.h
objc\objc-runtime.h
objc\objc-load.h
objc\objc-auto.h
objc\objc-exception.h
AssertMacros.h
Block.h
CommonCrypto/CommonCrypto.h
NSSystemDirectories.h
Xplugin.h
aio.h
aliasdb.h
architecture/alignment.h
architecture/i386/asm_help.h
architecture/i386/byte_order.h
architecture/i386/fpu.h
architecture/i386/frame.h
architecture/i386/table.h
arpa/nameser.h
bootparams.h
bsm/audit_session.h
bsm/libbsm.h
bzlib.h
cache_callbacks.h
checkint.h
copyfile.h
cpio.h
default_pager/default_pager_object.h
device/device_port.h
dispatch/introspection.h
dns_sd.h
dns_util.h
editline/readline.h
execinfo.h
ffi/ffi.h
ffi/ffi_common.h
fmtmsg.h
fstab.h
fts.h
ftw.h
get_compat.h
glob.h
gssapi/gssapi_generic.h
gssapi/gssapi_krb5.h
histedit.h
i386/setjmp.h
i386/user_ldt.h
ifaddrs.h
iso646.h
krb5/locate_plugin.h
krb5/preauth_plugin.h
launch.h
ldap_schema.h
ldap_utf8.h
ldif.h
libcharset.h
libgen.h
libkern/OSCacheControl.h
libkern/OSDebug.h
libkern/OSKextLib.h
libkern/OSTypes.h
libproc.h
libunwind.h
libxml2/libxml/HTMLtree.h
libxml2/libxml/SAX.h
libxml2/libxml/SAX2.h
libxml2/libxml/catalog.h
libxml2/libxml/chvalid.h
libxml2/libxml/debugXML.h
libxml2/libxml/dict.h
libxml2/libxml/encoding.h
libxml2/libxml/globals.h
libxml2/libxml/parser.h
libxml2/libxml/relaxng.h
libxml2/libxml/tree.h
libxml2/libxml/xmlIO.h
libxml2/libxml/xmlmemory.h
libxml2/libxml/xmlwriter.h
libxslt/transform.h
libxslt/xsltlocale.h
libxslt/xsltutils.h
mach-o/arch.h
mach-o/compact_unwind_encoding.h
mach-o/dyld_images.h
mach-o/getsect.h
mach-o/i386/swap.h
mach-o/ldsyms.h
mach-o/swap.h
mach/clock.h
mach/clock_reply.h
mach/exc.h
mach/exception.h
mach/mach_syscalls.h
mach/mach_time.h
mach/mach_vm.h
mach/machine/asm.h
mach/machine/ndr_def.h
mach/port_obj.h
mach/shared_memory_server.h
mach/shared_region.h
mach/sync.h
mach/vm_param.h
mach/vm_task.h
mach_debug/mach_debug.h
machine/byte_order.h
machine/profile.h
machine/setjmp.h
membership.h
menu.h
miscfs/devfs/devfs.h
miscfs/specfs/specdev.h
monitor.h
ncurses.h
ndbm.h
net/if_dl.h
net/if_llc.h
net/if_media.h
net/if_mib.h
net/if_types.h
net/kext_net.h
net/ndrv.h
netinet/bootp.h
netinet/icmp6.h
netinet/icmp_var.h
netinet/if_ether.h
netinet/igmp.h
netinet/igmp_var.h
netinet/in_var.h
netinet/ip6.h
/netinet/in_systm.h
netinet/ip_icmp.h
netinet/ip_mroute.h
netinet/tcp_fsm.h
netinet/tcp_seq.h
netinet/tcp_var.h
netinet/tcpip.h
netinet/udp_var.h
netinet6/ah.h
netinet6/esp.h
netinet6/ip6_mroute.h
netinet6/ipcomp.h
netinet6/nd6.h
netinet6/pim6.h
netinet6/raw_ip6.h
netkey/keysock.h
nfs/krpc.h
nfs/nfsproto.h
nfs/rpcv2.h
nfs/xdr_subs.h
nl_types.h
openssl/opensslconf.h
openssl/aes.h
openssl/asn1_mac.h
openssl/asn1t.h
openssl/blowfish.h
openssl/cast.h
openssl/conf_api.h
openssl/dso.h
openssl/engine.h
openssl/krb5_asn.h
openssl/md2.h
openssl/md4.h
openssl/md5.h
openssl/mdc2.h
openssl/ocsp.h
openssl/pkcs12.h
openssl/rc2.h
openssl/rc4.h
openssl/rc5.h
openssl/ripemd.h
openssl/seed.h
openssl/tmdiff.h
openssl/txt_db.h
panel.h
paths.h
pcap-bpf.h
pcap-namedb.h
pexpert/i386/efi.h
pexpert/machine/protos.h
pexpert/pexpert.h
printerdb.h
protocols/talkd.h
protocols/timed.h
pthread_spis.h
readline/history.h
readline/readline.h
readpassphrase.h
removefile.h
resolv.h
rpc/types.h
rpc/xdr.h
rpc/pmap_clnt.h
rpc/pmap_prot.h
rpc/pmap_rmt.h
rpcsvc/bootparam_prot.h
rpcsvc/klm_prot.h
rpcsvc/mount.h
rpcsvc/nfs_prot.h
rpcsvc/nlm_prot.h
rpcsvc/rex.h
rpcsvc/rnusers.h
rpcsvc/rquota.h
rpcsvc/rstat.h
rpcsvc/rusers.h
rpcsvc/rwall.h
rpcsvc/sm_inter.h
rpcsvc/spray.h
rpcsvc/yp.h
rpcsvc/yp_prot.h
rpcsvc/ypclnt.h
rpcsvc/yppasswd.h
rune.h
sandbox.h
sasl/gai.h
sasl/saslutil.h
search.h
security/mac.h
security/pam_appl.h
security/pam_modules.h
servers/bootstrap.h
servers/bootstrap_defs.h
servers/ls_defs.h
servers/netname.h
servers/nm_defs.h
slapi-plugin.h
spawn.h
sqlite3ext.h
standards.h
strhash.h
stringlist.h
sys/acct.h
sys/acl.h
sys/disk.h
sys/fasttrap.h
sys/filedesc.h
sys/gmon.h
sys/kdebug.h
sys/kern_memorystatus.h
sys/lctx.h
sys/lockf.h
sys/mbuf.h
sys/msg.h
sys/msgbuf.h
sys/pipe.h
sys/posix_sem.h
sys/posix_shm.h
sys/protosw.h
sys/ptrace.h
sys/quota.h
sys/random.h
sys/rbtree.h
sys/reboot.h
sys/resourcevar.h
sys/sbuf.h
sys/sdt.h
sys/sem.h
sys/shm.h
sys/signalvar.h
sys/statvfs.h
sys/tprintf.h
sys/trace.h
sys/tty.h
sys/unpcb.h
sys/user.h
sys/utfconv.h
sys/utsname.h
sys/vadvise.h
sys/vcmd.h
sys/vmmeter.h
sys/vmparam.h
sys/vnioctl.h
sys/vstat.h
sys/xattr.h
tclTomMath.h
term_entry.h
termcap.h
tidy/buffio.h
tidy/platform.h
tidy/tidy.h
timeconv.h
ttyent.h
ulimit.h
unwind.h
utmpx.h
vis.h
vproc.h
wordexp.h
xar/xar.h
zlib.h

-I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX10.9.sdk/usr/include
-I/Applications/Xcode.app/Developer/usr/lib/llvm-gcc/4.2.1/include
-I/Applications/Xcode.app/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include
-I/Applications/Xcode.app/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/6.0/include
-DBSD
-D__APPLE__
-D__GNUC__=4
-D__x86_64__=1
-D__builtin_va_list="void *"
-D_LARGEFILE64_SOURCE=0
-D_DARWIN_C_SOURCE
-DXP_NO_X_HEADERS
-D_POSIX_C_SOURCE=1900
-D__STRICT_ANSI__
-DBSD=199103
-D__const=""
-D_INTEGRAL_MAX_BITS=32
-D__m128="long long"
-D__m128i="long long"
-D__m128d="long long"
-D__i386__
-D__GNUC__
-D__APPLE_CPP__
-D__LITTLE_ENDIAN__
-DBYTE_ORDER=LITTLE_ENDIAN
-D__MACH__
-D_MAC_
-DTARGET_API_MAC_OSX
-DHANDLE="unsigned long"
-D_Bool="BOOL"
-D_WCHAR_T
-D_Complex
-Drestrict
-D__restrict
-D__LP64__
-DSIXTY_FOUT_BIT_LONG
-DBN_ULONG="unsigned long"
-D_FORTIFY_SOURCE=0
-Dbool_t=boolean_t
