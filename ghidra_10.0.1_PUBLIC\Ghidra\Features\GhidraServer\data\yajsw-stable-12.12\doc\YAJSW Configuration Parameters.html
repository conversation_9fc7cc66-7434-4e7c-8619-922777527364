<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>

















































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <meta content="text/html; charset=ISO-8859-1" http-equiv="content-type">

















































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <title>YAJSW Configuration Parameters</title>
</head>


<body>

































<br>
















































<br>
















































<div style="text-align: center;"><big><big><span style="font-weight: bold;">YAJSW Configuration Properties</span></big></big></div>







































































<br>

























<ol id="mozToc">

























<!--mozToc h4 4--><li><a href="#mozTocId390170">Introduction</a>
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    <ol>

























      <li>
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        <ol>

























          <li>
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            <ol>

























              <li><a href="#mozTocId846544">General Notes</a></li>

























              <li><a href="#mozTocId338951">Accessing Environment Properties</a></li>

























              <li><a href="#mozTocId174362">Includes</a></li>

























              <li><a href="#mozTocId639405">Conditional Includes</a></li>

























              <li><a href="#mozTocId408541">Groovy Expressions</a></li>

























              <li><a href="#mozTocId280789">Multiple Lines</a></li>

























              <li><a href="#mozTocId761097">Lists and arrays</a></li>

























              <li><a href="#mozTocId172527">Multiple Value Properties</a></li>

























              <li><a href="#mozTocId372157">Classpath Wildcards</a></li>

























              <li><a href="#mozTocId570283">New&nbsp; Configuration Properties</a></li>

























              <li><a href="#mozTocId579660">New Optional Configuration Properties</a></li>

























              <li><a href="#mozTocId669993">Configuration Properties from tanuki software</a></li>

























            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            </ol>

























          </li>

























        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        </ol>

























      </li>

























    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    </ol>

























  </li>

























</ol>





































<h4><a class="mozTocH4" name="mozTocId390170"></a>Introduction</h4>





































In order to wrap an application you will either need to create a
configuration file or specify the configuration properties as command
line attributes of the wrapper exe.<br>





































It is recommended to create a configuration file and store it in the <span style="font-style: italic;">&lt;yajsw&gt;/conf</span> folder. <br>





































<br>





































You can create a configuration file for a running application by using the configuration generator.<br>





































<br>





































In the following we describe the configuration properties.<br>





































<br>





































<span style="font-weight: bold;">NOTE: this description applies only
for configuration properties and configuration files. It does not apply
for batch scripts such as &lt;yajsw&gt;/bat/setenv.bat or
&lt;yajsw&gt;/bat/setenv.sh</span>










<h4><a class="mozTocH4" name="mozTocId846544"></a>General Notes</h4>





































<br>






















































&nbsp;"\"&nbsp; is the escape character. For file paths it is
preferable to use "/". <br>






















































If back-slash is required use "\\".<br>






















































<br>






















































"," is used as list delimiter. If required you will have to escape it
(eg write "\,")<br>






















































<br>






















































For windows %SOME_ENV_VAR% will not work. You will have to use
${SOME_ENV_VAR}<br>






















































<br>






















































Example:<br>






















































<br>






















































<table style="text-align: left; width: 1182px; height: 32px;" border="1" cellpadding="2" cellspacing="2">






















































  <tbody>






















































    <tr>






















































      <td style="background-color: rgb(204, 204, 204);">&nbsp;
&nbsp; &nbsp; &nbsp; &nbsp;
wrapper.java.additional.2=-Xdebug
-Xrunjdwp:transport=dt_socket<span style="color: red;">\,</span>server=y<span style="color: red;">\,</span>suspend=y<span style="text-decoration: underline; color: red;">\,</span>address=1044<br>






















































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <div style="margin-left: 40px;">wrapper.java.library.path.2=<span style="color: red;">${</span>JNA_HOME<span style="color: red;">}</span>/bin<br>






















































wrapper.java.library.path.1=.<span style="color: red;">\\</span>bin</div>






















































      </td>






















































    </tr>






















































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </tbody>
</table>






















































<h4></h4>



<table style="text-align: left; width: 1180px; height: 32px;" border="1" cellpadding="2" cellspacing="2">



  <tbody>



    <tr>



      <td style="background-color: rgb(255, 204, 204);">&nbsp;<span style="font-weight: bold;">NOTE</span>:<br>



      
      
      
      <div style="margin-left: 40px;">${abc.efg} is similar to ${abc.getefg()} therefore <span style="font-weight: bold;"><br>



      <br>



do not use "." in environment or configuration variables</span>.<br>



      <br>



instead use : ${abc_efg}<br>



      <br>
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------<br>



do not use:<br>


      <br>


wrapper.java.additional.2=-Xdebug
-Xrunjdwp:transport=dt_socket<span style="color: red;">\,</span>server=y<span style="color: red;">\,</span>suspend=y<span style="text-decoration: underline; color: red;">\,</span>address=1044<br>



      <br>



instead:<br>


      <br>




wrapper.java.additional.2.1=-Xdebug
      <br>




wrapper.java.additional.2.2=-Xrunjdwp:transport=dt_socket<span style="color: red;">\,</span>server=y<span style="color: red;">\,</span>suspend=y<span style="text-decoration: underline; color: red;">\,</span>address=1044<br>



      <br>



      <span style="font-weight: bold;">use one java option or main method argument per configuration property.<br>


      </span></div>



      </td>



    </tr>



  
  
  
  </tbody>
</table>



<h4><br>



</h4>



<h4><a class="mozTocH4" name="mozTocId338951"></a>Accessing Environment Properties</h4>






















































If you defined OS environment variables you may access these directly
within the configuration.<br>






















































<br>






















































Example (Windows)<br>






















































<br>






















































set wrapper_java_path=c:/java/jdk1.5.0<br>






















































<br>






















































<table style="text-align: left; width: 1156px; height: 32px;" border="1" cellpadding="2" cellspacing="2">






















































  <tbody>






















































    <tr>






















































      <td style="background-color: rgb(204, 204, 204);">&nbsp;
&nbsp; &nbsp; &nbsp; &nbsp;
wrapper.java=${wrapper_java_path}/bin/java.exe</td>






















































    </tr>






















































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </tbody>
</table>






















































<br>

































NOTE: Windows: environment variables are converted to lower case.
Therefore event if &nbsp;you use PATH on your computer you have too
call ${path} in the configuration file.<br>

































<h4><a class="mozTocH4" name="mozTocId174362"></a>Includes</h4>






















































If a property is named "<code>include</code>" and the value
of that property is the name of a file on the disk, that file will be
included into the configuration.<br>






















































<br>






















































Example:<br>






















































<br>






















































<table style="text-align: left; width: 1159px; height: 32px;" border="1" cellpadding="2" cellspacing="2">






















































  <tbody>






















































    <tr>






















































      <td style="background-color: rgb(204, 204, 204);">tomcat_start.conf:<br>






















































      <br>






















































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <div style="margin-left: 40px;">include=tomcat_base.conf<br>






















































wrapper.app.parameter.1=start<br>






















































wrapper.stop=tomcat_stop.conf<br>






















































      </div>






















































      <br>






















































tomcat_stop.conf:<br>






















































      <br>






















































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <div style="margin-left: 40px;">include=tomcat_base.conf<br>






















































wrapper.app.parameter.1=stop</div>






















































      </td>






















































    </tr>






















































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </tbody>
</table>






















































<br>






















































<br>




















































<h4><a class="mozTocH4" name="mozTocId639405"></a>Conditional Includes</h4>




















































Some applications require different configurations for different
platforms, operating systems or languages. This can be achieved by
creating platform specific includes. As example, our application
requires<br>




















































us to set the native library depending on the operating system and
architecture. We determine these from the System.properties "os.name"
and "os.arch". Per supported os and arch we create a configuration file<br>




















































named &lt;os.name&gt;.&lt;arch&gt;.conf. Within our configuration file we define include as follows:<br>




















































<br>






















































<table style="text-align: left; width: 1156px; height: 32px;" border="1" cellpadding="2" cellspacing="2">






















































  <tbody>






















































    <tr>






















































      <td style="background-color: rgb(204, 204, 204);">&nbsp;
&nbsp; &nbsp; &nbsp; &nbsp; include=${os.name}.${os.arch}.conf<br>




















































      <br>




















































The file <br>




















































&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Windows XP.x86.conf<br>




















































      <br>




















































defines the platform specific library <br>




















































&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;wrapper.java.additional.1=-Dlib=w86.dll<br>




















































      <br>




















































Whereas the file<br>




















































&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Linux.x86.conf<br>




















































      <br>




















































defines<br>




















































&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;wrapper.java.additional.1=-Dlib=l86.iso<br>




















































      </td>






















































    </tr>






















































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </tbody>
</table>






















































<span style="font-weight: bold;"><br>

















































<br>

















































</span>
<h4><a class="mozTocH4" name="mozTocId408541"></a>Groovy Expressions</h4>

















































Alternatively to conditional includes, one may use groovy scripts.<br>

















































${ expr } are interpreted as groovy expressions which are evaluated.<br>

















































<br>

















































Example:<br>

















































<br>

















































<br>

















































<table style="text-align: left; width: 1149px; height: 32px;" border="1" cellpadding="2" cellspacing="2">

















































  <tbody>

















































    <tr>

















































      <td style="background-color: rgb(204, 204, 204);">wrapper.java.library.path.1=${ if
&nbsp;("${os.name}".toLowerCase().startsWith("windows")) "libw.dll";
else if&nbsp; ("${os.name}".toLowerCase().startsWith("linux")) "libl" }<br>

















































      </td>

















































    </tr>

















































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </tbody>
</table>

















































<br>

















































YAJSW includes a util binding for getting user input:<br>
















































<br>
















































<span style="font-weight: bold; font-style: italic;">util.inquireCLI(String)</span> prints the given message to the wrapper process console and waits for the user input.<br>




















































<br>




















































<span style="font-style: italic;"><span style="font-weight: bold;">util.inquireTryIcon</span></span><span style="font-weight: bold; font-style: italic;">(String)</span> displays the given message on the system tray icon and waits for the user input.The user will have to click the <span style="font-weight: bold;">respond</span> button in the tray icon menue and enter the required data.<br>


















































<br>
















































Example:<br>
















































<br>
















































<table style="text-align: left; width: 1136px; height: 32px;" border="1" cellpadding="2" cellspacing="2">
















































  <tbody>
















































    <tr>
















































      <td style="background-color: rgb(204, 204, 204);" align="undefined" valign="undefined"><span style="font-style: italic;">wrapper.ntservice.password = ${<span style="font-weight: bold;">util.inquireCLI</span>('please enter password for winods service user')}</span><br>
















































      <br>




















































      <span style="font-style: italic;">wrapper.app.password = ${<span style="font-weight: bold;">util.inquireTryIcon</span>('please enter password for winods service user')}</span></td>
















































    </tr>
















































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </tbody>
</table>
















































<br>

















































<h4><a class="mozTocH4" name="mozTocId280789"></a>Multiple Lines</h4>

















































For readability properties may be formatted on multiple lines.<br>

















































<br>

















































Example:<br>

















































<br>

















































<table style="text-align: left; width: 1145px; height: 32px;" border="1" cellpadding="2" cellspacing="2">

















































  <tbody>

















































    <tr>

















































      <td style="background-color: rgb(204, 204, 204);" align="undefined" valign="undefined"><br>






















































wrapper.script.ABORT=scripts/sendMail.gv<br>






















































wrapper.script.ABORT.args=<EMAIL>,"process aborted","process
took a long time\n \<br>

















































&nbsp;it had to be aborted\n \<br>

















































please do something"<br>

















































      <br>

















































wrapper.java.library.path.1=${ \<br>

















































&nbsp; &nbsp; &nbsp; if
&nbsp;("${os.name}".toLowerCase().startsWith("windows")) \<br>

















































&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;"libw.dll"; \<br>

















































&nbsp; &nbsp; &nbsp;&nbsp;else
if&nbsp; ("${os.name}".toLowerCase().startsWith("linux")) \<br>

















































&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; "libl"; \<br>

















































&nbsp; &nbsp; &nbsp; }<br>

















































      <br>

















































      </td>

















































    </tr>

















































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </tbody>
</table>


















































<h4><a class="mozTocH4" name="mozTocId761097"></a>Lists and arrays</h4>






















































Commons Configuration has the ability to return easily a list of
values. For example a properties file can contain a list of comma
separated values<br>






















































<br>






















































<span style="font-weight: bold;">NOTE:
currently please do not use lists. wrapper.java.classpath.1=x.jar,
y.jar will not work !. This will be supported in a later version.<br>
















































</span>
<h4><a class="mozTocH4" name="mozTocId172527"></a>Multiple Value Properties</h4>






















































Multiple Value Properties are configuration properties which require
multiple inputs such as <span style="font-style: italic;">wrapper.app.parameter.&lt;n&gt;</span><br>






















































In the tanukisoftware wrapper &lt;n&gt; must be a number and
numbers must be sequential.<br>






















































For YAJSW &lt;n&gt; may be a number or string. The numbers do
not have to be sequential. Nor do they need to be unique. <br>






















































Ordering is done by the natural alpha numeric ordering. In case of
parameters with the same key ordering is random.<br>






















































<br>






















































Example<br>






















































<table style="text-align: left; width: 1203px; height: 32px;" border="1" cellpadding="2" cellspacing="2">






















































  <tbody>






















































    <tr>






















































      <td style="background-color: rgb(204, 204, 204);">
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <div style="margin-left: 40px;">wrapper.java.classpath.1=a.jar<br>






















































wrapper.java.classpath.1=b.jar<br>






















































wrapper.java.classpath.5=c.jar<br>






















































wrapper.java.classpath.ab5=d.jar<br>






















































wrapper.java.classpath.ab12=e.jar<br>






















































      </div>






















































      <br>






















































will result in <br>






















































      <br>






















































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <div style="margin-left: 40px;">-classpath
b.jar;a.jar;c.jar;d.jar;e.jar</div>






















































      </td>






















































    </tr>






















































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </tbody>
</table>






















































<br>






















































<span style="font-weight: bold;"><br>

























</span>
<h4><a class="mozTocH4" name="mozTocId372157"></a><span style="font-weight: bold;">Classpath Wildcards</span></h4>

























<span style="font-weight: bold;"></span>YAJSW
supports wildcard for files as well as folders. However recursive wild
cards for folders are currently not supported, but may be supported in
future.<br>






















































For wildcard syntax refer to <a href="http://commons.apache.org/io/apidocs/org/apache/commons/io/filefilter/WildcardFileFilter.html">apache
commons</a>.<br>

























Note that in YAJSW wildcards are used differently then in java.<br>

























<br>
























In java lib/* denotes all .jar files in the lib folder. In YAJSW this
will denote all files in the lib folder. To denote the jar files in a
folder please use lib/*.jar<br>






















































      <br>






















































Examples:<br>

























<table style="text-align: left; width: 1208px; height: 32px; margin-left: 80px;" border="1" cellpadding="2" cellspacing="2">

























  <tbody>

























    <tr>

























      <td style="width: 100%; background-color: rgb(204, 204, 204);">
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <div style="margin-left: 40px;">




























wrapper.java.classpath.lib=lib/*/*.jar<br>






















































wrapper.java.classpath.test=*test*/*.jar</div>

























      </td>

























    </tr>

























  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </tbody>
</table>

























<span style="font-weight: bold;"><br>

























<br>

























NOTE:</span><br>






















































The colors in the sequel are defined as follows:<br>






















































<br>






















































<table style="text-align: left; width: 1131px; height: 32px;" border="1" cellpadding="2" cellspacing="2">






















































  <tbody>






















































    <tr>






















































      <td style="width: 10%; background-color: rgb(102, 255, 153);"></td>






















































      <td>Implemented</td>






















































    </tr>






















































    <tr>






















































      <td style="width: 10%;"></td>






















































      <td>Currently undecided</td>






















































    </tr>






















































    <tr>






















































      <td style="width: 10%; background-color: rgb(255, 255, 102);"></td>






















































      <td>Currently not Implemented. May be implemented in a
future release</td>






















































    </tr>






















































    <tr>






















































      <td style="width: 10%; background-color: rgb(255, 102, 102);"></td>






















































      <td>Currently not Implemented. Will probably NOT be
implemented in future.</td>






















































    </tr>






















































    <tr>






















































      <td style="width: 10%; background-color: rgb(255, 204, 255);"></td>






















































      <td>The meaning of the property has changed.</td>






















































    </tr>






















































    <tr>






















































      <td style="width: 10%; background-color: rgb(204, 255, 255);"></td>






















































      <td>Minor &nbsp;change to the meaning of the property.</td>






















































    </tr>






















































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </tbody>
</table>






















































<h4><a class="mozTocH4" name="mozTocId570283"></a>New&nbsp; Configuration Properties</h4>






















































<table style="text-align: left; width: 1138px; height: 117px;" border="1" cellpadding="2" cellspacing="2">






















































  <tbody>






















































    <tr>






















































      <td style="width: 10%;">
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <h5><a class="mozTocH5" name="mozTocId507836"></a>Property Name</h5>






















































      </td>






















































      <td>
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <h5><a class="mozTocH5" name="mozTocId505678"></a>Comments&nbsp;&nbsp;&nbsp;</h5>






















































      </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.java.app.jar</td>






















































      <td>In case a java application is started with the "-jar"
option, this property specifies the jar file to execute.<br>






















































      <br>






















































Example:<br>






















































wrapper.java.app.jar=wrapper.jar<br>






















































wrapper.app.parameter.1=-c<br>






















































wrapper.app.parameter.2=wrapper.conf<br>






















































      <br>






















































This will start the following java application : "java -jar wrapper.jar
-c wrapper.conf"</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.java.app.mainclass</td>






















































      <td>The main class of the application.<br>






















































&nbsp;This
is not required in the tanuki software wrapper. For compatibility it is
also not required when excuting bat/wrapper.bat which is compatible
with the original wrapper.exe. <br>






















































However passing the main class as
an application argument can be confusing. I find that it is better to
add the main class to the configuration properties.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.groovy</td>






















































      <td align="undefined" valign="undefined">Start
a groovy script as an application<br>






















































      <br>






















































Example:<br>






















































wrapper.groovy = myWebServices.gv</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.image</td>






















































      <td>Native image to start. <br>






















































      <br>






















































Example:<br>






















































wrapper.image=notepad.exe</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.stop.conf</td>






















































      <td align="undefined" valign="undefined">There
are applications such as tomcat which are stopped by invoking a
program instead of calling System.exit() from within the application.<br>






















































If
this property is defined and the specified conf file is found, the
application is stopped by calling the application defined by the given
configuration file.<br>






















































      <br>






















































If the application is not stopped within the given timeout it is killed.<br>






















































      <br>






















































Example:<br>






















































within the file tomcat.conf:<br>






















































&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
wrapper.stop=conf/tomcat.stop.conf<br>






















































&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
wrapper.app.parameter.1=start<br>






















































      <br>






















































the file tomcat.stop.conf:<br>






















































&nbsp; &nbsp; &nbsp; &nbsp; # overwrite the first
argument <br>






















































&nbsp; &nbsp; &nbsp; &nbsp; wrapper.app.parameter.1=stop<br>






















































&nbsp; &nbsp; &nbsp; &nbsp; # this is a stopper
configuration<br>






















































&nbsp; &nbsp; &nbsp; &nbsp; wrapper.stopper = true<br>






















































&nbsp; &nbsp; &nbsp; &nbsp; wrapper.console.title =
ShutdownTomcat<br>






















































&nbsp; &nbsp; &nbsp; &nbsp; # for all other properties:
use the same as for stating tomcat<br>






















































&nbsp; &nbsp; &nbsp; &nbsp; include = tomcat.conf <br>






















































      <br>






















































Default: no stopper configuration.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.stopper</td>






















































      <td align="undefined" valign="undefined">This
property indicates that this is a stopper application, for stopping
another application.<br>






















































It &nbsp;overrides properties as following and removes all
conditions, scripts, timers, ....<br>






















































      <br>






















































wrapper.exit_on_main_terminate = 0<br>






















































wrapper.on_exit.default = SHUTDOWN<br>






















































      <br>






















































Example<br>






















































&nbsp; &nbsp; &nbsp; wrapper.stopper = true<br>






















































      <br>






















































Default: false</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.exit_on_main_terminate</td>






















































      <td align="undefined" valign="undefined">There
are java applications, such as servers which run&nbsp;a separate thread.
The
application should continue running even if the main method terminated.<br>






















































Other applications should terminate with an exit code.<br>






















































      <br>






















































This property defines the exit code.<br>






















































If this property is &gt; -1, System.exit(exit code) will be called
once the main method terminates.<br>






















































      <br>






















































Example:<br>






















































&nbsp; &nbsp; wrapper.exit_on_main_terminate = -1<br>






















































      <br>






















































Default: -1, eg the application will not be terminated once
the main method terminates</td>






















































    </tr>






















































    <tr>





















































      <td style="background-color: rgb(102, 255, 153);">wrapper.exit_on_main_exception</td>





















































      <td align="undefined" valign="undefined">The main method may throw an exception. In this case the application can be terminated with an exit code or may continue to run.<br>





















































      <br>





















































This property defines the exit code in case of an exception.<br>





















































If this property is &gt; -1 System.exit(exit code) will be called if an exception is thrown.<br>





















































      <br>





















































Default: 999, eg the application will terminate with exit code 999 in case the main method throws an exception.</td>





















































    </tr>





















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.daemon.dir</td>






















































      <td>For Posix OS: Directory in which to create and execute
daemon init scripts.<br>






















































Default: <span style="font-style: italic;">/etc/init.d</span></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.daemon.pid.dir</td>






















































      <td>For&nbsp;Posix OS: Directory where to store the
wrapper.pidfile, if this
property is not defined, in case the application is started as a daemon.<br>






















































Default: <span style="font-style: italic;">/var/run</span></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.daemon.template</td>






















































      <td>For&nbsp;Posix OS: <a href="http://velocity.apache.org/">Velocity</a>
template file for generating daemon init scripts.<br>






















































Default: <span style="font-style: italic;">&lt;templates&gt;/daemon.vm</span></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.daemon.run_level_dir<br>




































- CHANGED since beta 10.3</td>






















































      <td>For&nbsp;Posix OS: Directory in which to create
K... and S... links<br>






















































Default: <span style="font-style: italic;">&lt;wrapper.daemon.dir&gt;/rcX.d<br>









































      <br>









































      </span>Example 1: For Ubuntu:<br>




































      <span style="font-style: italic;">




      <br>









































wrapper.daemon.run_</span><em style="font-style: italic;">level_</em><span style="font-style: italic;">dir=/etc/rcX.d</span><br style="font-style: italic;">









































      <br>









































will create the links <br>









































      <br>









































      <span style="font-style: italic;">/etc/rc5.d/K99&lt;wrapper.ntservice.name&gt;</span><br style="font-style: italic;">









































      <span style="font-style: italic;">/etc/rc5.d/S99&lt;wrapper.ntservice.name&gt;</span><br style="font-style: italic;">









































      <span style="font-style: italic;"><br>









































      </span>depending on the settings of the property <span style="font-style: italic;">wrapper.daemon.update_r</span>c<br>









































      </td>






















































    </tr>






















































    <tr>




































      <td style="background-color: rgb(153, 255, 153);">wrapper.daemon.update_rc</td>




































      <td align="undefined" valign="undefined">For Posix OS: This
property allows setting the runlevels and priorities for automatic
startup and stop of the daemon with a similar syntax as the <a href="http://www.debuntu.org/how-to-manage-services-with-update-rc.d">update_rc.d command</a>.<br>




































      <br>




































Per default creates links for stop runevels 0, 1, &nbsp;6 and start runlevels 2, 3, 4, 5&nbsp; with priority 20.<br>




































      <br>




































NOTE the difference: no closing "." is required.<br>




































      <br>




































      <span style="font-weight: bold;">NOTE: uninstall the daemon using &lt;yajsw&gt;/bat/uninstallDaemon.sh before changing the property </span><span style="font-style: italic; font-weight: bold;">wrapper.daemon.run_level_dir</span><span style="font-weight: bold;"> property &nbsp;or </span><span style="font-style: italic; font-weight: bold;">wrapper.daemon.update_rc</span><span style="font-weight: bold;">. </span>If
you change any of these properties while the daemon is installed or
running, you will not be able to start, stop, install or uninstall the
daemon.<br>




































      <br>




































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <ul>




































        <li>Custom Priorities:<span style="font-style: italic;"></span></li>




































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      </ul>




































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <div style="margin-left: 40px;"><span style="font-style: italic;">wrapper.update_rc = 91<br>




































      <br>




































      </span>will create links at default start and stop runlevels with priority 91<br>




































      </div>




































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <ul>




































        <li>Different Priorities for start and stop:</li>




































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      </ul>




































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <div style="margin-left: 40px;"><span style="font-style: italic;">wrapper.update_rc = 20 80<br>




































      <br>




































      </span>will create links at default start and stop runlevels with start priority 20 and stop priority 80</div>




































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <ul>




































        <li>Custom Run Levels:</li>




































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      </ul>




































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <div style="margin-left: 40px;"><span style="font-style: italic;">wrapper.update_rc =&nbsp;start 20 2 3 4 . start 30 5 . stop 80 0 1 6</span><br>




































      <br>




































will create start links at runlevels 2, 3, 4 with priority 20 at
runlevel 5 with priority 30 and stop links at runlevels 0, 1, 6 with
priority 80<br>




































      <br>




































In this case the following links are created:<br>




































      <br>




































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <div style="margin-left: 40px;">/etc/init.d/rc0.d/K80&lt;service name&gt;<br>





































   /etc/init.d/rc1.d/K80&lt;service name&gt;<br>





































   /etc/init.d/rc6.d/K80&lt;service name&gt;<br>





































   /etc/init.d/rc2.d/S20&lt;service name&gt;<br>





































   /etc/init.d/rc3.d/S20&lt;service name&gt;<br>





































   /etc/init.d/rc4.d/S20&lt;service name&gt;<br>





































   /etc/init.d/rc5.d/S30&lt;service name&gt;<br>




































      </div>




































 </div>




































      </td>




































    </tr>




































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.daemon.k_order.&lt;n&gt;<br>




































-DEPRECATED since beta-10.3</td>






















































      <td>For Posix OS: the link K...&nbsp; will be named:
K&lt;wrapper.daemon.k_order&gt;&lt;wrapper.ntservice.name&gt;<br>






















































Default: 99</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.daemon.s_order.&lt;n&gt;<br>




































- DEPRECATED since beta-10.3</td>






















































      <td>For Posix OS: the link S...&nbsp; will be named:
S&lt;wrapper.daemon.s_order&gt;&lt;wrapper.ntservice.name&gt;<br>






















































Default: 99</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.launchd.dir</td>






















































      <td align="undefined" valign="undefined">For
Mac OS X daemons: Directory into which the <a href="http://en.wikipedia.org/wiki/Launchd">launchd</a>
plist file is copied.<br>






















































Default:<span style="font-style: italic;">
~Library/LaunchAgents</span></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.plist.template</td>






















































      <td align="undefined" valign="undefined">For
Mac OS X daemons: <a href="http://velocity.apache.org/">Velocity</a>&nbsp;template
file for generating a <a href="http://developer.apple.com/DOCUMENTATION/DARWIN/Reference/ManPages/man5/launchd.plist.5.html">launchd
plist file</a>.<br>






















































Default: <span style="font-style: italic;">&lt;templates&gt;/launchd.plist.vm</span></td>






















































    </tr>






















































    <tr>
































      <td style="background-color: rgb(153, 255, 153);">wrapper.ntservice.autoreport.startup</td>
































      <td align="undefined" valign="undefined">For Windows: if set to <span style="font-style: italic;">false</span> the wrapper will not report service startup to the windows service manager. Instead the application is expected to call <span style="font-style: italic;">WrapperJVMMain.WRAPPER_MANAGER.reportServiceStartup()</span>
thus notifying the windows service manager, that the application is up
and ready. The wrapper will report to the windows service manager as
startup time the value of the property <span style="font-style: italic;">wrapper.startup.timeout</span>. If the application does not invoke <span style="font-style: italic;">reportServiceStartup()</span> within this timeout the service is stopped.<br>
































      <br>
































Default: <span style="font-style: italic;">true</span>. In this case the wrapper will not wait for the application to startup and will notify the service manager as soon as possible.</td>
































    </tr>
































    <tr>






















































      <td>wrapper.exit_code.stop</td>






















































      <td>Exit Code of the application when the stop method has
been succesfully invoked.<br>






















































Default: 0</td>






















































    </tr>






















































    <tr>






















































      <td>wrapper.exit_code.kill</td>






















































      <td>Exit
Code of the application when the stop method did not stop the
application within the given timeout and the application termination is
forced.<br>






















































Default: 999</td>






















































    </tr>






















































    <tr>






















































      <td>wrapper.exit_code.fatal</td>






















































      <td>Exit Code of the application in case it could not be
started or in case the main method terminated with an Exception<br>






















































Default: 999</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(153, 255, 153);">wrapper.control</td>






















































      <td>Depending on the application there are use cases where the
wrapper process should continue to run, even when the application is
stopped and vice versa. For example in case the wrapper continues to
run independently of the application the application may be restarted
by jmx. In other cases such as important server applications you may
want to shutdown the wrapper due to an update of a script without
stopping the application. The wrapper may then be restarted so that it
"reconnects" to the application.<br>





















































      <br>





















































With the this property one may control how the wrapper and the application are coupeled.<br>





















































      <br>





















































LOOSE : &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
&nbsp;application will continue running when the controller is
terminated and vice versa<br>





















































TIGHT: &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
&nbsp; &nbsp;application will terminate if controller terminates and
vice
versa<br>






















































WRAPPER:
&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; controller will be terminated if the application terminates.
Application will not be terminated if the controller is terminated<br>






















































APPLICATION:
&nbsp; &nbsp; &nbsp; &nbsp;application will be terminated if the contrloler terminates. Controller
will not be terminated if the application is terminated<br>






















































      <br>






















































In
case a timer or condition is active, the controller is not stopped. In
this cases you may consider calling System.exit() from within a
condition script.<br>






















































      <br>






















































Default: TIGHT </td>






















































    </tr>






















































    <tr>





















































      <td style="background-color: rgb(153, 255, 153);">wrapper.windows.cluster.script<br>





















































wrapper.windows.cluster.script.args<br>











































wrapper.windows.cluster.groups</td>





















































      <td>Name of a groovy script which is called whenever the owner of the group of the cluster changes.<br>





















































By calling <span style="font-style: italic;">System.getenv("COMPUTERNAME").equals(process.cluster.activeNode)</span> within the script one may check if the current node is the active node.<br>











































      <br>











































In case the script should be invoked only for specific groups one may add the list of groups to the property <span style="font-style: italic;">wrapper.windows.cluster.groups</span><br style="font-style: italic;">





















































      <br>





















































NOTE: do not define this property if the wrapper is not running on a cluster, as this will crash the JVM.<br>





















































NOTE: this property is only valid for windows OS and requires ClusApi.dll.<br>





















































NOTE: the first call to WrappedProcess.start() will not start the
application, but will execute the script. One may thus make sure that
the application is started only on the active node. </td>





















































    </tr>





















































  
  <tr>



















































      <td style="background-color: rgb(153, 255, 153);">wrapper.codebase</td>



















































      <td>Root folder for classpath files and for resource files.<br>



















































Supports file systems supported by <a href="http://commons.apache.org/vfs/filesystems.html">commons VFS</a><br>



















































      <br>



















































Examples:<br>



















































wrapper.codebase =webdav://somehost:8080/dis<code>t<br>


















































      </code>
wrapper.codebase =c:/dist<code></code><br>


















































      </td>



















































    </tr>



















































    <tr>



















































      <td style="background-color: rgb(153, 255, 153);">wrapper.cache</td>



















































      <td>Cache directory to which files are copied.<br>


















































      <br>


















































Example:<br>


















































wrapper.cache=c:/yajsw_cache</td>



















































    </tr>



















































    <tr>



















































      <td style="background-color: rgb(153, 255, 153);">

wrapper.cache.local</td>



















































      <td>Indicates if local files are copied to wrapper.cache<br>


















































      <br>


















































Default: false<br>


















































      <br>


















































Example:<br>


















































wrapper.cache.local = true</td>



















































    </tr>



















































    <tr>



















































      <td style="background-color: rgb(153, 255, 153);">wrapper.resource.&lt;n&gt;</td>



















































      <td>Defines files which should be copied to the cache:<br>


















































      <br>


















































Examples:<br>


















































      <br>


















































wrapper.resource.1=images/image1.jpg<br>


















































wrapper.resource.2=conf/test.properties</td>



















































    </tr>



















































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <tr>

































      <td style="background-color: rgb(153, 255, 153);">wrapper.app.env.&lt;variable&gt;</td>

































      <td>Defines an environment variable which is set when executing the application.<br>

































      <br>

































Example:<br>

































      <br>

































wrapper.app.env.path = /path-to-some-dll;${path}</td>

































    </tr>

































  
  
  
  
  
  <tr>



























      <td style="background-color: rgb(153, 255, 153);">wrapper.fork_hack</td>



























      <td>On Linux/OSX/Unix calling fork/exec from within java/jna <a href="http://bugs.sun.com/bugdatabase/view_bug.do?bug_id=4388188">may not allways work</a> due to multithreading.<br>



























Activating the "fork hack" will start the sub-process by using standard java Runtime.exec instead.<br>



























Set this property to true, if you are having issues starting your
application. Before that make sure that your issues are not caused by
wrong configuration.<br>



























      <br>



























Default: false<br>



























      <br>



























Example:<br>



























      <br>



























wrapper.fork_hack = true</td>



























    </tr>



























    <tr>



























      <td style="background-color: rgb(153, 255, 153);">wrapper.save_interpolated</td>



























      <td>Per default, when installing a service, the yajsw "saves"
the value of interpolations, eg configuration values accessed using
${xxx}. It thus makes sure that the service is started with the same
settings as when it was installed. There are cases where this is not
desired and interpolations should be reevaluated at service start. For
this usecase set this property to false<br>


























      <br>


























Default: true<br>


























      <br>


























Example:<br>


























      <br>


























wrapper.save_interpolated = false</td>



























    </tr>



























  
  
  <tr>
























      <td style="background-color: rgb(153, 255, 153);"><span style="font-style: italic;">wrapper.ntservice.failure_actions.reset_period<br>
























wrapper.ntservice.failure_actions.reboot_msg</span><br>
























      <span style="font-style: italic;">wrapper.ntservice.failure_actions.command</span><br>
























      <span style="font-style: italic;">wrapper.ntservice.failure_actions.action</span>s<br>
























      <span style="font-style: italic;">wrapper.ntservice.failure_actions.actions_delay</span></td>
























      <td>Properties for setting windows service recovery. <br>
























For more details refer to the <a href="http://msdn.microsoft.com/en-us/library/windows/desktop/ms685939%28v=vs.85%29.aspx">windows documentation</a><br>
























These properties do not refer to the wrapped application but to the wrapper process.<br>
























      <br>
























NOTE: When setting this property you will have to consider the setting of the <span style="font-weight: bold;">wrapper.control</span>
property. If this property is not set to TIGHT restarting of the
service (wrapper process) may result in multiple application processes
running.<br>
























      <br>
























      <span style="font-weight: bold;">actions</span> is a comma separated list which can take the following string values:<br>
























      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <ul>
























        <li>NONE</li>
























        <li>REBOOT</li>
























        <li>RESTART</li>
























        <li>COMMAND</li>
























      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      </ul>
























default: no actions<br>
























      <span style="font-weight: bold;">reset_period</span>: default is 0, set to -1 for INFINITE<br>
























      <br>
























      <span style="font-weight: bold;">actions_delay</span>: is a
comma separated list of delays in milliseconds. Default is 0. If the
list is shorter than the actions list, the last delay in the list is
used for the rest of the actions.<br>
























      <br>
























Example:<br>
























      <br>
























      <span style="font-style: italic;">wrapper.ntservice.failure_actions.action</span>s = RESTART<br>























      <span style="font-style: italic;">wrapper.ntservice.failure_actions.actions_delay</span>=60000<br>























      <br>























This will restart the wrapper every time it crashes with a delay of 1 minute.<br>























      <br>























      <span style="font-style: italic;">wrapper.ntservice.failure_actions.action</span>s = RESTART, NONE<br>























      <span style="font-style: italic;">wrapper.ntservice.failure_actions.actions_delay</span>=60000<br>























      <br>























This will restart the wrapper only once.<br>























      <br>























NOTE: The winodws services gui cannot display all settings of these
properties. To check that failure actions have been set you may check
the registry HKEY_LOCAL_MACHINE\SYSTEM\Services\&lt;service
name&gt;\FailureActions.<br>























      <br>























NOTE: setting REBOOT requires the according user rights.<br>























      <br>
























NOTE: similar functionality can be implemented using groovy scripts resulting in platform independent configuration.<br>
























      </td>
























    </tr>
























  
  
  
  
  
  
  <tr>

















      <td style="background-color: rgb(153, 255, 153);">wrapper.script.reload</td>

















      <td>Indicates to the wrapper to check if scripts' files' last
changed time should be checked before each execution. If the file has
changed it will be reloaded.<br>

















      <br>

















Default: false</td>

















    </tr>

















    <tr>

















      <td style="background-color: rgb(153, 255, 153);"><span style="font-style: italic;">wrapper.script.encoding<br>

















wrapper.log.encoding<br>

















wrapper.conf.encoding</span></td>

















      <td>Set the encoding of &nbsp;files.<br>

















      <br>

















Default: the java default encoding .<br>

















      <br>

















Example:<br>

















      <br>

















      <span style="font-style: italic;">wrapper.log.encoding </span>= UTF-8</td>

















    </tr>

















    <tr>

















      <td style="background-color: rgb(153, 255, 153);"><span style="font-style: italic;">wrapper.console.pipestreams</span></td>

















      <td>Per default stdout, stderr and stdin are piped to null
whereas the java System.in, System.out and System.err are T-ed to the
wrapper.<br>

















This has the advantage, that the wrapper and the application can run
independently (currently only on windows) . However errors from the
java launcher errors are not logged in the wrapper.<br>

















Setting this property to true will stream the std streams to the wrapper instead of T-ing. <br>

















      <br>

















Default: java applications: false, native applications: true<br>

















      <br>

















Example:<br>

















      <br>

















      <span style="font-style: italic;">wrapper.console.pipestreams = </span>true</td>

















    </tr>

















  
  
  <tr>














      <td style="background-color: rgb(153, 255, 153); font-style: italic;"> wrapper.config.script.&lt;n&gt;</td>














      <td>The configuration can be extended to include user defined
groovy scripts. &lt;n&gt; is the binding which is used to invoke the
script within the configuration.<br>














      <br>














Example:<br>














      <br>














 wrapper.config.script.decryptor = scripts/decrypt.gv<br>














      <br>














invocation:<br>














      <br>














wrapper.ntservice.password =${decryptor.invoke('decrypt'\, 'aplasdfjlk')}</td>














    </tr>














  
  <tr>












      <td style="background-color: rgb(153, 255, 153); font-style: italic;"> <span style="font-style: italic;">wrapper.cleanup_tmp</span></td>












      <td>Per default at startup (WrappedProcess.init(),
startInternal()) the wrapper will delete all err_, in_, out_ files from
the tmp folder.<br>












To disable, set this boolean property to false.<br>












      <br>












Default : true<br>












      <br>












Example:<br>












      <br>












 wrapper.cleanup_tmp = false</td>












    </tr>












  <tr>











      <td style="background-color: rgb(153, 255, 153); font-style: italic;">wrapper.tray.spawn_process</td>











      <td>Per default runConsole will spawn the system tray icon process, if wrapper.tray is set.<br>











To disable, set this property to false.<br>











      <br>











Default: true<br>











      <br>











Example:<br>











      <br>











wrapper.tray.spawn_process = false</td>











    </tr>











  <tr>










      <td style="background-color: rgb(153, 255, 153); font-style: italic;"> <span style="font-style: italic;">wrapper.logfile.maxdays</span>&nbsp;</td>










      <td>maximal number of logfile days to keep if wrapper.logfile.rollmode
is set to DATE. wrapper.logfile.maxfiles is the maximal number of files
&nbsp;to keep per day. Thus a burst in logging, for example due to a
bug, will not affect previous days.<br>










      <br>










Default: -1, eg keep all days.</td>










    </tr>










  
  <tr>








      <td style="background-color: rgb(153, 255, 153); font-style: italic;">wrapper.ntservice.reduce_signals</td>








      <td>In case the application is using&nbsp;Signal.handle(new Signal(..)) the wrapped service should not set the java option -Xrs.<br>








In this case you should set this property to false.<br>








      <br>








Default: true<br>








      <br>








Example:<br>








      <br>








wrapper.ntservice.reduce_signals = true </td>








    </tr>








  <tr>







      <td style="background-color: rgb(153, 255, 153); font-style: italic;">wrapper.console.use_interpolated</td>







      <td>In case java options are used by the wrapper, these are per
default, if they are used in the configuration then they are added to
the java options of the wrapped application.<br>







In cases, where this is not desired, set this property to false.<br>







      <br>







Default: false<br>







      <br>







Example:<br>







      <br>







wrapper.console.use_interpolated = false</td>







    </tr>







  <tr>






      <td style="background-color: rgb(153, 255, 153); font-style: italic;">wrapper.app.status.log.lines</td>






      <td>Maximal number of lines read from the wrapped application which are logged by the wrapper in case the log level is set to STATUS<br>






      <br>






Default: 100<br>






      <br>






Example:<br>






      <br>






wrapper.app.status.log.lines = 40</td>






    </tr>






  <tr>





      <td style="background-color: rgb(153, 255, 153); font-style: italic;"><span style="font-style: italic;">wrapper....script.&lt;n&gt;.maxConcInvoc</span></td>





      <td>Filter triggers execute scripts in a separate thread. In
order to avoid overusage of threads when a filter trigger is executing
a
script a mximal number of concurrent executions of the script is set.
If more than maxConcInvoc scripts are invoked while a script is&nbsp;
executing subsequent invocations are ignored. <br>





      <br>





Default: 5</td>





    </tr>





    <tr>





      <td style="background-color: rgb(153, 255, 153); font-style: italic;"><span style="font-style: italic;">wrapper.debug.level</span></td>





      <td>Control how talkative yajsw logfile is.<br>





      <br>





0 : only pid of application process, and stop of application<br>





1 : debug information on applicaiton start/stop<br>





2 : some more information<br>





3: &nbsp;full debug information<br>





      <br>





Note: check the source for details. If you think that another assignment of messages to levels makes more sense pls post.<br>





      <br>





This property is read only if <span style="font-weight: bold;">wrapper.debug</span> is set to true<br>





      <br>





Default: 3<br>





      <br>





Example:<br>





      <br>





wrapper.debug = true<br>





wrapper.debug.level = 1<br>





&nbsp; </td>





    </tr>





    <tr>





      <td style="background-color: rgb(153, 255, 153); font-style: italic;">wrapper.filter.debug.default</td>





      <td>To avoid major changes to existing installations some users require a property to change the default settings of the property <span style="font-style: italic;">wrapper.filter.debug</span>. check source for more details.</td>





    </tr>





    <tr>





      <td style="background-color: rgb(153, 255, 153); font-style: italic;"><span style="font-style: italic;">wrapper.wrapperJar<br>





wrapper.appJar</span></td>





      <td>When embedding yajsw in another application one may want to
be able to update the jars used by the application and by the wrapper
separately. These properties are set if the wrapped applicaiton should
use a different jar file. They point to the full path of the jar file.</td>





    </tr>





  <tr>




      <td style="background-color: rgb(153, 255, 153); font-style: italic;">wrapper.logfile.desc</td>




      <td>Per default, when log files are rotated all log files have to
be renamed: The most recent log file has the extension 1. This is not
desirable in cases where the log files are synchronized to the cloud,
because with each rotation all files have to be synchronized.<br>




      <br>




Setting this property to true will rename only the newest file. Thus
the most recent log file (except the current) will have the highest
extension whereas the oldest one will have the lowest.<br>




      <br>




Default: false<br>




      <br>




Example:<br>




      <br>




wrapper.logfile.desc = true<br>




      <br>




will result in the following log files sorted by last modified:<br>




      <br>




wrapper.log<br>




wrapper.log.3<br>




wrapper.log.2<br>




wrapper.log.1<br>




      <br>




wrapper.logfile.desc = false<br>




      <br>




will result in the following log files sorted by last modified:<br>




      <br>




wrapper.log<br>




wrapper.log.1<br>




wrapper.log.2<br>




wrapper.log.3<br>




      </td>




    </tr>




  
  <tr>


      <td style="background-color: rgb(153, 255, 153); font-style: italic;">wrapper.posix_spawn</td>


      <td>Spawning a child process using fork/exec has resulted in some issues, when invoked from java.<br>


As result of this the property wrapper.fork_hack was introduced, which is however also not perfect.<br>


      <br>


The best option seems to be <a href="http://pubs.opengroup.org/onlinepubs/009695399/functions/posix_spawn.html">posix</a><a href="http://pubs.opengroup.org/onlinepubs/009695399/functions/posix_spawn.html">_spawn</a>.<br>


      <br>


Default: false (for backwards compatibility)<br>


      <br>


      <span style="font-weight: bold;">Please test this option and post your feedback.</span><br style="font-weight: bold;">


      <br style="font-weight: bold;">


      <span style="font-weight: bold;">In future this option will be set to true by default and will be used per default on all posix systems.</span><br>


      <br>


      </td>


    </tr>


    <tr>


      <td style="background-color: rgb(153, 255, 153); font-style: italic;">wrapper.posix_vfork</td>


      <td>This property is used only if wrapper.posix_spawn is true.<br>


      <br>


If set to true, posix_spawn is invoked with the attribute property vfork.<br>


For some more information see <a href="http://stackoverflow.com/questions/2731531/faster-forking-of-large-processes-on-linux">here</a>.<br>


      <br>


Default: false</td>


    </tr>


  
  
  </tbody>
</table>






















































<h4><a class="mozTocH4" name="mozTocId579660"></a>New Optional Configuration Properties</h4>






















































<table style="text-align: left; width: 1148px; height: 201px;" border="1" cellpadding="2" cellspacing="2">






















































  <tbody>






















































    <tr>






















































      <td>
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <h5><a class="mozTocH5" name="mozTocId468977"></a>Property Name</h5>






















































      </td>






















































      <td>
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <h5><a class="mozTocH5" name="mozTocId916414"></a>Comments&nbsp;&nbsp;&nbsp;</h5>






















































      </td>






















































    </tr>






















































    <tr>
      <td style="background-color: rgb(51, 255, 51);">wrapper.ping.check_ack</td>
      <td align="undefined" valign="undefined">There are cases where
the tcp/ip communication is interrupted by the client socket is not
closed by the OS. The application in the case sends a ping without a
tcp/ip error. In this case, check that the application receives an
application level ack when a ping is transmitted.<br>
      <br>
Default: false<br>
      <br>
Example:<br>
wrapper.ping.check_ack=true</td>
    </tr>
    <tr>
      <td style="background-color: rgb(51, 255, 51);">wrapper.delay_shutdown</td>
      <td align="undefined" valign="undefined">On shutdown of the
wrapper we may need to delay the wrapper termination for some script to
terminate. This property indicates how many seconds the termination of
the process will be delayed.<br>
Note: If this is longer than the value set on OS level for shutting
down a service&nbsp; the OS will terminate the wrapper and log an error.<br>
      <br>
Default: 0<br>
      <br>
Example:<br>
wrapper.delay_shutdown=40</td>
    </tr>
    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.tmp.path</td>






















































      <td>path for storing temporary files. Default is /tmp</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.console.visible</td>






















































      <td>Boolean (true, false) indicating if the console of the
application should be visible.<br>






















































Default: false</td>






















































    </tr>






















































    <tr>






















































      <td>wrapper.id</td>






















































      <td>|</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.script.&lt;n&gt;.&lt;state&gt;<br>






















































wrapper.script.&lt;n&gt;.&lt;state&gt;.args<br>






















































wrapper.script.&lt;n&gt;.&lt;state&gt;.timeout</td>






















































      <td>The file name of the script to be executed when the
processing state is changed to the given state. <br>






















































&lt;state&gt; :: START | RUN | RESTART | <span style="font-style: italic;"></span>STOP | ABORT |
SHUTDOWN | IDLE<br>






















































      <br>






















































State changes are as follows:<br>






















































      <br>






















































IDLE -&gt; START -&gt; ABORT -&gt; IDLE<br>






















































&nbsp;
&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
&nbsp; &nbsp; &nbsp; &nbsp;
&nbsp; &nbsp; &nbsp; -&gt; RUN -&gt; ABORT
-&gt; IDLE<br>






















































&nbsp;
&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
&nbsp; &nbsp; &nbsp; &nbsp;
&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
&nbsp; &nbsp; &nbsp; &nbsp;
-&gt; STOP -&gt; IDLE<br>






















































&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
&nbsp; &nbsp; &nbsp; &nbsp;
&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; -&gt;
RESTART -&gt; RUN<br>






















































      <br>






















































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <table style="text-align: left; width: 902px; height: 172px;" border="1" cellpadding="2" cellspacing="2">






















































        <tbody>






















































          <tr>






















































            <td align="undefined" valign="undefined">IDLE</td>






















































            <td align="undefined" valign="undefined">
initial state</td>






















































          </tr>






















































          <tr>






















































            <td align="undefined" valign="undefined">START</td>






















































            <td align="undefined" valign="undefined">WrappedProcess.start()
executed</td>






















































          </tr>






















































          <tr>






















































            <td align="undefined" valign="undefined">RUN</td>






















































            <td align="undefined" valign="undefined">Application
successfully launched</td>






















































          </tr>






















































          <tr>






















































            <td align="undefined" valign="undefined">ABORT</td>






















































            <td align="undefined" valign="undefined">unintentional
stop of the application:<br>






















































            <br>






















































application crashed and will not be restarted<br>






















































missing application
ping<br>






















































application did not start<br>






















































application did not log on<br>






















































max restarts</td>






















































          </tr>






















































          <tr>






















































            <td align="undefined" valign="undefined">STOP</td>






















































            <td align="undefined" valign="undefined">Intentional
stop of the application:<br>






















































            <br>






















































WrappedProcess.stop()<br>






















































WrapperManager.stop()<br>






















































WrapperExe terminated<br>






















































WrappedService.stop()</td>






















































          </tr>






















































          <tr>






















































            <td align="undefined" valign="undefined">RESTART</td>






















































            <td align="undefined" valign="undefined">Intentional
or unintentional restart of the application<br>






















































            <br>






















































WrappedProcess.restart()<br>






















































WrapperManger.restart()
            <br>






















































wrapper.on_exit.&lt;n&gt;=RESTART<br>






















































wrapper.filter.action.&lt;n&gt;=RESTART</td>






















































          </tr>






















































          <tr>






















































            <td align="undefined" valign="undefined">SHUTDOWN</td>






















































            <td align="undefined" valign="undefined">WrapperExe
terminates<br>






















































WrappedService terminates<br>






















































            <br>






















































NOTE:
in case of shutdown we give the script 5 sec to execute before
Runtime.halt() is called. This should be sufficient to send a last mail
or snmp trap.</td>






















































          </tr>






















































        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        </tbody>
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      </table>






















































      <br>






















































      <br>






















































The following parameters are passed to the script:<br>






















































&lt;id&gt; &lt;state&gt; &lt;count&gt;
&lt;pid&gt; &lt;exit-code&gt;&lt;line&gt;<br>






















































where
&lt;id&gt; is set to &lt;state&gt;,
&lt;count&gt; is the current number
of restarts, &lt;pid&gt; is the last available process id and
exit code
is the last available exit code<br>






















































Example:<br>






















































wrapper.script.ABORT=scripts/sendMail.gv<br>






















































wrapper.script.ABORT.args=<EMAIL>,"process aborted","process
aborted"<br>



































      <br>



































timeout is the maximal time, in seconds,&nbsp; the wrapper waits for the script to terminate. Default is 30 seconds.<br>



































      <br>



































If the script does not terminate within the given duration, the wrapper
will try to interrupt it and will continue its operation. Note that
there is no guaranty that the script will be interupted.<br>



































      <br>



































Note: state change scripts are not excuted in a separate thread. They may therefore delay the wrapper execution.<br>













Note: for backward compatibility: wrapper.script.&lt;state&gt; is
equivalent to setting &lt;n&gt; to "". For the same state execution
order is the alpha numeric order of &lt;n&gt;








































      </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(153, 255, 153);">wrapper.filter.script.&lt;n&gt;<br>






















































wrapper.filter.script.&lt;n&gt;.args<br>






















































wrapper.filter.script.&lt;n&gt;.timeout</td>






















































      <td>The file name of the script to be executed when the
filter is triggered. <br>






















































The following parameters are passed to the script:<br>






















































&lt;id&gt; &lt;state&gt; &lt;count&gt;
&lt;pid&gt; &lt;exit-code&gt; &lt;line&gt;<br>






















































where &lt;state&gt; is "RUNNING", &lt;id&gt; is set to
&lt;n&gt;, &lt;count&gt; is the current
number of restarts, &lt;pid&gt; is the last available process
id and
exit code is the last available exit code<br>






















































      <br>






















































Currently shell scripts (assuming ending with .bat or .sh) and groovy
scripts (ending with .gv or .groovy) are supported.<br>






















































Within groovy the following bindings are available: id, state, count,
pid, exitCode, line, wrappedProcess<br>






















































      <br>






















































args is a list of "," separated string arguments which are passed to
the script<br>






















































      <br>






















































Example:<br>






















































wrapper.filter.trigger.exception=Exception<br>






















































wrapper.filter.script.exception=sendmail.gv<br>






















































wrapper.filter.script.exception.args=<EMAIL>,"process
restart","process threw an exection $process.triggerLine -&gt;
restarting"<br>






















































wrapper.filter.action.exception=RESTART<br>



































      <br>



































If the script does not terminate within the given duration, the wrapper
will try to interrupt it and will continue its operation. Note that
there is no guaranty that the script will be interupted.<br>



































      <br>



































Note that filter scripts are executed in a separate thread, so us not
to delay the processing reading from the application console.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(153, 255, 153);">wrapper.filter.trigger-regex.&lt;n&gt;</td>






















































      <td>Similar to "wrapper.filter.trigger.&lt;n&gt;"
except that the trigger used is a <a href="http://www.karneim.com/jrexx/project01/syntax/jrx_syntax.htm">regular
expression.</a></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.timer.cron.&lt;cmd&gt;</td>






















































      <td>&lt;cmd&gt; :: START | STOP | RESTART<br>






















































      <br>






















































Timer command for controlling process execution with a <a href="http://www.opensymphony.com/quartz/api/org/quartz/CronExpression.html">cron
expression</a>.<br>






















































RESTART:&nbsp; Restarts the process at the trigger time.
If the process is running it is stopped and then restarted. Otherwise
the process is started.<br>






















































START:
Starts the process if it is not running at the trigger time. If the
wrapper.timer.cron.START property is missing the process is started
immediatly.<br>






















































STOP: Stops the process if it is running at the trigger time.<br>






















































      <br>






















































Example:<br>






















































wrapper.timer.cron.RESTART=* * 0/12 * * ?<br>






















































      <br>






















































Starts the process immediatly and restart the process daily at 00:00
and 12:00 h.<br>






















































      <br>






















































Note that WrapperManger.stop() and WrappedJavaProcess.stop() will stop
the application but will not stop the timer.<br>






















































To stop the timer use WrapperManger.stopTimer()
or&nbsp;WrappedJavaProcess.stopTimer()<br>






















































      <br>






















































Note
that this is just a "dumb time trigger". It does not consider if the
application is startable or if it has been restarted by a filter or by
the user.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.timer.simple.&lt;cmd&gt;.&lt;prop&gt;</td>






















































      <td>&lt;cmd&gt; :: START | STOP | RESTART<br>






















































&lt;prop&gt; : FIRST | COUNT | INTERVAL<br>






















































      <br>






















































&lt;cmd&gt; has the same meaning as for the "cron" property.<br>






















































FIRST: time of the first execution of the command. Time format:
"dd.mm.yyyy hh:mm:ss" or "hh:mm:ss". Default: immediatly<br>






















































COUNT: the number of repetitions of the command. Default: INDEFINITELY<br>






















































INTERVAL: The number of&nbsp;seconds to pause between the repeat
firing.<br>






















































      <br>






















































Example:<br>






















































wrapper.timer.simple.RESTART.FIRST=13:30:00<br>






















































wrapper.timer.simple.RESTART.COUNT=5<br>






















































wrapper.timer.simple.RESTART.INTERVAL=300<br>






















































      <br>






















































Starts the process at 13:30&nbsp; then restarts it 5 times at
intervals of 5 minutes.<br>






















































      <br>






















































Note that WrapperManger.stop() and WrappedJavaProcess.stop() will stop
the application but will not stop the timer.<br>






















































To stop the timer use WrapperManger.stopTimer()
or&nbsp;WrappedJavaProcess.stopTimer()<br>






















































      <br>






















































Note that this is just a "dumb time trigger". It does not consider if
the application is startable or if it has been restarted by a filter or
by the user.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.priority</td>






















































      <td>priority of the process. The following values are
supported: LOW, BELOW_NORMAL, NORMAL, ABOVE_NORMAL, HIGH<br>






















































Default: NORMAL<br>






















































      <br>






















































Example:<br>






















































wrapper.priority=BELOW_NORMAL</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.affinity</td>






















































      <td>CPU affinity of the process. this is a bit-array
representing the cpus.<br>






















































In case this value is not consistent with the number of processors it
is not set.<br>






















































      <br>






















































Example:<br>






















































wrapper.affinity=1 &nbsp;means CPU 0<br>






















































wrapper.affinity=2 &nbsp;means CPU 1<br>






















































wrapper.affinity=3 means CPU 0 and 1</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.java.customProcName</td>






















































      <td>To easily find applications in the task list, the java exe may be copied to a file with a different name.<br>






















      <br>






















This property indicates to yajsw to copy java.exe or javaw.exe to a
file with a given name.
If the property (after interpolation) specifies a full path to a file
with existing parent path, the given path is used and the file is not
marked for deletion on exit. Otherwise a temporary file (call to
File.createTempFile())&nbsp; &nbsp;named <span style="font-style: italic;">java_&lt;customProcName&gt;_nnnn.exe</span> is created.<br>






















      <span style="font-style: italic;">































      <br>






















































      </span>NOTE: currently only available for windows</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.java.command.minVersion</td>






















































      <td>Alternatively
to giving the path to the java.exe in the wrapper.java property, YAJSW
may search for a given JVM on the computer. The properties in the
sequel will indicate which jvm to use.<br>






















































      <br>






















































Minmal required version of the jvm. <br>






















































Format: <span class="codeword">x.x.x[_xx]</span><br>






















































      <br>






















































Example:<br>






















































wrapper.java.minVersion=1.5.0<br>






















































      <br>






















































NOTE: currently only available for windows</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.java.command.maxVersion</td>






















































      <td>Maximal required version of the jvm. If not defined the
maximal JVM version found will be used.<br>






















































      <br>






















































Example:<br>






















































wrapper.java.minVersion=1.4.0<br>






















































      <br>






















































NOTE: currently only available for windows</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.java.command.64bit</td>






















































      <td>Indicates if to use a 64 bit jvm<br>






















































Default: false</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.java.command.jreOnly</td>






















































      <td>Indicates to search only for JREs<br>






















































      <br>






















































Example<br>






















































wrapper.java.jreOnly=true<br>






















































      <br>






















































NOTE: currently only available for windows</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.java.command.preferJre</td>






















































      <td>Indicates that JRE are prefered over JDK<br>






















































      <br>






















































NOTE: currently only available for windows<br>






















































      </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.java.command.preferJdk</td>






















































      <td>Indicates that JDK are prefered over JRE, per default
JDKs are prefered.<br>






















































      <br>






















































NOTE: currently only available for windows<br>






















































      </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.java.command.jdkOnly</td>






















































      <td>Indicates to search only for JDKs<br>






















































      <br>






















































NOTE: currently only available for windows<br>






















































      </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.java.command.javaw</td>






















































      <td>Use javaw.exe instead of java.exe<br>






















































      <br>






















































NOTE: currently only available for windows</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.jmx</td>






















































      <td>If true registers the WrappedProcessMBean to the
MBeanServer of the wrapper. <br>














































If no MBeanServer has been initialized by the
wrapper, eg wrapper was not started with , the wrapper will create one and an RMI acceptor is
initialized. Therefore the property -Dcom.sun.management.jmxremote is
not required.<br>






















































Default: false<br>














































      </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.java.jmx</td>






















































      <td>If true registers WrapperManagerMBean to the
MBeanServer of the java application. <br>






















































This property is inactive if no MBeanServer has been initialized by the
application.<br>






















































Default: false<br>






















































NOTE:
to initialize an MBeanServer use the JVM option
-Dcom.sun.management.jmxremote or check the documentation of your
application server.</td>






















































    </tr>






















































    <tr>














































      <td style="background-color: rgb(153, 255, 153);" align="undefined" valign="undefined">wrapper.jmx.rmi.port</td>














































      <td align="undefined" valign="undefined">Port for RMI access to the JMX server of the wrapper process.<br>














































      <br>














































Default: 1099</td>














































    </tr>














































    <tr>







































      <td style="background-color: rgb(153, 255, 153);" align="undefined" valign="undefined">wrapper.jmx.rmi.user<br>







































wrapper.jmx.rmi.password</td>







































      <td align="undefined" valign="undefined">Sets the credentials
for JMX access, thus avoiding the handling and creation of jmx rmi
credentials file. Password is per default the empty string.<br>







































The url for accessing the wrapper is:<br>







































      <br>







































service:jmx:rmi:///jndi/rmi://localhost:&lt;wrapper.jmx.rmi.port&gt;/server<br>






































      <br>






































NOTE: per default no user/password is set. If user is set password is optional.<br>






































NOTE: System Tray Icon will read the credentials from the configuration file.</td>







































    </tr>







































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.condition.script<br>






















































wrapper.condition.script.args</td>






















































      <td>To conditionally control the application a groovy
script can be defined.<br>






















































The <span style="font-style: italic;">process</span>
variable can be used to control the process execution.<br>






















































The <span style="font-style: italic;">args</span>
variable is used to pass the arguments.<br>






































For bindings defined within the script see the documentation.<br>






















































The script is executed cyclically. The cycle is given by the property <span style="font-style: italic;">wrapper.condition.cycle</span>, which is in seconds.<a href="index.html">file:///Z:/dev/yajsw/doc/index.html</a><br>






















































The wrapper terminates if the script returns null.<br>






















































The property defines the name of the script file.<br>






















































Default: if no condition script is defined no condition check is
perormed.<br>






















































      <br>






















































Examples of scripts can be found in the scripts directory.<br>






















































      <br>






















































The following script makes sure that the application is running between
5:00 and 3:00 the next day<br>






















































wrapper.condition.script=timeCondition.gv<br>






















































wrapper.condition.script.args=5:00,3:00<br>






















































      <br>






















































The following script makes sure that the application is running while
the file "anchor.txt" exists.<br>






















































And that the application is not running once the file is deleted.<br>






















































wrapper.condition.script=fileCondition.gv<br>






















































wrapper.condition.script.args=anchor.txt<br>






















































      </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.condition.cycle</td>






















































      <td>Execution cycle for the condition script in seconds.<br>






















































Default:
if no cycle is defined the cycle is executed only once, assuming that
if required the cycle is implemented within the script<br>






















































      <br>






















































wrapper.condition.cycle=1</td>






















































    </tr>






















































    <tr>






















































      <td align="undefined" valign="undefined">wrapper.monitor.&lt;n&gt;<br>






















































wrapper.monitor.&lt;n&gt;.comparator<br>






















































wrapper.monitor.&lt;n&gt;.threshold<br>






















































wrapper.monitor.&lt;n&gt;.action<br>






















































wrapper.monitor.&lt;n&gt;.script<br>






















































wrapper.monitor.&lt;n&gt;.script.args</td>






















































      <td align="undefined" valign="undefined">Monitor
resources of the application and execute an action or script if
threshold is not held.<br>






















































The following monitors are currently available:<br>






















































CPU<br>






















































MEM<br>






















































THREAD<br>






















































      <br>






















































The following comparators are available:<br>






















































G :Greater<br>






















































L: Lower<br>






















































      <br>






















































Default: G<br>






















































      <br>






















































Example: Send an email if &nbsp;the thread count exceeds 20.<br>






















































wrapper.monitor.0 = THREAD<br>






















































wrapper.monitor.0.threshold = 20<br>






















































wrapper.monitor.0.script = sendMail.gv<br>






















































wrapper.monitor.0.script.args = <EMAIL>, too many threads in
application<br>






















































      <br>






















































      </td>






















































    </tr>






















































    <tr>































      <td style="background-color: rgb(153, 255, 153);" align="undefined" valign="undefined">wrapper.java.monitor.deadlock<br>































wrapper.java.monitor.deadlock.interval</td>































      <td align="undefined" valign="undefined">Monitors deadlocks from within the wrapped application. In case a deadlock is detected the message<br>































      <br>































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <div style="margin-left: 40px;">wrapper.java.monitor.deadlock: DEADLOCK IN THREADS:<br>































      </div>































      <br>































and a stacktrace of the according threads is printed to the log file.<br>































      <br>































By defining a trigger on this output one may define the action or script to execute in the case that a deadlock is detected.<br>































      <br>































      <span style="font-style: italic;">wrapper.java.monitor.deadlock.interval</span> can be used to set the interval for the check cycle in seconds. Default is 30 seconds.<br>































      <br>































Example: Turn monitoring on and display a system tray message when a deadlock is detected:<br>































      <br>































wrapper.java.monitor.deadlock = true<br>































wrapper.filter.trigger.deadlock.tray = wrapper.java.monitor.deadlock: DEADLOCK IN THREADS:<br>































wrapper.filter.script.deadlock.tray = scripts/trayMessage.gv<br>































wrapper.filter.script.deadlock.tray.arg s= Deadlock Detected<br>































      </td>































    </tr>































    <tr>































      <td style="background-color: rgb(153, 255, 153);" align="undefined" valign="undefined">wrapper.java.monitor.heap<br>































wrapper.java.monitor.heap.threshold.percent<br>































wrapper.java.monitor.heap.interval<br>































wrapper.java.monitor.heap.restart</td>































      <td align="undefined" valign="undefined">Monitor the heap size from within the wrapped application. In case the heap exceeds the given threshold the message<br>































      <br>































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <div style="margin-left: 40px;">wrapper.java.monitor.heap: HEAP SIZE</div>































      <br>































is printed to the log file.<br>































      <br>































By defining a trigger on this output one may define the action or script to execute in case a threshold violation is detected.<br>































      <br>































      <span style="font-style: italic;">wrapper.java.monitor.heap.threshold.percent</span> can be used to define the threshold in precent ( usedHeap / maxHeap * 100 ). Default value is 95.<br>































      <br>































      <span style="font-style: italic;">wrapper.java.monitor.heap.interval</span> can be used to set the interval for the check cycle in seconds. Default is 30 seconds.<br>































      <br>


















If <span style="font-style: italic;">wrapper.java.monitor.heap.restart</span> is set then used heap is sent from the application to the wrapper within the ping message.<br>


















If <span style="font-style: italic;">wrapper.java.monitor.heap.threshold.percent</span> is also set the application will be restarted by the wrapper if &nbsp;the used heap exceeds the given threshold.<br>































      <br>


















      <br>































Example: Turn on monitoring and display a system tray message when the heap size exceeds the default threshold:<br>































      <br>































wrapper.java.monitor.heap = true<br>































wrapper.filter.trigger.heap.tray = wrapper.java.monitor.heap: HEAP SIZE<br>































wrapper.filter.script.heap.tray = scripts/trayMessage.gv<br>































wrapper.filter.script.heap.tray.args = Heap Issue Detected<br>


















      <br>


















Example: Restart the application if the used heap exceeds 99% of maximal heap:<br>


















      <br>































wrapper.java.monitor.heap.restart = true<br>































wrapper.java.monitor.heap.threshold.percent=99</td>































    </tr>































    <tr>





























      <td style="background-color: rgb(102, 255, 153);" align="undefined" valign="undefined">wrapper.java.monitor.gc<br>





























wrapper.java.monitor.gc.interval<br>


















wrapper.java.monitor.gc.threshold<br>


















wrapper.java.monitor.gc.restart</td>





























      <td align="undefined" valign="undefined">This is similar to setting the "-verbose:gc" &nbsp;java option.<br>





























The difference is that the output is written to System.err and you set a pattern for the output.<br>





























      <br>





























      <span style="font-style: italic;">wrapper.java.monitor.gc</span>&nbsp; is a <a href="http://download.oracle.com/javase/1.4.2/docs/api/java/text/MessageFormat.html">java MessageFormat pattern</a> which is formatted with the following arguments: Used Heap,&nbsp;Minor GC Duration,&nbsp;Full GC Duration<br>





























      <br>































      <span style="font-style: italic;">wrapper.java.monitor.gc.interval</span> can be used to set the interval for the check cycle in seconds. Default is 30 seconds.<br>


















      <br>


















If <span style="font-style: italic;">wrapper.java.monitor.gc.restart</span> is set then used heap and full gc duration are sent from the application to the wrapper within the ping message.<br>


















If <span style="font-style: italic;">wrapper.java.monitor.gc.threshold</span>
is also set the application will be restarted by the wrapper if
&nbsp;the full gc duration (millis)&nbsp; exceeds the given threshold.<br>































      <br>































Example: Turn on monitoring and print the data in CSV format:<br>































      <br>





























wrapper.java.monitor.gc=MYGC: {0\,number\,integer}\, {1\,number\,integer}\, {2\,number\,integer}<br>


















      <br>


















Example: Restart the application if the jvm executes full gc for "too long":<br>


















      <br>































wrapper.java.monitor.gc.restart = true<br>































wrapper.java.monitor.gc.threshold=2500</td>





























    </tr>





























    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.tray</td>






















































      <td align="undefined" valign="undefined">Indicates
if a tray icon should be created.<br>






















































This requires the wrapper to run with JVM 1.6 or higher.<br>






















































Windows: when installing as service sets wrapper.ntservice.interactive
= true<br>






















































Note: Windows: If combined with wrapper.ntservice.account: the given
account must have the <a href="http://msdn.microsoft.com/en-us/library/ms683502%28VS.85%29.aspx">according
privilges</a><br>






















































      <br>






















































Default: false<br>






















































      <br>






















































Example<br>






















































wrapper.tray = true</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.tray.icon</td>






















































      <td align="undefined" valign="undefined">Name
of the file to be used as icon for the system tray.<br>






















































      <br>






















































Default: If no icon is defined or the defined icon is not found a
default icon (console.png) is used.<br>






















































      <br>






















































Example:<br>






















































wrapper.tray.icon = tomcat.gif</td>






















































    </tr>






















































    <tr>




























      <td style="background-color: rgb(102, 255, 153);" align="undefined" valign="undefined">wrapper.tray.commands</td>




























      <td align="undefined" valign="undefined">A list indicating which commands are to be displayed in the system tray icon popup. Following values are possible:<br>




























      <br>




























      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <div style="margin-left: 40px;">start<br>




























stop<br>




























restart<br>




























console<br>




























response<br>




























exitWrapper<br>




























startService<br>




























exitTray</div>




























      <br>




























Default: all menue points are displayed.<br>




























      <br>




























Example:<br>




























wrapper.tray.commands = startService, exitWrapper<br>




























      <br>




























Note:&nbsp; this is a list -&gt;&nbsp; "," should not be escaped.</td>




























    </tr>




























    <tr>




























      <td style="background-color: rgb(102, 255, 153);" align="undefined" valign="undefined">wrapper.ntservice.autoreport.waitready</td>




























      <td align="undefined" valign="undefined">Boolean value indicating if the tray icon status should be set to waiting until the application calls the method <span style="font-style: italic;">WrapperJVMMain.WRAPPER_MANAGER.reportServiceStartup().<br>




























      <br>




























      </span>Default: false<br>




























      <br>




























Example:<br>




























      <br>




























wrapper.ntservice.autoreport.waitready = true</td>




























    </tr>




























    <tr>




























      <td style="background-color: rgb(102, 255, 153);" align="undefined" valign="undefined">wrapper.tray.dialog</td>




























      <td align="undefined" valign="undefined">Boolean value
indicating if the user should be displayed a dialog before stop,
restart, exitWrapper, exitTray commands are executed.<br>




























      <br>




























Default: true<br>




























      <br>




























Example:<br>




























      <br>




























wrapper.tray.dialog = false</td>




























    </tr>




























    <tr>




























      <td style="background-color: rgb(102, 255, 153);" align="undefined" valign="undefined">wrapper.tray.text.dialog_exit_tray,
wrapper.tray.text.dialog_stop, wrapper.tray.text.dialog_restart,
wrapper.tray.text.dialog_exit_wrapper</td>




























      <td align="undefined" valign="undefined">Texts for the dialogs displayed to the user when calling stop, restart, exitWrapper, exitTray commands.<br>




























      <br>




























Default: if the property is not defined a default text is displayed. See source code for more information.</td>




























    </tr>




























    <tr>



























      <td style="background-color: rgb(102, 255, 153);" align="undefined" valign="undefined"> wrapper.tray.look_and_feel</td>



























      <td align="undefined" valign="undefined">Set the look and feel for the system tray icon menue and console.<br>



























This property expects the name of a L&amp;F class.<br>



























If the given class cannot be loaded the default L&amp;F is used.<br>



























      <br>



























Default: default L&amp;F<br>



























      <br>



























Example:<br>



























      <br>



























wrapper.tray.look_and_feel = com.sun.java.swing.plaf.windows.WindowsLookAndFeel</td>



























    </tr>



























    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.app.account</td>






















































      <td align="undefined" valign="undefined">User
account for running the application. Equivalent to Windows runas or
Linux sudo.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.app.password</td>






















































      <td align="undefined" valign="undefined">Optional
password for wrapper.app.account</td>






















































    </tr>






















































    <tr>





















































      <td style="background-color: rgb(102, 255, 153);">wrapper.ntservice.additional.&lt;n&gt;</td>





















































      <td align="undefined" valign="undefined">Additional java options for the wrapper service. <br>





















































wrapper.ntservice.additional.1=-Xmx5m<br>





















































wrapper.ntservice.additional.2=-Xms3m
      <br>





















































wrapper.ntservice.additional.3=-server
      </td>





















































    </tr>





















































  
  
  
  
  
  <tr>






















      <td style="background-color: rgb(153, 255, 153);" align="undefined" valign="undefined">wrapper.ntservice.java.command</td>






















      <td align="undefined" valign="undefined">Enables to set the java command for the service wrapper. <br>






















Default: use the same java command as for the application.<br>






















      <br>






















NOTE: when running as console the wrapper java command is set in the batch file setenv.</td>






















    </tr>






















    <tr>





















      <td style="background-color: rgb(153, 255, 153);" align="undefined" valign="undefined">wrapper.ntservice.java.customProcName</td>





















      <td align="undefined" valign="undefined">To easily find applications in the task list, the java exe may be copied to a file with a different name.<br>





















The copied file is used with the wrapper process when run as service.<br>






















      <br>






















This property indicates to yajsw to copy java.exe or javaw.exe to a
file with a given name.
If the property (after interpolation) specifies a full path to a file
with existing parent path, the given path is used. Otherwise a temporary file <br>





















      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <div style="margin-left: 80px;">&nbsp; &nbsp;File.createTempFile( <span style="font-style: italic;">java_&lt;customProcName&gt;_nnnn.exe</span>))&nbsp;&nbsp;<span style="font-style: italic;"></span><br>





















      </div>





















 is created. The file is not marked for deletion.<br>





















      <br>





















Copying of file is done during install of the service.<br>





















      <br>





















If you use a temporary file, you should make sure that the file is not
deleted on reboot. Otherwise the service may not be restarted.<br>





















      <br>





















Default: java exe is not copied.<br>






















      <span style="font-style: italic;">































      <br>






















































      </span>NOTE: currently only available for windows</td>





















    </tr>





















    <tr>















































      <td style="background-color: rgb(102, 255, 153);">wrapper.filter.missing.trigger-regex.&lt;n&gt;<br>












































wrapper.filter.missing.trigger.&lt;n&gt; = &lt;text&gt;,&lt;count&gt;,&lt;period&gt;<br>















































wrapper.filter.missing.script.&lt;n&gt;<br>















































wrapper.filter.missing.script.&lt;n&gt;.args<br>















































wrapper.filter.missing.action.&lt;n&gt;<br>





























 wrapper.filter.missing.autostop.&lt;n&gt;</td>















































      <td align="undefined" valign="undefined">Some applications may
hung although the heartbeat between the wrapper and the application is
ok. This happens generally in polling applications which
cyclically&nbsp; log the result of the polling.<br>















































      <br>















































This property allows one to specify the expected text, the number of
expected occurences and the time period. The trigger is raised if
within a time period the number of lines containing the given text is
lower than the given count.<br>















































      <br>















































Time period is in seconds.<br>












































"*" stands for any line for trigger (not for trigger-regex)<br>















































      <br>















































       The boolean property
wrapper.filter.missing.autostop.&lt;n&gt; indicates if the
monitoring&nbsp; is stopped after the first trigger. Default is true.<br>





























      <br>















































Example:<br>















































      <br>















































wrapper.filter.missing.trigger.ok &nbsp;= &nbsp;*, 1, 10<br>















































wrapper.filter.missing.script.ok = sendmail.gv<br>















































wrapper.filter.missing.script.ok.args = <EMAIL>,"process
restart","process not logging -&gt;
restarting"<br>















































wrapper.filter.missing.action.ok = RESTART<br>















































&nbsp;<br>















































In case the process does not log at least 1 line per 10 seconds an email is sent and the process is restarted.<br>















































      </td>















































    </tr>















































  
  <tr>













































      <td style="background-color: rgb(102, 255, 153);"> wrapper.restart.delay.script<br>













































wrapper.restart.delay.script.args</td>













































      <td align="undefined" valign="undefined">Some applications may
require a dynamic setting of the restart delay. For example some
applications may double the timeout after each restart.<br>













































Other applications may want to check resources, such as the
availability of network HDD before restarting. Instead of defining a <span style="font-style: italic;">wrapper.restart.delay</span>
one may define a script with is executed during restart and which is
supposed to return an Integer. If null, or another value is returned
the default restart delay is used.<br>













































      <br>













































Example:<br>













































      <br>













































wrapper.restart.delay.script=scripts/linearRestartDelay.gv<br>













































wrapper.restart.delay.script.args=10<br>













































      </td>













































    </tr>













































  
  
  <tr>










































      <td style="background-color: rgb(102, 255, 153);"><span style="font-style: italic;">&nbsp;</span>wrapper.app.pre.script</td>










































      <td align="undefined" valign="undefined">Some applications
require a script to be executed before the main method of an
application is executed. For example, when running as service we may
need to set the network printer or the network disk.<br>










































      <br>










































Example:<br>










































      <br>










































      <span style="font-style: italic;">&nbsp;wrapper.app.pre.script=scripts/mapNetworkDrive.gv</span></td>










































    </tr>










































  
  <tr>








































      <td style="background-color: rgb(102, 255, 153);">wrapper.java.debug.port </td>








































      <td align="undefined" valign="undefined">To debug the application
one may set the wrapper.java.additional properties to -Xdebug and
-Xrunjdwp:transport=dt_socket\,server=y\,suspend=y\,address=1044.
However when debuging the wrapper may run into time outs thus
restarting the application. To avoid this one may instead set the
attributewrapper.java.debug.port. This will add the above jvm options and will set the wrapper timeouts to a maximum. <br>








































      <br>








































Example:<br>








































      <br>








































      <span style="font-style: italic;">wrapper.java.debug.port = 1044</span><br>








































      <br>








































Will result in&nbsp;:<br>








































      <br>








































java -Xdebug &nbsp;-Xrunjdwp:transport=dt_socket,server=y,suspend=y,address=1044 ...</td>








































    </tr>








































  <tr>







































      <td style="background-color: rgb(102, 255, 153);">wrapper.app.pre_main.script</td>







































      <td align="undefined" valign="undefined">Some applications
require a script to be executed before the main method is invoked but
after the wrapper heart beat is initiated. The script is invoked
with the application args.</td>







































    </tr>







































  
  
  
  
  
  
  
  
  <tr>






























      <td style="background-color: rgb(102, 255, 153);">wrapper.console.minimized</td>






























      <td align="undefined" valign="undefined">Boolean (true, false) indicating if the console of the
application should be visible started minimized.<br>






















































Default: false<br>






























      <br>






























NOTE:<br>






























This property takes effect only if &nbsp;<span style="font-style: italic;">wrapper.console.visible</span> is true.<br>






























This property is only effective on windows.</td>






























    </tr>





























    <tr>





























      <td style="background-color: rgb(153, 255, 153);">wrapper.ntservice.logon_active_session<br>





























wrapper.ntservice.desktop</td>





























      <td align="undefined" valign="undefined">Handle Windows Vista (and higher) Session 0 service restriction.<br>





























      <a href="index.html/#mozTocId245576">see here</a></td>





























    </tr>






























  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <tr>









      <td style="background-color: rgb(153, 255, 153);">wrapper.restart.reload_cache</td>









      <td align="undefined" valign="undefined">If this property is set to true in conjunction with&nbsp;<span style="font-weight: bold; font-style: italic;">wrapper.restart.reload_configuration</span> and network launching:<br>









on each restart he wrapper will reload the configuration and update the cache.</td>









    </tr>









  
  
  
  
  
  
  
  
  
  </tbody>
</table>






















































<br>






















































<h4><a class="mozTocH4" name="mozTocId669993"></a>Configuration Properties from tanuki software</h4>






















































<table style="text-align: left; width: 1151px; height: 2845px;" border="1" cellpadding="2" cellspacing="2">






















































  <tbody>






















































    <tr>






















































      <td style="width: 30%;">
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <h5><a class="mozTocH5" name="mozTocId576902"></a>Property Name</h5>






















































      </td>






















































      <td>
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <h5><a class="mozTocH5" name="mozTocId148563"></a>Comments</h5>






















































      </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 102, 102);">wrapper.adviser</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 102, 102);">wrapper.anchorfile</td>






















































      <td>YAJSW implements anchor file with groovy script. Use wrapper.condition.script.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 102, 102);">wrapper.anchorfile.umask</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.app.parameter.&lt;n&gt;</td>






















































      <td>Application parameters to pass to your application when
it is launched. These are the parameters passed to your application's
main method. </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 102, 102);">wrapper.commandfile</td>






















































      <td>YAJSW implements command&nbsp; file with groovy script. Use wrapper.condition.script.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 102, 102);">wrapper.console.flush</td>






















































      <td>This should already be done by java.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.console.format</td>






















































      <td>Format to use for output to the console. Logging was
intentionally kept simple. The format consists of the tokens <tt>'L'</tt>
for log level, <tt>'P'</tt> for prefix, <tt>'D'</tt>
for thread, <tt>'T'</tt> for time, <tt>'Z'</tt>
for millisecond time, and <tt>'M'</tt> for message. If the
format contains these values then it will be included in the format.
The order of the tokens does not affect the way the log appears, but
the <tt>'M'</tt> token should usually be placed last as it
is the only column without a uniform width. If the property is missing
or commented out, then the default value <tt>'PM'</tt>
will be used. Setting the property to a blank value will cause console
output to be disabled. </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.console.loglevel</td>






















































      <td>Log level to use for console output. Valid log levels
include: <tt>NONE</tt> for no output, <tt>FATAL</tt>
to only show fatal error messages, <tt>ERROR</tt> to show
all error messages, <tt>STATUS</tt> to show all state
changes, <tt>INFO</tt> shows all JVM output and
informative messages, and <tt>DEBUG</tt> shows detailed
debug information. The default value is <tt>INFO</tt>. </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.console.title</td>






















































      <td>Setting this property allows the Title Bar of the
console in which the java application is running to be set. </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 102, 102);">wrapper.cpu.timeout</td>






















































      <td>Timer attributes are not required. We have
wrapper.ping.interval. In
case of CPU starvation by the application the wrapper will not be
pinged.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 102, 102);">wrapper.daemonize</td>






















































      <td>Whereever possible applications are demonized:<br>






















































Java applications are daemonized except if wrapper.visible = false<br>






















































Native images are not deamonized.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.debug</td>






















































      <td style="background-color: rgb(255, 204, 255);">Used
to enable further debug information. This requires that logging to a
file or the console is enabled.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.disable_restarts</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.disable_shutdown_hook</td>






















































      <td>No shutdown hook is required by YAJSW. </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.filter.action.&lt;n&gt;</td>






















































      <td>
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <p> The wrapper.filter pair of properties make it possible
to filter the output of a JVM and then perform some action whenever a
specific trigger string is found.&nbsp; </p>






















































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <p>Possible actions are <tt>RESTART</tt>, <tt>SHUTDOWN</tt>,
and <tt>NONE</tt>. <tt>RESTART</tt> will stop
the current JVM and then restart a new invocation. <tt>SHUTDOWN</tt>
will stop the JVM. <tt>NONE</tt> is useful because it will
prevent any triggers with a higher number from being triggered.</p>






















































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <p> If an action is omitted, it will default to <tt>RESTART</tt>.
      </p>






















































      </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.filter.trigger.&lt;n&gt;</td>






















































      <td>
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <p> The wrapper.filter pair of properties make it possible
to filter the output of a JVM and then perform some action whenever a
specific trigger string is found. The filtering process works by
comparing JVM console output against registered triggers until a match
is found. At that point the associated action is executed. Only the
first matching trigger will be handled for any line of output.&nbsp;</p>






















































      </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.ignore_signals</td>






















































      <td>Use wrapper.java.additional.jvm-opts=-Xsr</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.java.additional.&lt;n&gt;</td>






















































      <td>Additional Java parameters to pass to Java when it is
launched. </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.java.classpath.&lt;n&gt;</td>






















































      <td style="background-color: rgb(204, 255, 255);">YAJSW
supports wildcard for files as well as folders. However recursive wild
cards for folders are currently not supported, but may be supported in
future.<br>






















































For wildcard syntax refer to <a href="http://commons.apache.org/io/apidocs/org/apache/commons/io/filefilter/WildcardFileFilter.html">apache
commons</a>.<br>






















































      <br>






















































Examples:<br>






















































      <br>






















































wrapper.java.classpath.1=lib/*/*.jar<br>






















































wrapper.java.classpath.2=*test*/*.jar</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.java.command</td>






















































      <td style="background-color: rgb(204, 255, 255);">The
command to use when launching a JVM.<br>






















































The wrapper checks that the given file exists.<br>






















































      <br>






















































For the wrapper to choose a java command use the wrapper.java.command.*
properties. <br>






















































If wrapper.java.command is set and the file exists, the
wrapper.java.command.* are ignored</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.java.command.loglevel</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.java.idfile</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.java.initmemory</td>






















































      <td style="background-color: rgb(204, 255, 255);">The
initial amount of memory in megabytes that the JVM should allocate.
If -Xms is defined using wrapper.java.additional property, then this
property is ignored.<br>






















































      <span style="font-style: italic;">wrapper.java.initialmemory.relative</span>
is relative to the available total RAM. If this is defined, then <span style="font-style: italic;">wrapper.java.</span><span style="font-style: italic;">initialmemory </span>is
ignored.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.java.library.path.&lt;n&gt;</td>






















































      <td>The library path is used to specify a list of
directories in which to look for any native (JNI) libraries used by the
application..<br>






















































Note that YAJSW does not require native libraries for the java
application.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 102, 102);">wrapper.java.library.path.append_system_path</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.java.mainclass</td>






















































      <td style="background-color: rgb(204, 255, 255);">Class
to execute when the wrapper starts the application.<br>






















































The default class is <span style="font-style: italic;">org.rzo.yajsw.WrapperMain</span>.
This class will support all required integration methods.<br>






















































Note that this is not the main class of your java application.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.java.maxmemory</td>






















































      <td style="background-color: rgb(204, 255, 255);">The
maximal amount of memory in megabytes that the JVM should allocate. If
-Xmx is defined using wrapper.java.additional property, then this
property is ignored.<br>






















































      <span style="font-style: italic;">wrapper.java.maxmemory.relative</span>
is relative to the available total RAM. If this is defined, then <span style="font-style: italic;">wrapper.java.maxmemory</span>
is ignored.<br>






















































      </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.java.idfile.umask</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.java.pidfile</td>






















































      <td style="background-color: rgb(204, 255, 255);">File
to write the java application process ID to. If set, a file containing
the pid of the Wrapper process will be written to the specified
location when the process is launched. The file will be deleted when
the Wrapper process has terminated. This property is not set by
default. On process restart the file is updated. The file is not locked
and may be deleted by external applications.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.java.pidfile.umask</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.java.statusfile</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.java.statusfile.umask</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.java.umask</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.jvm_exit.timeout</td>






















































      <td style="background-color: rgb(255, 204, 255);">Currently
we do not control the jvm exit timeout and the shutdown timeout. We
only control the total time required to stop the appliation. If the
application is not killed within (wrapper.jvm_exit.timeout &nbsp;+
wrapper.shutdown.timeout) the application is killed by calling
TerminateProcess, which immediatly terminates the process. </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 102, 102);">wrapper.jvm.port</td>






















































      <td>JVM
port is not required. YAJSW connects from the application to the
wrapper, so only one port definition is required. The application port
is automatically selected.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 102, 102);">wrapper.jvm.port.max</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 102, 102);">wrapper.jvm.port.min</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.lockfile</td>






















































      <td style="background-color: rgb(204, 255, 255);">File
to create and lock when process is started. If set, is created and
locked when the process is launched. The file will be deleted when the
application is idle. This property is not set by default. If the lock
file already exists and is locked the process will not start. The lock
is maintained during restart of the application.&nbsp;</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.lockfile.umask</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.logfile</td>






















































      <td>Log file to which all output to the console will be
logged. If the logfile is not specified, then file logging will be
written to a file called <tt>"wrapper.log"</tt> in the
same directory as the Wrapper executable. Providing a blank value will
cause file logging to be disabled.<br>






















































      <br>






















































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <p> The specified log file name may contain one or both of
the tokens; <tt>ROLLNUM</tt> and <tt>YYYYMMDD</tt>.
      </p>






















































The <tt>ROLLNUM</tt> token is used when the log file is
rolled. <br>






















































      <br>






















































The <tt>YYYYMMDD</tt> token is required when the
wrapper.logfile.rollmode property has a value of DATE. This token will
be replaced by the date of the log entries contained in the file. <br>






















































      </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.logfile.format</td>






















































      <td>Format to use for logging to the log file. Logging was
intentionally kept simple. The format consists of the tokens <tt>'L'</tt>
for log level, <tt>'P'</tt> for prefix, <tt>'D'</tt>
for thread, <tt>'T'</tt> for time, <tt>'Z'</tt>
for millisecond time, and <tt>'M'</tt> for message. If the
format contains these values then it will be included in the format.
The order of the tokens does not affect the way the log appears, but
the <tt>'M'</tt> token should usually be placed last as it
is the only column without a uniform width. If the property is missing
or commented out, then the default value <tt>'LPTM'</tt>
will be used. Setting the property to a blank value will cause file
logging to be disabled.<br>
















      <br>















YAJSW extension: to control end of line characters other than java
default append "\r\n" or "\n" or "\n" to the end. Default is
System.getProperty("line.separator")<br>
















      <br>
















Example:<br>
















      <br>
















wrapper.logfile.format = LPTM\r\n </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 102, 102);">wrapper.logfile.inactivity.timeout</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.logfile.loglevel</td>






















































      <td>Log level to use for logging to the log file. Valid log
levels include: <tt>NONE</tt> for no output, <tt>FATAL</tt>
to only show fatal error messages, <tt>ERROR</tt> to show
all error messages, <tt>STATUS</tt> to show all state
changes, <tt>INFO</tt> shows all JVM output and
informative messages, and <tt>DEBUG</tt> shows detailed
debug information. The default value is <tt>INFO</tt>. </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.logfile.maxfiles</td>






















































      <td>When log file rolling is enabled, old log files will be
renamed by replacing their ROLLNUM token with an integer or by
appending that integer to the end of the file. Larger indices are older
log files. The maximum number of rolled log files can be set using this
property.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.logfile.maxsize</td>






















































      <td>The wrapper.log file can be set to roll when the file
reaches a specified size. The default value of 0 will disable log file
rolling. To roll the file, specify a maximum file size in bytes. The
size can be abbreviated with the suffixes <tt>'k'</tt> for
kilobytes, or <tt>'m'</tt> for megabytes. For example, <tt>'10m'</tt>
sets the log file to be rolled when it reaches a size of 10 megabytes. </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.logfile.rollmode</td>






















































      <td>
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <p style="background-color: rgb(204, 255, 255);">
Controls the roll mode of the log file. Possible values include: </p>






















































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <ul>






















































        <li style="background-color: rgb(204, 255, 255);">
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          <p style="background-color: rgb(204, 255, 255);">
          <tt>JVM</tt> - The log file will be rolled on
Wrapper startup as with the <tt>WRAPPER</tt> mode. But it
will also be rolled just before any restarted JVMs are launced. The log
files will not have a maximum size and will continue to grow until the
Wrapper or a JVM is restarted. </p>






















































        </li>






















































        <li>
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          <p style="background-color: rgb(204, 255, 255);">
          <tt>DATE</tt> - As each log entry is logged to a
file, its timestamp is compared with a YYYYMMDD formatted token in the
current log file. Whenever this date changes a new log file will be
opened. This has the benefit of grouping all log entries for a given
day in a specific file. A file will not be created on days which do not
have any log output. </p>






















































        </li>






















































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      </ul>






















































      </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.logfile.umask</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.max_failed_invocations</td>






















































      <td>Maximum number of times that the Wrapper will attempt
to restart the JVM if each attempted invocation exits abnormally or is
restarted shortly after having being launched. Must be at least 1.
Defaults to 5 invocations. </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.monitor_thread_count</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 102, 102);">wrapper.native_library</td>






















































      <td>YAJSW does not require a native library for the java
application.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.ntservice.account</td>






















































      <td style="background-color: white;">User name to be
used by the windows service.<br>






















































On windows the format should be:
&lt;domain&gt;\&lt;user&gt;<br>






















































      </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.ntservice.console</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.ntservice.dependency.&lt;n&gt;</td>






















































      <td>Names of any other Services or groups which must be
running before this service can be started. Stopping any of the listed
services, or all members of a specified group, will also stop this
service. Because both services and groups share the same name space,
group names must be prefixed with the '+' character.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.ntservice.description</td>






















































      <td>Description of the NT service when installed.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.ntservice.displayname</td>






















































      <td>Display name of the NT service when installed. </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.ntservice.hide_console</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.ntservice.interactive</td>






















































      <td>Note: Windows: If combined with
wrapper.ntservice.account: the given account must have the <a href="http://msdn.microsoft.com/en-us/library/ms683502%28VS.85%29.aspx">according
privilges</a><br>






















































Please note <a href="http://en.wikipedia.org/wiki/Shatter_attack">potential
security risks</a> associated with this.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.ntservice.load_order_group</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.ntservice.name</td>






















































      <td>Name of the NT service when installed. </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.ntservice.password</td>






















































      <td style="background-color: white;">password for
user specified by wrapper.ntservice.account.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.ntservice.password.prompt</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.ntservice.password.prompt.mask</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.ntservice.pausable</td>






















































      <td><br>






















































      </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.ntservice.pausable.stop_jvm</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.ntservice.process_priority</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.ntservice.starttype</td>






















































      <td>Mode in which the service is installed. <tt>AUTO_START</tt>
or <tt>DEMAND_START or (for windows vista and higher only) </tt><tt>DELAYED_AUTO_START</tt><br>



























      <br>



























      <tt>


























Default: AUTO_START</tt></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.on_exit.&lt;n&gt;</td>






















































      <td style="background-color: rgb(204, 255, 255);">
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <p> It may be desirable to restart the application in the
event that it exits with certain exit codes. This set of properties
makes this possible. </p>






















































      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <p>It possible to specify an action to take for any exit
code by making use of the <tt>wrapper.on_exit.default</tt>
property and setting it to either <tt>SHUTDOWN</tt> or <tt>RESTART</tt>.
The default on_exit property defaults to <tt>SHUTDOWN</tt>.</p>



















      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <p>STOP: stop the application but not the wrapper
      </p>






















































      </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.pidfile</td>






















































      <td style="background-color: rgb(204, 255, 255);">File
to write the wrapper&nbsp; process ID to. If set, a file containing
the pid of the wrapper process will be written to the specified
location when the process is launched. The file will be deleted when
the application process has terminated. This property is not set by
default. The file is not locked
and may be deleted by external applications.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.pidfile.umask</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.ping.interval</td>






















































      <td style="background-color: rgb(255, 204, 255);">Number
of seconds between java application ping requests to the wrapper.
Defaults to 5 seconds. </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.ping.interval.logged</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.ping.timeout</td>






















































      <td style="background-color: rgb(255, 204, 255);">Number
of seconds the wrapper waits for a ping message from the java
application. The application is considered for restart if no ping
message is received within the give timeout. This value should be
greater or equal to&nbsp;wrapper.ping.interval. Default is 30
seconds.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.port</td>






















































      <td style="background-color: rgb(204, 255, 255);">The
Wrapper uses a socket to communicate with the java or groovy
application. It waits for the application to connect. Only connections
from localhost are accepted. Only a single connection which has been
authenticated is accepted. All other connections are rejected. If the
application has not authenticated itsself within a timeout, the wrapper
will restart it.<br>






















































      <br>






















































Generally the wrapper will search for a free port. In some cases you
may need to assign a specific port or a range of ports.<br>






















































      <br>






















































This property allows the configuration of a specific port.<br>






















































      <br>






















































NOTE:
native applications are currently not wrapped by java. Therefore the
wrapper (controller) only monitors that the application is running. It
does not monitor hangging of the application and therefore does not
require a port. </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.port.max</td>






















































      <td style="background-color: rgb(204, 255, 255);">The
wrapper searches a free port by starting at wrapper.port.min and
terminating at port wrapper.port.max.<br>






















































If this property is not defined the wrapper will iterate through all
ports, starting at wrapper.port.min.<br>






















































      <br>






















































Per default no max port is defined.<br>






















































      </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.port.min</td>






















































      <td style="background-color: rgb(204, 255, 255);">This
property defines the first port to use while searching for a free port.<br>






















































      <br>






















































Default min port is: 15003</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.registry.java_home</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.request_thread_dump_on_failed_jvm_exit</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.restart.delay</td>






















































      <td>Controls the number of seconds to pause between a JVM
exiting for any reason, and a new JVM being launched. Defaults to 5
seconds. </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(153, 255, 153);">wrapper.restart.reload_configuration</td>






















































      <td style="background-color: rgb(204, 255, 255);">Controls whether or not the Wrapper configuration file will be
            reloaded in the event of a JVM restart.  Defaults to FALSE.<br>




















































NOTE: currently only the configuration file is reloaded. Environement or System properties changes are not.<br>




















































NOTE: This property may be changed when reloading the configuration.
        </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.shutdown.timeout</td>






















































      <td style="background-color: rgb(204, 255, 255);">see
wrapper.jvm_exit.timeout</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.signal.mode.hup</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.signal.mode.usr1</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.signal.mode.usr2</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.single_invocation</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(153, 255, 153);">wrapper.startup.delay</td>






















































      <td>Number of seconds to pause between the Wrapper being launched
            and the first JVM being launched.  <br>



















































      <br>



















































Default:&nbsp;0</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.startup.timeout</td>






















































      <td style="background-color: rgb(255, 204, 255);">Number
of seconds to allow between the time that the Wrapper asks the
application to start and the time that the wrapped application connects
to the wrapper and sends the correct key. Defaults to 30 seconds.
The JVM is killed if it does not start within the given timeout. </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.statusfile</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.statusfile.umask</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.successful_invocation_time</td>






















































      <td>Specifies the amount of time that an application must
remain running before it will be considered to have been a successful
invocation. This property's use is described in the description of the<span style="font-style: italic;"> </span><span style="font-style: italic;">wrapper.max_failed_invocations</span>
property. Defaults to 60 seconds. </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.syslog.facility</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.syslog.ident</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.syslog.loglevel</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.thread_count_delay</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 102, 102);">wrapper.timer_fast_threshold</td>






















































      <td>Timer
attributes are not required. We have wrapper.ping.interval. In case of
CPU starvation by the application the wrapper will not be pinged.<br>






















































      </td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 102, 102);">wrapper.timer_slow_threshold</td>






















































      <td>Timer
attributes are not required. We have wrapper.ping.interval. In case of
CPU starvation by the application the wrapper will not be pinged.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 255, 102);">wrapper.umask</td>






















































      <td></td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(255, 102, 102);">wrapper.use_system_time</td>






















































      <td>Timer
attributes are not required. We have wrapper.ping.interval. In case of
CPU starvation by the application the wrapper will not be pinged.</td>






















































    </tr>






















































    <tr>






















































      <td style="background-color: rgb(102, 255, 153);">wrapper.working.dir</td>






















































      <td>Working directory for the java application. </td>






















































    </tr>






















































  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  </tbody>
</table>






















































<br>






















































</body>
</html>
