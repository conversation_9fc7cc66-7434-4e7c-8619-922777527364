sal.h
assert.h
conio.h
crtassem.h
crtdbg.h
crtdefs.h
crtwrn.h
ctype.h
WinDef.h
WinNT.h
delayimp.h
direct.h
dos.h
errno.h
excpt.h
fcntl.h
float.h
fpieee.h
io.h
iso646.h
limits.h
locale.h
malloc.h
math.h
mbctype.h
mbstring.h
memory.h
minmax.h
new.h
omp.h
ompassem.h
pgobootrun.h
process.h
rtcapi.h
search.h
setjmp.h
setjmpex.h
share.h
signal.h
srv.h
stdarg.h
stddef.h
stdexcpt.h
stdio.h
stdlib.h
string.h
tchar.h
time.h
use_ansi.h
vadefs.h
varargs.h
wchar.h
wctype.h
xlocinfo.h
xmath.h
ymath.h
yvals.h
CommDlg.h
WinUser.h
WinNls.h
C:\Program Files (x86)\Microsoft Visual Studio 9.0\VC\crt\src\internal.h
strsafe.h
penwin.h

-IC:\Program Files (x86)\Microsoft Visual Studio 9.0\VC\Include
-IC:\Program Files (x86)\Microsoft SDKs\Windows\v5.0\Include
-IC:\Program Files (x86)\Microsoft Visual Studio 9.0\VC\crt\src
-D_M_IX86=500
-D_MSC_VER=9090
-D_MSC_EXTENSIONS
-D_WIN32_WINNT=0x0400
-D_WIN32_WINDOWS=0x400
-D_INTEGRAL_MAX_BITS=32
-D_X86_
-D_WIN32
-D_USE_DECLSPECS_FOR_SAL=0
-D_USE_ATTRIBUTES_FOR_SAL=0
-DWINVER=0x0600
-DCRTDLL
-D_CRTBLD
-D_OPENMP_NOFORCE_MANIFEST
-DSTRSAFE_LIB
-DSTRSAFE_NO_CB_FUNCTIONS
-DSTRSAFE_NO_CCH_FUNCTIONS
-DSTRSAFE_LIB_IMPL
-DLPSKBINFO=LPARAM
-D_PHNDLR=void *
-D_WCHAR_T_DEFINED
-DCONST=const
