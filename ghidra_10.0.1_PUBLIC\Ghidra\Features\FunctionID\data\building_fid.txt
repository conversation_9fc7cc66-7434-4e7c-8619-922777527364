The file common_symbols_win32.txt contains the symbols that were considered common when generating the
.fidb files for Windows 32-bit system code.

The main entry point for building adding libraries to a .fidb file is the dialog
      Tools -> Function ID -> "Populate FidDb from programs ...".
This takes on optional file parameter called "Common Symbols File".  The .fidb files currently being
distributed with Ghidra were generated by supplying common_symbols_win32.txt here.
After a library is built, starting from this dialog, a final window provides a list of the most
common symbols encountered for that particular library.  For Windows system code, the top 70 or so symbols from
each list, generated for the libraries listed below, were merged and deduped to produce the final common_symbols_win32.txt.

Visual Studio 1998 Debug
Visual Studio 1998 Release
Visual Studio 2003 Debug
Visual Studio 2003 Release
Visual Studio 2005 Debug
Visual Studio 2005 Release
Visual Studio 2008 Debug
Visual Studio 2008 Release
Visual Studio 2010 Debug
Visual Studio 2010 Release
Visual Studio 2012 Debug
Visual Studio 2012 Release
Visual Studio 2015 Debug
Visual Studio 2015 Release

Each .fidb was generated by supplying common_symbols_win32.txt as the "Common Symbols File" parameter.
After this completed, adjustments were performed by running the RemoveFunctions.java script on the database.
This makes the following modifications:

1)  Auto-fail is set for functions which match a list of full hashes.
2)  Auto-fail is set for functions whose name matches specific regular expressions.
3)  Force-relation is set for functions which match a list of full hashes.
4)  Force-specific is set for functions which match a list of full hashes.
5)  Auto-pass is set for functions which match a list of full hashes.

Auto-fail means a full-hash match will not be returned under any circumstances (even though the function is still in the database)
Force-relation means a full-hash match will only be returned if one of the function's parents or children also matches.
Force-specific means a full-hash match will only be returned if the specific hash matches as well.
Auto-pass means a full-hash match is always returned, even if the function is tiny.

The final .fidb was defragmented using the RepackFid.java script to make the disk image as small as possible.
