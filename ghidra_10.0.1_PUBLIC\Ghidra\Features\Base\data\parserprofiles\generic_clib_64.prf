sys/types.h
types.h
stddef.h
stddef.h
openssl/opensslconf-x86_64.h
openssl/bn.h
openssl/ssl.h
openssl/asn1_mac.h
openssl/asn1t.h
openssl/blowfish.h
openssl/camellia.h
openssl/cast.h
openssl/cmac.h
openssl/cms.h
openssl/conf_api.h
openssl/des.h
openssl/dso.h
openssl/engine.h
openssl/fips_rand.h
openssl/idea.h
openssl/krb5_asn.h
openssl/md2.h
openssl/md4.h
openssl/md5.h
openssl/ocsp.h
openssl/pkcs12.h
openssl/rc2.h
openssl/rc4.h
openssl/ripemd.h
openssl/seed.h
openssl/ssl3.h
openssl/txt_db.h
openssl/whrlpool.h
aio.h
arpa/inet.h
cpio.h
dirent.h
fcntl.h
fmtmsg.h
fnmatch.h
ftw.h
glob.h
grp.h
iconv.h
langinfo.h
libgen.h
monetary.h
mqueue.h
ndbm.h
net/if.h
netdb.h
netinet/in.h
netinet/tcp.h
nl_types.h
poll.h
pthread.h
pwd.h
regex.h
sched.h
search.h
semaphore.h
spawn.h
strings.h
stropts.h
sys/ipc.h
sys/mman.h
sys/msg.h
sys/resource.h
sys/select.h
sys/sem.h
sys/shm.h
sys/socket.h
sys/stat.h
sys/statvfs.h
sys/time.h
sys/times.h
sys/types.h
sys/uio.h
sys/un.h
sys/utsname.h
sys/wait.h
syslog.h
tar.h
termios.h
trace.h
ulimit.h
unistd.h
utime.h
utmpx.h
wordexp.h
assert.h
complex.h
ctype.h
fenv.h
float.h
inttypes.h
iso646.h
limits.h
locale.h
math.h
setjmp.h
signal.h
stdarg.h
stdbool.h
stddef.h
stdint.h
stdio.h
stdlib.h
string.h
tgmath.h
time.h
wchar.h
wctype.h
sys/acct.h
sys/debugreg.h
sys/epoll.h
sys/eventfd.h
sys/fcntl.h
sys/fsuid.h
sys/gmon.h
sys/gmon_out.h
sys/inotify.h
sys/io.h
sys/kd.h
sys/kdaemon.h
sys/klog.h
sys/mount.h
sys/mtio.h
sys/pci.h
sys/perm.h
sys/personality.h
sys/prctl.h
sys/profil.h
sys/ptrace.h
sys/quota.h
sys/raw.h
sys/reboot.h
sys/reg.h
sys/sem.h
sys/sendfile.h
sys/signal.h
sys/signalfd.h
sys/socketvar.h
sys/soundcard.h
sys/statvfs.h
sys/swap.h
sys/sysctl.h
sys/sysinfo.h
sys/termios.h
sys/timerfd.h
sys/ttychars.h
sys/ultrasound.h
sys/unistd.h
sys/ustat.h
sys/vfs.h
sys/vlimit.h
sys/vt.h
sys/vtimes.h
sys/xattr.h
errno.h
mathcalls.h
net/ethernet.h
net/if_arp.h
net/if_ppp.h
net/if_slip.h
net/ppp_defs.h
net/if.h
net/if_packet.h
net/if_shaper.h
net/ppp-comp.h
net/route.h
netinet/ether.h
netinet/if_fddi.h
netinet/in.h	
netinet/ip6.h
netinet/udp.h
netinet/icmp6.h
netinet/if_tr.h
netinet/in_systm.h
netinet/ip_icmp.h
netinet/if_ether.h
netinet/igmp.h
netinet/ip.h
netinet/tcp.h
rpc/types.h
rpc/auth.h
rpc/des_crypt.h
rpc/pmap_prot.h
rpc/rpc_msg.h
rpc/xdr.h
rpc/auth_des.h
rpc/key_prot.h
rpc/pmap_rmt.h
rpc/svc.h
rpc/auth_unix.h
rpc/netdb.h
rpc/rpc.h
rpc/svc_auth.h
rpc/clnt.h
rpc/pmap_clnt.h
rpc/rpc_des.h
rpcsvc/bootparam.h
rpcsvc/nis_callback.h
rpcsvc/yp_prot.h
rpcsvc/rstat.h
rpcsvc/rusers.h
rpcsvc/spray.h
rpcsvc/ypupd.h
rpcsvc/mount.h
rpcsvc/nis.h
protocols/routed.h
protocols/rwhod.h
protocols/talkd.h
protocols/timed.h
arpa/ftp.h
arpa/inet.h
arpa/nameser.h
arpa/nameser_compat.h
arpa/telnet.h
arpa/tftp.h

-I/linux/include
-I/linux/include/sys
-I/linux/gcc/include
-I/linux/x86_64-redhat-linux5E/include
-I/linux/x86_64-redhat-linux5E/include/sys
-D_X86_
-D__STDC__
-D_GNU_SOURCE
-D__WORDSIZE=64
-D__builtin_va_list=void *
-D__DO_NOT_DEFINE_COMPILE
-D_Complex
-D_WCHAR_T
-D__NO_STRING_INLINES
-D__signed__
-D__extension__=""
-D_Bool="bool"
-D__GLIBC_HAVE_LONG_LONG=1
-D__need_sigset_t
-Daligned_u64=uint64_t
