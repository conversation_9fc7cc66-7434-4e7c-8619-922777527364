From SRS0=aO/p=ON=bag.python.org=<EMAIL>  Fri Nov 26 21:40:36 2004
X-VM-v5-Data: ([nil nil nil nil nil nil nil nil nil]
	[nil nil nil nil nil nil nil "MAILER DAEMON <>" "MAILER DAEMON <>" nil nil "Banned file: auto__mail.python.bat in mail from you" "^From:" nil nil nil nil "Banned file: auto__mail.python.bat in mail from you" nil nil nil nil nil nil nil]
	nil)
MIME-Version: 1.0
Message-Id: <<EMAIL>>
Content-Type: multipart/report; report-type=delivery-status;
    charset=utf-8;
    boundary="----------=_1101526904-1956-5"
X-Virus-Scanned: by XS4ALL Virus Scanner
X-UIDL: 4\G!!!<c"!UV["!M7C!!
From: MAILER DAEMON <>
To: <<EMAIL>>
Subject: Banned file: auto__mail.python.bat in mail from you
Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

This is a multi-part message in MIME format...

------------=_1101526904-1956-5
Content-Type: text/plain; charset="utf-8"
Content-Disposition: inline
Content-Transfer-Encoding: 7bit

BANNED FILENAME ALERT

Your message to: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>
was blocked by our Spam Firewall. The email you sent with the following subject has NOT BEEN DELIVERED:

Subject: Delivery_failure_notice

An attachment in that mail was of a file type that the Spam Firewall is set to block.



------------=_1101526904-1956-5
Content-Type: message/delivery-status
Content-Disposition: inline
Content-Transfer-Encoding: 7bit
Content-Description: Delivery error report

Reporting-MTA: dns; sacspam01.dot.ca.gov
Received-From-MTA: smtp; sacspam01.dot.ca.gov ([127.0.0.1])
Arrival-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

Final-Recipient: rfc822; <EMAIL>
Action: failed
Status: 5.7.1
Diagnostic-Code: smtp; 550 5.7.1 Message content rejected, id=01956-02-2 - BANNED: auto__mail.python.bat
Last-Attempt-Date: Fri, 26 Nov 2004 19:41:44 -0800 (PST)

------------=_1101526904-1956-5
Content-Type: text/rfc822-headers
Content-Disposition: inline
Content-Transfer-Encoding: 7bit
Content-Description: Undelivered-message headers

Received: from kgsav.org (ppp-70-242-162-63.dsl.spfdmo.swbell.net [*************])
	by sacspam01.dot.ca.gov (Spam Firewall) with SMTP
	id A232AD03DE3A; Fri, 26 Nov 2004 19:41:35 -0800 (PST)
From: <EMAIL>
To: <EMAIL>
Date: Sat, 27 Nov 2004 03:35:30 UTC
Subject: Delivery_failure_notice
Importance: Normal
X-Priority: 3 (Normal)
X-MSMail-Priority: Normal
Message-ID: <<EMAIL>>
MIME-Version: 1.0
Content-Type: multipart/mixed; boundary="====67bd2b7a5.f99f7"
Content-Transfer-Encoding: 7bit

------------=_1101526904-1956-5--

